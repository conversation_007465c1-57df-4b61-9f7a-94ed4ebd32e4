✔ Working on filament dashboard Page @done(24-04-18 22:06)
✔ Handle Notifications (SMS and Database) @done(24-04-20 01:06)
    ✔ Send Notification when User Created or Attatched to a HealthCenter @done(24-04-20 01:06)
    ✔ Send Notification to the Companion when Attatched to case @done(24-04-20 01:06)
    ✔ Send Notification to companion when a case is closed or open @done(24-04-20 01:07)
    ✔ Send Notification to APSs whenever Relapse delected @done(24-04-20 01:29)
    ✔ Notify when user responsibility changed for the health center @done(24-04-20 07:36)
    ✔ Notify when user dettached from a health center @done(24-04-20 07:36)
    ✔ Companion crud notification @done(24-04-20 07:36)
    ✔ Add advices table for table @done(24-04-26 12:44)
    ✔ Instace relations API (Diagnostic, Treatments, Companion, followups etc) @done(24-04-26 22:14)
        ✔ Diagnostic @done(24-04-26 15:04)
        ✔ Treatments @done(24-04-26 22:13)
        ✔ Companions @done(24-04-26 23:18)
        ✔ Relapse @done(24-04-27 01:39)
        ✔ followups @done(24-04-27 01:39)
    ✔ Working on companion APIS @done(24-04-27 16:06)
    ✔ Working notifications API @done(24-04-27 16:34)
    ✔ Add search API on Instance @done(24-04-28 12:33)
    ✔ Record all User activities (activities table) @done(24-04-28 19:19)
    ✔ Reset Password Send Notification API @done(24-04-28 19:19)
    ✔ Add backup system @done(24-05-02 00:06)
    ✔ Working on deployment scripts (Ansible) @done(24-05-06 23:11)
        ✔ Write scripts for deployment @done(24-05-06 23:11)
    ✔ Working on SMS @done(24-05-06 23:11)
    ✔ Working on mobile @done(24-08-09 23:11)
        ✔ Splash screen @done(24-05-07 10:04)
        ✔ Improve Icon for flutter @done(24-05-07 13:52)
        ✔ Advice Page (Default Page - Home Page) @done(24-05-19 01:43)
            ✔ domain @done(24-05-13 16:15)
            ✔ presentation @done(24-05-13 16:15)
            ✔ datasource remote @done(24-05-13 16:15)

        ✔ Authentication feature @done(24-05-19 01:43)
            ✔ domain @done(24-05-13 16:16)
            ✔ presentation @done(24-05-13 16:16)
            ✔ datasource remote @done(24-05-13 16:17)
            ✔ store token local and user object to objectbox (datasource local) @done(24-05-15 15:09)
            ✔ Authentication guards @done(24-05-15 15:09)

        ✔ User detail page @done(24-05-23 01:42)

        ✔ Health center domain @done(24-05-19 01:44)
            ✔ load healthcenter @done(24-05-19 01:44)
            ✔ create health center detail page @done(24-05-19 01:45)
            ✔ implement health center data layer @done(24-05-19 01:45)

        ✔ Implement companion domain @done(24-05-25 03:31)
            ✔ Companion list page @done(24-05-23 22:09)
            ✔ Companion detail @done(24-05-23 22:12)
            ✔ Companion relase @done(24-05-23 22:12)
            ✔ Follow up page @done(24-05-25 03:31)

        ✔ Implement Instance domain @done(24-05-25 20:37)
            ✔ Instance list (grouped by instance and relapsed) @done(24-05-25 20:36)
            ✔ Create Instance usecase and page @done(24-05-25 20:37)
            ✔ Delete instance @done(24-05-25 20:37)
        
        ✔ Implement seach query on paginated bloc and domain... @done(24-05-25 20:37)

        ✔ User Notifications @done(24-05-25 21:53)
            ✔ create usecases @done(24-05-25 20:36)
            ✔ notification page @done(24-05-25 20:36)
            ✔ fetch unread notification periodically @done(24-05-25 20:36)
            ✔ Implement backgound process fetch and show notification even when app is closed @done(24-05-31 14:30)
            ✔ Display red poul on notification icon appbar when found unread notification @done(24-05-31 14:30)
        
        ✔ Implement Health center members (APS and Companion) @done(24-05-29 12:14)

        ✔ Instance items @done(24-08-09 23:11)
            ✔ Instance detail @done(24-06-06 23:01)
                ✔ close or open case @done(24-06-06 23:01)
                ✔ Signale relapse @done(24-06-06 23:01)
            ✔ diagniostics @done(24-06-09 02:06)
                ✔ view file @done(24-06-06 01:40)
                ✔ create diagniostic @done(24-06-09 02:06)
                ✔ edit diagniostic @done(24-06-09 02:06)
            ✔ treatments @done(24-08-09 23:11)
            ✔ companions @done(24-08-09 23:11)
            ✔ followups @done(24-08-09 23:11)

        ✔ Add DIO or http client cache (return cached data when offline) @done(24-05-30 07:23)
            ✔ fix some issue with @done(24-08-09 23:11)
        
        [] Optional features
            [] All instance page (when user belongs to many healthCenter or user is root)


---------------------- V2 ------------------

[] Mobile
    ✔ Support de dictionnaire suggestion keyboard @done(25-06-08 10:31)
    ✔ Fix Validator which doesn't work properly (Companion followup form) @done(25-06-08 14:09)
    [] Notify APS when there is a followup, and the followup and APS are in the same health center
    [] Offline (Support) cases creation, followup creation
