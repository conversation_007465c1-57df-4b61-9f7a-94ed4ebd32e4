{"private": true, "type": "module", "scripts": {"dev": "concurrently \"pnpm vite\" \"pnpm laravel-serve\"", "laravel-serve": "php artisan serve --host 0.0.0.0", "build": "vite build", "pint": "./vendor/bin/pint", "phpstan": "./vendor/bin/phpstan analyse"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "laravel-vite-plugin": "^1.0.5", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "vite": "^5.4.9"}, "dependencies": {"preline": "^2.5.1"}}