<?php

use App\Http\Controllers\GuestController;
use App\Http\Controllers\Report;
use Illuminate\Support\Facades\Route;

Route::get('/', [GuestController::class, 'index']);

Route::get('/login', function () {
    return redirect(route('filament.admin.auth.login'));
})->name('login');

Route::middleware('auth')->group(function () {

    Route::prefix('report')
        ->name('report.')
        ->middleware('auth.root')->group(function () {

            Route::get('/overview', Report\OverviewReportController::class)->name('overview');

            Route::get('/parameterized', Report\ParameterizedReportController::class)->name('parameterized');

            Route::get('/instance', Report\InstanceReportController::class)->name('instance');
        });
});
