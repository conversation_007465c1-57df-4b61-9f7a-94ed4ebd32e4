<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

Schedule::command('backup:clean', ['--disable-notifications'])->daily()->at('01:00');

Schedule::command('backup:run', ['--disable-notifications'])->daily()->at('01:30');

Schedule::command('app:activity-purge')->weekly()->mondays()->at('01:00');

Schedule::command('app:clear-treatment-observation-dashes')->daily();
