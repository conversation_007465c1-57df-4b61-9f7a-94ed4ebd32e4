<?php

namespace App\Listeners;

use App\Events\RecordActivityEvent;
use App\Models\Activity;

class RecordActivity
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(RecordActivityEvent $event): void
    {
        Activity::create([
            'model' => $event->model,
            'action' => $event->action,
            'data' => $event->data,
            'attributes' => $event->attributes,
            'user_id' => $event->user?->id ?? null,
        ]);
    }
}
