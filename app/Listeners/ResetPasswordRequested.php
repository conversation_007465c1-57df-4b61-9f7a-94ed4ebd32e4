<?php

namespace App\Listeners;

use App\Events\PasswordForgotten;

class ResetPasswordRequested
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PasswordForgotten $event): void
    {
        $event->user->notify(
            new \App\Notifications\ResetPasswordRequestCreated(
                token: $event->token,
                password: $event->password
            )
        );

        event(
            new \App\Events\RecordActivityEvent(
                user: $event->user,
                model: $event->user->getTable(),
                action: \App\Enums\ActivityActionType::PASSWORD_FORGOT,
                data: [],
                attributes: []
            )
        );
    }
}
