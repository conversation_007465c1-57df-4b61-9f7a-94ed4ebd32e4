<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Logout;

class LogSuccessfulLogout
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Logout $event): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: $event->user,
                action: \App\Enums\ActivityActionType::LOGOUT,
                model: $event->user->getTable(),
                data: [],
                attributes: []
            )
        );
    }
}
