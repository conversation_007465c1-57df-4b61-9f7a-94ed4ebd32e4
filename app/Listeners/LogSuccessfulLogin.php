<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Login;

class LogSuccessfulLogin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: $event->user,
                action: \App\Enums\ActivityActionType::LOGIN,
                model: $event->user->getTable(),
                data: [
                    'type' => 'WEB',
                    'at' => now()->toString(),
                ],
                attributes: []
            )
        );
    }
}
