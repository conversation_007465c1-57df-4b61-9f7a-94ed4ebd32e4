<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Registered;

class LogRegisteredUser
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: $event->user,
                action: \App\Enums\ActivityActionType::REGISTER,
                model: $event->user->getTable(),
                data: [],
                attributes: []
            )
        );
    }
}
