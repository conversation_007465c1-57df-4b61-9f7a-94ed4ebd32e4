<?php

namespace App\Listeners;

use Illuminate\Notifications\Events\NotificationSent;

class LogSentNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NotificationSent $event): void
    {
        $notification = null;

        if (method_exists($event->notification, 'toDatabase')) {
            $notification = $event->notification->toDatabase($event->notifiable);
        }

        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                action: \App\Enums\ActivityActionType::NOTIFICATION_SENT,
                model: 'notifications',
                data: [
                    'channel' => $event->channel,
                    'notifiable' => $event->notifiable->withoutRelations()->toArray(),
                    'notification' => $notification,
                ],
                attributes: []
            )
        );
    }
}
