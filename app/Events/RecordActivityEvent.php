<?php

namespace App\Events;

use App\Enums\ActivityActionType;
use Illuminate\Foundation\Events\Dispatchable;

class RecordActivityEvent
{
    use Dispatchable;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public readonly string $model,
        public readonly ActivityActionType $action,
        public readonly ?array $data,
        public readonly ?array $attributes,
        public readonly mixed $user = null
    ) {
        //
    }
}
