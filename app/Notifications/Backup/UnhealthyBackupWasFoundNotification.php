<?php

namespace App\Notifications\Backup;

use Illuminate\Bus\Queueable;
use NotificationChannels\Telegram\TelegramMessage;
use Spatie\Backup\Notifications\Notifications\UnhealthyBackupWasFoundNotification as BaseNotification;

class UnhealthyBackupWasFoundNotification extends BaseNotification
{
    use Queueable;

    public function toTelegram($notifiable)
    {
        $subject = trans('backup::notifications.unhealthy_backup_found_subject', ['application_name' => $this->applicationName()]);

        $message = TelegramMessage::create()
            ->to($notifiable->routeNotificationForTelegram())
            ->content("⚠️\n$subject\n")
            ->line(trans('backup::notifications.unhealthy_backup_found_body', ['application_name' => $this->applicationName(), 'disk_name' => $this->diskName()]))
            ->line($this->problemDescription());

        $this->backupDestinationProperties()->each(function ($value, $name) use ($message) {
            $message->line("{$name}: $value");
        });

        if ($this->failure()->wasUnexpected()) {
            $message
                ->line('Health check: '.$this->failure()->healthCheck()->name())
                ->line(trans('backup::notifications.exception_message', ['message' => $this->failure()->exception()->getMessage()]))
                ->line(trans('backup::notifications.exception_trace', ['trace' => $this->failure()->exception()->getTraceAsString()]));
        }

        return $message;
    }
}
