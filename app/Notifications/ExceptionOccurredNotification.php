<?php

namespace App\Notifications;

use App\NotificationChannels\Twilio\TwilioMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Throwable;

class ExceptionOccurredNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly Throwable $exception,
        public readonly string $url,
        public readonly ?string $userId = null,
        public readonly ?array $context = null
    ) {
        $this->afterCommit();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['twilio'];
    }

    /**
     * Get the twilio representation of the notification.
     */
    public function toTwilio($notifiable): TwilioMessage
    {
        $appName = config('app.name');
        $environment = config('app.env');
        $exceptionClass = class_basename($this->exception);
        $message = $this->exception->getMessage();
        $file = $this->exception->getFile();
        $line = $this->exception->getLine();

        // Truncate message if too long for SMS
        $maxMessageLength = 100;
        if (strlen($message) > $maxMessageLength) {
            $message = substr($message, 0, $maxMessageLength).'...';
        }

        // Truncate file path to show only relevant part
        $shortFile = str_replace(base_path(), '', $file);

        $body = "🚨 {$appName} ({$environment})\n";
        $body .= "Exception: {$exceptionClass}\n";
        $body .= "Message: {$message}\n";
        $body .= "File: {$shortFile}:{$line}\n";
        $body .= "URL: {$this->url}\n";

        if ($this->userId) {
            $body .= "User: {$this->userId}\n";
        }

        $body .= 'Time: '.now()->format('Y-m-d H:i:s');

        return (new TwilioMessage($body))->toASCII();
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'exception_class' => get_class($this->exception),
            'exception_message' => $this->exception->getMessage(),
            'exception_file' => $this->exception->getFile(),
            'exception_line' => $this->exception->getLine(),
            'url' => $this->url,
            'user_id' => $this->userId,
            'context' => $this->context,
            'occurred_at' => now()->toISOString(),
        ];
    }
}
