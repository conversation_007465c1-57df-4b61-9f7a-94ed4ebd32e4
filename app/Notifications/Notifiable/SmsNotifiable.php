<?php

namespace App\Notifications\Notifiable;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class SmsNotifiable extends Model
{
    use Notifiable;

    public function __construct(
        public readonly string $phone
    ) {
        // Don't call parent constructor to avoid database issues
    }

    /**
     * Route notifications for the Twilio channel.
     */
    public function routeNotificationForTwilio(): string
    {
        return $this->phone;
    }

    /**
     * Override to prevent database operations
     */
    public function withoutRelations(): static
    {
        return $this;
    }

    /**
     * Override to return empty array
     */
    public function toArray(): array
    {
        return [
            'phone' => $this->phone,
        ];
    }
}
