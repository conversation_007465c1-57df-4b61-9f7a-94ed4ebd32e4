<?php

namespace App\Filament\Auth;

use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;

class EditProfile extends \Filament\Pages\Auth\EditProfile
{
    protected function getPhoneFormComponent(): Component
    {
        return TextInput::make('phone')
            ->label(__('Numéro de téléphone'))
            ->rules(['phone'])
            ->required()
            ->maxLength(255)
            ->unique(ignoreRecord: true);
    }

    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        $this->getNameFormComponent(),
                        $this->getPhoneFormComponent(),
                        $this->getEmailFormComponent(),
                        $this->getPasswordFormComponent(),
                        $this->getPasswordConfirmationFormComponent(),
                    ])
                    ->operation('edit')
                    ->model($this->getUser())
                    ->statePath('data')
                    ->inlineLabel(! static::isSimple()),
            ),
        ];
    }
}
