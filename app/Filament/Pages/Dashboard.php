<?php

namespace App\Filament\Pages;

use App\Models\Health\HealthCenter;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    use BaseDashboard\Concerns\HasFiltersForm;

    public function filtersForm(Form $form): Form
    {
        $auth = auth()->user();

        if (! $auth->isRoot()) {
            return $form;
        }

        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Select::make('healthCenter')
                            ->native(false)
                            ->options(HealthCenter::all()->pluck('name', 'id'))
                            ->searchable()
                            ->label(__('Centre de Santé')),

                        DatePicker::make('startDate')
                            ->native(false)
                            ->placeholder('dd/mm/yyyy')
                            ->label(__('Date de début'))
                            ->maxDate(fn (Get $get) => $get('endDate') ?: now()),

                        DatePicker::make('endDate')
                            ->label(__('Date de fin'))
                            ->native(false)
                            ->placeholder('dd/mm/yyyy')
                            ->minDate(fn (Get $get) => $get('startDate') ?: null)
                            ->maxDate(now()),
                    ])
                    ->columns(3),
            ]);
    }
}
