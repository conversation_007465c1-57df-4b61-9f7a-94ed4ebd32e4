<?php

namespace App\Filament\Widgets;

use App\Models\Victim\Instance;
use Filament\Widgets\ChartWidget;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;

class InstancesChart extends ChartWidget
{
    protected static ?string $heading = 'Total cas';

    protected static ?int $sort = 2;

    protected function getType(): string
    {
        return 'line';
    }

    public static function canView(): bool
    {
        return auth()->user()->isRoot();
    }

    protected function getData(): array
    {
        $data = Trend::model(Instance::class)
            ->between(
                start: now()->subMonths(11),
                end: now(),
            )
            ->perMonth()
            ->count();

        return [
            'datasets' => [
                [
                    'label' => 'Cas',
                    'fill' => 'start',
                    'data' => $data->map(fn (TrendValue $value) => $value->aggregate),
                ],
            ],
            'labels' => $data->map(fn (TrendValue $value) => $value->date),
        ];
    }
}
