<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\InstanceResource;
use App\Models\Victim\Instance;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class LatestInstances extends BaseWidget
{
    protected static ?string $heading = 'Derniers Cas';

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 2;

    public static function canView(): bool
    {
        return auth()->user()->isRoot();
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Instance::query())
            ->defaultPaginationPageOption(5)
            ->defaultSort('created_at', 'desc')
            ->paginated([10, 25, 50, 100])
            ->columns([
                Tables\Columns\TextColumn::make('healthCenter.name')
                    ->label(__('Centre de santé'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('survivor.code')
                    ->label(__('Code Survivant'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('code')
                    ->searchable(),

                Tables\Columns\BadgeColumn::make('type')
                    ->sortable(),

                Tables\Columns\IconColumn::make('status'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Ajouté le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),
            ])
            ->actions([
                Tables\Actions\Action::make('open')
                    ->label(__('Ouvrir'))
                    ->url(fn (Instance $record): string => InstanceResource::getUrl('edit', ['record' => $record])),
            ]);
    }
}
