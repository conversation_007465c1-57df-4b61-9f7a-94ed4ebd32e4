<?php

namespace App\Filament\Resources\HealthZoneResource\RelationManagers;

use App\Filament\Resources\HealthCenterResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class HealthCentersRelationManager extends RelationManager
{
    protected static string $relationship = 'healthCenters';

    protected static ?string $modelLabel = 'Centre de santé';

    protected static ?string $title = 'Centres de santé';

    public function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Centre de santé'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('address')
                    ->label(__("L'adresse"))
                    ->limit(50)
                    ->searchable(),

                Tables\Columns\TextColumn::make('phone')
                    ->label(__('Numéro de téléphone'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable(),
            ])
            ->filters([])
            ->headerActions([
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(function (Tables\Actions\ViewAction $action) {
                        $record = $action->getRecord();

                        return HealthCenterResource::getUrl('edit', ['record' => $record->id]);
                    }),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ]);
    }
}
