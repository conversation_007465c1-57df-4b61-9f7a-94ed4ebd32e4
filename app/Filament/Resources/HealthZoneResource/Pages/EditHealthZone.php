<?php

namespace App\Filament\Resources\HealthZoneResource\Pages;

use App\Filament\Resources\HealthZoneResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditHealthZone extends EditRecord
{
    protected static string $resource = HealthZoneResource::class;

    protected function getHeaderActions(): array
    {
        /**
         * @var \App\Models\Health\HealthZone
         */
        $record = $this->getRecord();

        if ($record->healthCenters()->count() > 0) {
            return [
                Actions\ViewAction::make(),
                Actions\RestoreAction::make(),
            ];
        }

        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
