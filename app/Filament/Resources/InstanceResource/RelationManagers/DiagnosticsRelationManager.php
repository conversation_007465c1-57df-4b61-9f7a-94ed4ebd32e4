<?php

namespace App\Filament\Resources\InstanceResource\RelationManagers;

use App\Enums;
use App\Models\Questionnaire;
use App\Validator\Attachment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DiagnosticsRelationManager extends RelationManager
{
    protected static string $relationship = 'diagnostics';

    protected static ?string $modelLabel = 'Diagnostique';

    protected static ?string $title = 'Diagnostiques';

    public function form(Form $form): Form
    {
        [$allQuestionnaires, $questionnaires] = once(function () {
            /**
             * @var \App\Models\Victim\Instance
             */
            $instance = $this->getOwnerRecord();
            $diagnostics = $instance->diagnostics->pluck('questionnaire_id')->all();

            $allQuestionnaires = Questionnaire::pluck('question', 'id');

            return [
                $allQuestionnaires->all(),
                $allQuestionnaires->filter(fn ($v, $k) => ! in_array($k, $diagnostics))->all(),
            ];
        });

        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Select::make('questionnaire_id')
                    ->label(__('Question'))
                    ->options($allQuestionnaires)
                    ->native(false)
                    ->visibleOn('edit')
                    ->disabled()
                    ->live()
                    ->required(),

                Forms\Components\Select::make('questionnaire_id')
                    ->label(__('Question'))
                    ->options($questionnaires)
                    ->visibleOn('create')
                    ->native(false)
                    ->searchable()
                    ->live()
                    ->required(),

                Forms\Components\TextInput::make('response')
                    ->hidden($this->hiddenField(Enums\QuestionnaireResponseType::TEXT))
                    ->hint(__('Entrez le texte'))
                    ->autocomplete(false)
                    ->required(),

                Forms\Components\TextInput::make('response')
                    ->hidden($this->hiddenField(Enums\QuestionnaireResponseType::NUMBER))
                    ->hint(__('Entrez le nombre'))
                    ->numeric()
                    ->autocomplete(false)
                    ->required(),

                Forms\Components\ToggleButtons::make('response')
                    ->hidden($this->hiddenField(Enums\QuestionnaireResponseType::BOOLEAN))
                    ->inline()
                    ->options([
                        'Oui' => __('Oui'),
                        'Non' => __('Non'),
                    ])
                    ->required(),

                Forms\Components\ToggleButtons::make('response')
                    ->hidden($this->hiddenField(Enums\QuestionnaireResponseType::CHOICES))
                    ->hint(__("Choisissez parmi l'une des réponses suivantes"))
                    ->options(function (Get $get) {
                        if ($get('questionnaire_id') === null) {
                            return [];
                        }

                        $questionnaire = Questionnaire::find($get('questionnaire_id'));

                        return $questionnaire->choices->pluck('choice', 'choice')->all();
                    })
                    ->required(),

                Forms\Components\DatePicker::make('response')
                    ->hidden($this->hiddenField(Enums\QuestionnaireResponseType::DATE))
                    ->hint(__('Entrez la date'))
                    ->format('d/m/Y')
                    ->native(false)
                    ->required(),

                Forms\Components\FileUpload::make('response')
                    ->hidden($this->hiddenField(Enums\QuestionnaireResponseType::ATTACHMENT))
                    ->disk('public')
                    ->downloadable()
                    ->moveFile()
                    ->directory(Attachment::DIAGNOSTIC_UPLOAD_DIRECTORY)
                    ->acceptedFileTypes(Attachment::ATTACHMENT_MIME_TYPE)
                    ->required(),
            ]);
    }

    public function hiddenField(Enums\QuestionnaireResponseType $type)
    {
        return function (Get $get) use ($type) {
            if ($get('questionnaire_id') === null) {
                return true;
            }
            $questionnaire = Questionnaire::find($get('questionnaire_id'));

            return $questionnaire->type !== $type;
        };
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('questionnaire.question')
            ->modifyQueryUsing(fn (Builder $query) => $query->with('questionnaire'))
            ->columns([
                Tables\Columns\TextColumn::make('questionnaire.question')
                    ->label(__('Question')),

                Tables\Columns\TextColumn::make('questionnaire.type')
                    ->badge()
                    ->label(__('Format')),

                Tables\Columns\ViewColumn::make('response')
                    ->label(__('Réponse'))
                    ->view('filament.tables.columns.diagnostic-response'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Ajouté le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Modifié le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->createAnother(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->defaultSort('id', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
