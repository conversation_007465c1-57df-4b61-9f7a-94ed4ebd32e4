<?php

namespace App\Filament\Resources\InstanceResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class FollowupsRelationManager extends RelationManager
{
    protected static string $relationship = 'followups';

    protected static ?string $modelLabel = 'Suivi';

    protected static ?string $title = 'Suivis';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('title')
                ->label(__('Titre'))
                ->disabled()
                ->required(),

            Forms\Components\Textarea::make('description')
                ->label(__('Description'))
                ->disabled()
                ->rows(7)
                ->columnSpanFull()
                ->required(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->modifyQueryUsing(fn (Builder $query) => $query->with('companion.user'))
            ->columns([
                Tables\Columns\TextColumn::make('companion.user.name')
                    ->label(__("Nom D'accompagnant"))
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('companion.type')
                    ->label(__("Type d'accompagnant"))
                    ->badge(),

                Tables\Columns\TextColumn::make('companion.user.phone')
                    ->label(__('Numéro de téléphone'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('title')
                    ->width(200),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Ajouté le'))
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([])
            ->headerActions([])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
