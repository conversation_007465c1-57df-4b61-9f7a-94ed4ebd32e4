<?php

namespace App\Filament\Resources\InstanceResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class RelapseRelationManager extends RelationManager
{
    protected static string $relationship = 'relapse';

    protected static ?string $modelLabel = 'Rechute';

    protected static ?string $title = 'Rechute';

    public function form(Form $form): Form
    {

        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Textarea::make('description')
                    ->required()
                    ->minLength(10)
                    ->rows(8)
                    ->label('Description'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('created_at')
            ->columns([
                Tables\Columns\IconColumn::make('signale')
                    ->default('warning')
                    ->icon('heroicon-o-exclamation-circle')
                    ->color('warning'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Signalé le'))
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->hidden(fn () => $this->getOwnerRecord()->relapse()->exists())
                    ->label(__('Signaler une rechute')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getBadgeColor(Model $ownerRecord, string $pageClass): ?string
    {
        return 'warning';
    }

    public static function getBadge(Model $ownerRecord, string $pageClass): ?string
    {
        return $ownerRecord->relapse()->exists() ? 'Signalé' : null;
    }

    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
