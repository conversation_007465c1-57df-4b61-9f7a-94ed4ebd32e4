<?php

namespace App\Filament\Resources\InstanceResource\RelationManagers;

use App\Enums;
use App\Validator\Attachment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class TreatmentsRelationManager extends RelationManager
{
    protected static string $relationship = 'treatments';

    protected static ?string $modelLabel = 'Prise en charge';

    protected static ?string $title = 'Prises en charge';

    public function form(Form $form): Form
    {
        [$AllTypes, $types] = once(function () {
            /**
             * @var \App\Models\Victim\Instance
             */
            $owner = $this->getOwnerRecord();
            $types = $owner->treatments->pluck('type');

            $toArray = fn (Enums\TreatmentType $type) => [$type->value => $type->getLabel()];

            $collection = collect(Enums\TreatmentType::cases());

            return [
                $collection->mapWithKeys($toArray)->toArray(),

                $collection->filter(fn (Enums\TreatmentType $type) => ! $types->containsStrict($type))
                    ->mapWithKeys($toArray)
                    ->toArray(),
            ];
        });

        return $form
            ->columns(1)
            ->schema([
                Forms\Components\ToggleButtons::make('type')
                    ->inline()
                    ->required()
                    ->disabled()
                    ->visibleOn('edit')
                    ->options($AllTypes),

                Forms\Components\ToggleButtons::make('type')
                    ->inline()
                    ->required()
                    ->visibleOn('create')
                    ->disabledOn('edit')
                    ->options($types),

                Forms\Components\Textarea::make('observation')
                    ->required()
                    ->minLength(5)
                    ->rows(5)
                    ->columnSpanFull()
                    ->label('Observation'),

                Forms\Components\FileUpload::make('attachment')
                    ->disk('public')
                    ->downloadable()
                    ->moveFile()
                    ->directory(Attachment::TREATMENT_UPLOAD_DIRECTORY)
                    ->acceptedFileTypes(Attachment::ATTACHMENT_MIME_TYPE)
                    ->nullable(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('type')
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->badge(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Ajouté le'))
                    ->sortable()
                    ->toggleable(),

            ])
            ->filters([])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
