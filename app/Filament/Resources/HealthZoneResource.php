<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HealthZoneResource\Pages;
use App\Filament\Resources\HealthZoneResource\RelationManagers;
use App\Models\Health\HealthZone;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HealthZoneResource extends Resource
{
    protected static ?string $model = HealthZone::class;

    protected static ?int $navigationSort = 0;

    protected static ?string $navigationGroup = 'Santé';

    protected static ?string $navigationLabel = 'Zones de santé';

    protected static ?string $modelLabel = 'Zone de santé';

    protected static ?string $pluralModelLabel = 'Zones de santé';

    protected static ?string $navigationIcon = 'heroicon-o-cursor-arrow-rays';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('Nom'))
                            ->unique(ignoreRecord: true)
                            ->columnSpanFull()
                            ->required(),

                        Forms\Components\TextInput::make('province')
                            ->label(__('Province'))
                            ->required(),

                        Forms\Components\TextInput::make('population_served')
                            ->label(__('Population'))
                            ->required()
                            ->numeric(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?HealthZone $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label(__('Créé le'))
                            ->content(fn (HealthZone $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label(__('Mis à jour le'))
                            ->content(fn (HealthZone $record): ?string => $record->updated_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('health_centers_count')
                            ->label(__('Centres de santé'))
                            ->content(fn (HealthZone $record): ?int => $record->healthCenters()->count()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?HealthZone $record) => $record === null),
            ])
            ->columns(['sm' => 3, 'lg' => null]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Nom'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('health_centers_count')
                    ->counts('healthCenters')
                    ->label(__('Centres de santé'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('population_served')
                    ->label(__('Population servie'))
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Supprimé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Mis à jour le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\HealthCentersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHealthZones::route('/'),
            'create' => Pages\CreateHealthZone::route('/create'),
            'view' => Pages\ViewHealthZone::route('/{record}'),
            'edit' => Pages\EditHealthZone::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
