<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RelapseResource\Pages;
use App\Models\Victim\Instance;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RelapseResource extends Resource
{
    protected static ?string $model = Instance::class;

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-trending-down';

    protected static ?string $navigationGroup = 'Santé';

    protected static ?string $navigationLabel = 'Rechutes';

    protected static ?string $modelLabel = 'Rechute';

    protected static ?string $pluralModelLabel = 'Rechutes';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('relapse.created_at')
                    ->label(__('Signalé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('survivor.code')
                    ->label(__('Code Survivant'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('code')
                    ->label(__('Code Cas'))
                    ->searchable(),

            ])
            ->filters([])
            ->defaultSort('relapse.created_at', 'desc')
            ->paginated([10, 25, 50, 100])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(function (Tables\Actions\ViewAction $action) {
                        $record = $action->getRecord();

                        return InstanceResource::getUrl('edit', [
                            'record' => $record->id,
                            'activeRelationManager' => 3,
                        ]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        /**
         * @var \App\Models\User $user
         */
        $user = auth()->user();

        if ($user->isAPS()) {
            $ids = $user->healthCenters()->pluck('health_centers.id')->toArray();

            return parent::getEloquentQuery()
                ->with(['survivor', 'relapse'])
                ->has('relapse')
                ->whereIn('health_center_id', $ids);
        }

        return parent::getEloquentQuery()
            ->with(['survivor', 'relapse'])
            ->has('relapse')
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRelapses::route('/'),
        ];
    }
}
