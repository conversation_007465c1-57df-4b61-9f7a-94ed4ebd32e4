<?php

namespace App\Filament\Resources\HealthCenterResource\Widgets;

use App\Enums\UserRole;
use App\Models\Health\HealthCenter;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class HealthCenterOverview extends BaseWidget
{
    public ?HealthCenter $record = null;

    protected function getStats(): array
    {
        $instances = $this->record?->instances()->count();
        $aps = $this->record?->users()->where('role', UserRole::APS)->count();
        $companions = $this->record?->users()->where('role', UserRole::COMPANION)->count();
        $relapses = $this->record?->relapses()->count();

        return [
            Card::make(__('Cas'), $instances),
            Card::make(__('APS'), $aps),
            Card::make(__('Accompagnants'), $companions),
            Card::make(__('Rechutes'), $relapses),
        ];
    }
}
