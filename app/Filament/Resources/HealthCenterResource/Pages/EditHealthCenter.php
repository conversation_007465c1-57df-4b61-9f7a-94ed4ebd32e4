<?php

namespace App\Filament\Resources\HealthCenterResource\Pages;

use App\Filament\Resources\HealthCenterResource;
use App\Filament\Resources\HealthCenterResource\Widgets\HealthCenterOverview;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditHealthCenter extends EditRecord
{
    protected static string $resource = HealthCenterResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    public function getFormActions(): array
    {
        return $this->hasAccess() ? [
            $this->getSaveFormAction(),
            $this->getCancelFormAction(),
        ] : [];
    }

    public function hasAccess()
    {
        return auth()->user()->isRoot();
    }

    public function getHeaderWidgets(): array
    {
        return [
            HealthCenterOverview::class,
        ];
    }
}
