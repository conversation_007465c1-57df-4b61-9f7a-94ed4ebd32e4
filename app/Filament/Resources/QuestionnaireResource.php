<?php

namespace App\Filament\Resources;

use App\Enums;
use App\Filament\Resources\QuestionnaireResource\Pages;
use App\Models\Questionnaire;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class QuestionnaireResource extends Resource
{
    protected static ?string $model = Questionnaire::class;

    protected static ?int $navigationSort = 5;

    protected static ?string $navigationGroup = 'Global';

    protected static ?string $navigationLabel = 'Questionnaires';

    protected static ?string $modelLabel = 'Questionnaire';

    protected static ?string $pluralModelLabel = 'Questionnaires';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('question')
                    ->label(__('Question'))
                    ->unique(ignoreRecord: true)
                    ->required(),

                Forms\Components\ToggleButtons::make('type')
                    ->inline()
                    ->live()
                    ->label(__('Format de la response'))
                    ->options(Enums\QuestionnaireResponseType::class)
                    ->required(),

                Forms\Components\TextInput::make('hint')
                    ->label(__('Indice'))
                    ->nullable(),

                Forms\Components\Repeater::make('choices')
                    ->relationship()
                    ->label(__('Choix Multiple'))
                    ->hidden(fn (Get $get) => $get('type') !== Enums\QuestionnaireResponseType::CHOICES->value)
                    ->schema([
                        Forms\Components\TextInput::make('choice')
                            ->required()
                            ->label(__('Choix')),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('question')
                    ->searchable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Mis à jour le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuestionnaires::route('/'),
            'create' => Pages\CreateQuestionnaire::route('/create'),
            'edit' => Pages\EditQuestionnaire::route('/{record}/edit'),
        ];
    }
}
