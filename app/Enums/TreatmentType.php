<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum TreatmentType: string implements <PERSON><PERSON><PERSON><PERSON>
{
    case MEDICAL_CARE = 'MEDICAL_CARE';

    case LEGAL_AND_JUDICIAL_CARE = 'LEGAL_AND_JUDICIAL_CARE';

    case PSYCH<PERSON>OGICAL_CARE = 'PSY<PERSON><PERSON>OGICAL_CARE';

    case SAFEHOUSE_CARE = 'SAFEHOUSE_CARE';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::MEDICAL_CARE => __('Médicale'),
            self::LEGAL_AND_JUDICIAL_CARE => __('Juridique et judiciaire'),
            self::PSYCHOLOGICAL_CARE => __('Psychologique'),
            self::SAFEHOUSE_CARE => __('Safehouse'),
        };
    }
}
