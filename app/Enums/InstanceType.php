<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum InstanceType: string implements HasColor, HasIcon, HasLabel
{
    case NEW = 'NEW';

    case RELAPSE = 'RELAPSE';

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::NEW => 'info',
            self::RELAPSE => 'warning'
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::NEW => 'heroicon-m-arrow-path',
            self::RELAPSE => 'heroicon-m-exclamation-circle'
        };
    }

    /**
     * @return string|null
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::NEW => __('Nouveau'),
            self::RELAPSE => __('Rechute')
        };
    }
}
