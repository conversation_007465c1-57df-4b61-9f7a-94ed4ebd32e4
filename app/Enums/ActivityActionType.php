<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum ActivityActionType: string implements <PERSON><PERSON>abe<PERSON>
{
    case LOGIN = 'LOGIN';

    case LOGOUT = 'LOGOUT';

    case REGISTER = 'REGISTER';

    case PASSWORD_RESET = 'PASSWORD_RESET';

    case PASSWORD_FORGOT = 'PASSWORD_FORGOT';

    case CREATE = 'CREATE';

    case UPDATE = 'UPDATE';

    case DELETE = 'DELETE';

    case RESTORE = 'RESTORE';

    case FORCE_DELETE = 'FORCE_DELETE';

    case NOTIFICATION_SENT = 'NOTIFICATION_SENT';

    case NOTIFICATION_FAILED = 'NOTIFICATION_FAILED';

    case BACKUP_SUCCESSFUL = 'BACKUP_SUCCESSFUL';

    case BACKUP_FAILED = 'BACKUP_FAILED';

    case UNKNOWN = 'UNKNOWN';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::LOGIN => __("S'est connecté"),
            self::LOGOUT => __("S'est déconnecté"),
            self::REGISTER => __("S'est inscrit"),
            self::PASSWORD_RESET => __('A réinitialisé son mot de passe'),
            self::PASSWORD_FORGOT => __('A oublié son mot de passe'),
            self::CREATE => __('A créé'),
            self::UPDATE => __('A modifié'),
            self::DELETE => __('A supprimé'),
            self::RESTORE => __('A restauré'),
            self::FORCE_DELETE => __('A définitivement supprimé'),
            self::NOTIFICATION_SENT => __('Notification envoyée'),
            self::NOTIFICATION_FAILED => __('Notification échouée'),
            self::BACKUP_SUCCESSFUL => __('Sauvegarde réussie'),
            self::BACKUP_FAILED => __('Sauvegarde échouée'),
            self::UNKNOWN => __('Inconnu')
        };
    }
}
