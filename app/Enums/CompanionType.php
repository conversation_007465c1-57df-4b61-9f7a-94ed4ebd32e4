<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum CompanionType: string implements HasLabel
{
    case CODESA = 'CODESA';

    case AVEC = 'AVEC';

    case RELIGIOUS_LEADER = 'RELIGIOUS_LEADER';

    /**
     * @return string|null
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::CODESA => __('Codesa'),
            self::AVEC => __('AVEC'),
            self::RELIGIOUS_LEADER => __('Leader religieux')
        };
    }
}
