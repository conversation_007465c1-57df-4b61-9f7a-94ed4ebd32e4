<?php

namespace App\Policies;

use App\Models\Advice;
use App\Models\User;

class AdvicePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Advice $advice): bool
    {
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isRoot();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Advice $advice): bool
    {
        return $user->isRoot();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Advice $advice): bool
    {
        return $user->isRoot();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Advice $advice): bool
    {
        return $user->isRoot();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Advice $advice): bool
    {
        return $user->isRoot();
    }
}
