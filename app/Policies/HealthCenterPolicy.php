<?php

namespace App\Policies;

use App\Models\Health\HealthCenter;
use App\Models\User;

class HealthCenterPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, HealthCenter $healthCenter): bool
    {
        return $user->isRoot() ||
            (
                $user->isAPS() && $user->healthCenters()->where('health_centers.id', $healthCenter->id)->exists()
            );
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isRoot();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, HealthCenter $healthCenter): bool
    {
        return $user->isRoot() ||
            (
                $user->isAPS() && $user->healthCenters()->where('health_centers.id', $healthCenter->id)->exists()
            );
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, HealthCenter $healthCenter): bool
    {
        return $user->isRoot();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, HealthCenter $healthCenter): bool
    {
        return $user->isRoot();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, HealthCenter $healthCenter): bool
    {
        return $user->isRoot();
    }
}
