<?php

namespace App\Services;

use App\Models\User;
use App\Notifications\ExceptionOccurredNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

class ExceptionNotificationService
{
    /**
     * Send SMS notification for exceptions to admin users
     */
    public function notifyException(Throwable $exception, ?Request $request = null): void
    {
        // Check if SMS notifications are enabled
        if (!$this->shouldSendNotification($exception)) {
            return;
        }

        // Check rate limiting to prevent spam
        if ($this->isRateLimited($exception)) {
            Log::info('Exception SMS notification rate limited', [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
            ]);
            return;
        }

        try {
            $url = $request ? $request->fullUrl() : 'N/A';
            $userId = auth()->id();
            $context = $this->buildContext($request);

            // Get admin users who should receive notifications
            $adminUsers = $this->getNotificationRecipients();

            if ($adminUsers->isEmpty()) {
                Log::warning('No admin users found to send exception SMS notifications');
                return;
            }

            // Send notification to each admin user
            foreach ($adminUsers as $user) {
                $user->notify(new ExceptionOccurredNotification(
                    exception: $exception,
                    url: $url,
                    userId: $userId,
                    context: $context
                ));
            }

            Log::info('Exception SMS notifications sent', [
                'exception' => get_class($exception),
                'recipients_count' => $adminUsers->count(),
            ]);

        } catch (Throwable $notificationException) {
            // Don't let notification failures break the application
            Log::error('Failed to send exception SMS notification', [
                'original_exception' => get_class($exception),
                'notification_exception' => $notificationException->getMessage(),
            ]);
        }
    }

    /**
     * Check if SMS notifications should be sent for this exception
     */
    private function shouldSendNotification(Throwable $exception): bool
    {
        // Check if SMS notifications are enabled
        if (!config('app.sms_exception_notifications_enabled', true)) {
            return false;
        }

        // Check environment restriction
        $allowedEnvironments = explode(',', config('app.sms_exception_notifications_environment', 'production'));
        if (!in_array(config('app.env'), $allowedEnvironments)) {
            return false;
        }

        // Skip certain exception types that shouldn't trigger SMS
        $skipExceptions = [
            \Illuminate\Auth\AuthenticationException::class,
            \Illuminate\Validation\ValidationException::class,
            \Symfony\Component\HttpKernel\Exception\NotFoundHttpException::class,
            \Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException::class,
            \Illuminate\Database\Eloquent\ModelNotFoundException::class,
        ];

        foreach ($skipExceptions as $skipException) {
            if ($exception instanceof $skipException) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if we're hitting rate limits for this exception type
     */
    private function isRateLimited(Throwable $exception): bool
    {
        $rateLimit = config('app.sms_exception_notifications_rate_limit', 5);
        $cacheKey = 'exception_sms_rate_limit:' . md5(get_class($exception) . $exception->getMessage());
        
        $count = Cache::get($cacheKey, 0);
        
        if ($count >= $rateLimit) {
            return true;
        }

        // Increment counter with 1 hour expiry
        Cache::put($cacheKey, $count + 1, now()->addHour());
        
        return false;
    }

    /**
     * Get users who should receive exception notifications
     */
    private function getNotificationRecipients()
    {
        return User::whereIn('role', ['ROOT', 'APS'])
            ->whereNotNull('phone')
            ->get();
    }

    /**
     * Build context information for the notification
     */
    private function buildContext(?Request $request): ?array
    {
        if (!$request) {
            return null;
        }

        return [
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
        ];
    }
}
