<?php

namespace App\Services;

use App\Notifications\ExceptionOccurredNotification;
use Illuminate\Http\Request;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

class ExceptionNotificationService
{
    /**
     * Send SMS notification for exceptions to admin users
     */
    public function notifyException(Throwable $exception, ?Request $request = null): void
    {
        // Check if SMS notifications are enabled
        if (! $this->shouldSendNotification($exception)) {
            return;
        }

        // Check rate limiting to prevent spam
        if ($this->isRateLimited($exception)) {
            Log::info('Exception SMS notification rate limited', [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
            ]);

            return;
        }

        try {
            $url = $request ? $request->fullUrl() : 'N/A';
            $userId = auth()->id();
            $context = $this->buildContext($request);

            // Get the configured phone number for notifications
            $notificationPhone = $this->getNotificationPhone();

            if (! $notificationPhone) {
                Log::info('No phone number configured for exception SMS notifications');

                return;
            }

            // Create a simple notifiable object with the phone number
            $notifiable = new class($notificationPhone)
            {
                use Notifiable;

                public function __construct(public string $phone) {}

                public function routeNotificationForTwilio(): string
                {
                    return $this->phone;
                }
            };

            // Send notification
            $notifiable->notify(new ExceptionOccurredNotification(
                exception: $exception,
                url: $url,
                userId: $userId,
                context: $context
            ));

            Log::info('Exception SMS notification sent', [
                'exception' => get_class($exception),
                'phone' => $notificationPhone,
            ]);

        } catch (Throwable $notificationException) {
            // Don't let notification failures break the application
            Log::error('Failed to send exception SMS notification', [
                'original_exception' => get_class($exception),
                'notification_exception' => $notificationException->getMessage(),
            ]);
        }
    }

    /**
     * Check if SMS notifications should be sent for this exception
     */
    private function shouldSendNotification(Throwable $exception): bool
    {
        // Check if phone number is configured
        if (! $this->getNotificationPhone()) {
            return false;
        }

        // Check environment restriction
        $allowedEnvironments = explode(',', config('app.sms_exception_notifications_environment', 'production'));
        if (! in_array(config('app.env'), $allowedEnvironments)) {
            return false;
        }

        // Skip certain exception types that shouldn't trigger SMS
        $skipExceptions = [
            \Illuminate\Auth\AuthenticationException::class,
            \Illuminate\Validation\ValidationException::class,
            \Symfony\Component\HttpKernel\Exception\NotFoundHttpException::class,
            \Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException::class,
            \Illuminate\Database\Eloquent\ModelNotFoundException::class,
        ];

        foreach ($skipExceptions as $skipException) {
            if ($exception instanceof $skipException) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if we're hitting rate limits for this exception type
     */
    private function isRateLimited(Throwable $exception): bool
    {
        $rateLimit = config('app.sms_exception_notifications_rate_limit', 5);
        $cacheKey = 'exception_sms_rate_limit:'.md5(get_class($exception).$exception->getMessage());

        $count = Cache::get($cacheKey, 0);

        if ($count >= $rateLimit) {
            return true;
        }

        // Increment counter with 1 hour expiry
        Cache::put($cacheKey, $count + 1, now()->addHour());

        return false;
    }

    /**
     * Get the configured phone number for exception notifications
     */
    private function getNotificationPhone(): ?string
    {
        $phone = config('app.sms_exception_notifications_phone');

        return ! empty($phone) ? $phone : null;
    }

    /**
     * Build context information for the notification
     */
    private function buildContext(?Request $request): ?array
    {
        if (! $request) {
            return null;
        }

        return [
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
        ];
    }
}
