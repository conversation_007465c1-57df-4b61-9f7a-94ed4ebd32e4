<?php

namespace App\Services;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class ActivityRecorder
{
    /**
     * Record a CREATE activity for a model
     */
    public static function recordCreate(Model $model, ?User $user = null): void
    {
        static::record(
            model: $model->getTable(),
            action: ActivityActionType::CREATE,
            data: $model->withoutRelations()->toArray(),
            attributes: [],
            user: $user ?? auth()->user()
        );
    }

    /**
     * Record an UPDATE activity for a model
     */
    public static function recordUpdate(Model $model, ?User $user = null): void
    {
        static::record(
            model: $model->getTable(),
            action: ActivityActionType::UPDATE,
            data: $model->withoutRelations()->toArray(),
            attributes: $model->getChanges(),
            user: $user ?? auth()->user()
        );
    }

    /**
     * Record a DELETE activity for a model
     */
    public static function recordDelete(Model $model, ?User $user = null): void
    {
        static::record(
            model: $model->getTable(),
            action: ActivityActionType::DELETE,
            data: $model->withoutRelations()->toArray(),
            attributes: [],
            user: $user ?? auth()->user()
        );
    }

    /**
     * Record a RESTORE activity for a model
     */
    public static function recordRestore(Model $model, ?User $user = null): void
    {
        static::record(
            model: $model->getTable(),
            action: ActivityActionType::RESTORE,
            data: $model->withoutRelations()->toArray(),
            attributes: [],
            user: $user ?? auth()->user()
        );
    }

    /**
     * Record a LOGIN activity
     */
    public static function recordLogin(User $user, array $data = []): void
    {
        static::record(
            model: $user->getTable(),
            action: ActivityActionType::LOGIN,
            data: array_merge([
                'type' => 'WEB',
                'at' => now()->toString(),
            ], $data),
            attributes: [],
            user: $user
        );
    }

    /**
     * Record a REGISTER activity
     */
    public static function recordRegister(User $user): void
    {
        static::record(
            model: $user->getTable(),
            action: ActivityActionType::REGISTER,
            data: [],
            attributes: [],
            user: $user
        );
    }

    /**
     * Record a custom activity
     */
    public static function recordCustom(
        string $model,
        ActivityActionType $action,
        array $data = [],
        array $attributes = [],
        ?User $user = null
    ): void {
        static::record(
            model: $model,
            action: $action,
            data: $data,
            attributes: $attributes,
            user: $user ?? auth()->user()
        );
    }

    /**
     * Core method to record any activity
     */
    private static function record(
        string $model,
        ActivityActionType $action,
        array $data,
        array $attributes,
        mixed $user
    ): void {
        event(
            new RecordActivityEvent(
                model: $model,
                action: $action,
                data: $data,
                attributes: $attributes,
                user: $user
            )
        );
    }
}
