<?php

namespace App\Providers;

use App\Models;
use App\Policies;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Laravel\Pulse\Facades\Pulse;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (app()->isProduction()) {
            \URL::forceScheme('https');
        }

        Model::automaticallyEagerLoadRelationships();

        Gate::policy(Models\Health\HealthCenter::class, Policies\HealthCenterPolicy::class);

        Gate::policy(Models\Health\HealthZone::class, Policies\HealthZonePolicy::class);

        Gate::policy(Models\Victim\Instance::class, Policies\InstancePolicy::class);

        Gate::define('viewPulse', function (Models\User $user) {
            return $user->isRoot();
        });

        Pulse::user(fn ($user) => [
            'name' => $user->name,
            'extra' => $user->phone,
        ]);

        Builder::macro('whereLike', function ($attributes, string $searchTerm) {
            $this->where(function (Builder $query) use ($attributes, $searchTerm) {
                foreach (Arr::wrap($attributes) as $attribute) {
                    $query->when(
                        str_contains($attribute, '.'),
                        function (Builder $query) use ($attribute, $searchTerm) {
                            [$relationName, $relationAttribute] = explode('.', $attribute);

                            $query->orWhereHas($relationName, function (Builder $query) use ($relationAttribute, $searchTerm) {
                                $query->where($relationAttribute, 'LIKE', "%{$searchTerm}%");
                            });
                        },
                        function (Builder $query) use ($attribute, $searchTerm) {
                            $query->orWhere($attribute, 'LIKE', "%{$searchTerm}%");
                        }
                    );
                }
            });

            return $this;
        });
    }
}
