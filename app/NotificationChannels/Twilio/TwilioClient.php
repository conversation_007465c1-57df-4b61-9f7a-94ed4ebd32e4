<?php

namespace App\NotificationChannels\Twilio;

use App\NotificationChannels\Twilio\Exceptions\CouldNotSendNotification;
use Twilio\Rest\Api\V2010\Account\MessageInstance;
use Twilio\Rest\Client;

class TwilioClient
{
    /**
     * @param  Client  $client
     */
    public function __construct(
        private readonly Client $twilio,
        private readonly string $twilioMessagingServiceSid,
    ) {}

    public function send(string $to, string $message)
    {
        $response = $this->twilio->messages->create($to, [
            'body' => $message,
            'messagingServiceSid' => $this->twilioMessagingServiceSid,
        ]);

        $this->handleProviderResponses($response);
    }

    /**
     * @param  array  $responses
     *
     * @throws CouldNotSendNotification
     */
    protected function handleProviderResponses(MessageInstance $response)
    {
        $errorCode = $response->errorCode;

        if ($errorCode !== null) {
            throw CouldNotSendNotification::serviceRespondedWithAnError(
                (string) $response->errorMessage,
                (int) $errorCode
            );
        }
    }
}
