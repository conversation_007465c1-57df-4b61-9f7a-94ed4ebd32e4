<?php

namespace App\NotificationChannels\Twilio;

class TwilioMessage
{
    /** @var bool */
    private $toASCII = false;

    /** @var string */
    private $to = null;

    /**
     * @param  string  $body
     * @return static
     */
    public static function create($body = '')
    {
        return new static($body);
    }

    public function __construct(protected string $body = '')
    {
        //
    }

    /**
     * @return $this
     */
    public function body(string $body)
    {
        $this->body = $body;

        return $this;
    }

    /**
     * @return $this
     */
    public function to(string $to)
    {
        $this->to = $to;

        return $this;
    }

    /**
     * @return $this
     */
    public function toASCII(bool $value = true)
    {
        $this->toASCII = $value;

        return $this;
    }

    public function getBody(): string
    {
        return $this->body;
    }

    public function getDestination(): ?string
    {
        return $this->to;
    }

    public function shouldConvertToASCII(): bool
    {
        return $this->toASCII;
    }
}
