<?php

namespace App\NotificationChannels\Twilio;

use App\NotificationChannels\Twilio\Exceptions\CouldNotSendNotification;
use Illuminate\Notifications\Notification;

class TwilioChannel
{
    /** @var TwilioClient */
    protected $twilio;

    public function __construct(TwilioClient $twilio)
    {
        $this->twilio = $twilio;
    }

    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws CouldNotSendNotification
     */
    public function send($notifiable, Notification $notification)
    {
        if (! $to = $notifiable->routeNotificationFor('twilio')) {
            return;
        }

        $message = $notification->toTwilio($notifiable);

        if (is_string($message)) {
            $message = new TwilioMessage($message);
        }

        $this->twilio->send(
            to: $message->getDestination() ?? $to,

            message: $message->shouldConvertToASCII() ? iconv('UTF-8', 'ASCII//TRANSLIT', $message->getBody()) : $message->getBody(),
        );
    }
}
