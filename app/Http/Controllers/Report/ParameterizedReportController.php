<?php

namespace App\Http\Controllers\Report;

use App\Exports;
use App\Http\Controllers\Controller;
use App\Models\Health\HealthCenter;
use App\Models\Victim\Instance;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;

use function Spatie\LaravelPdf\Support\pdf;

class ParameterizedReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        // Instance or relapse
        $type = $request->get('type', 'instance');

        // Format PDF / Excel
        $format = $request->get('format', 'excel');

        // health center ids
        $healthCenterIds = $request->get('healthCenters', []);
        $healthCenterIds = is_array($healthCenterIds) ? $healthCenterIds : [];

        // Dates filter
        $startDate = $request->get('startDate');
        $startDate = $startDate ? Carbon::parse($startDate) : null;

        $endDate = $request->get('endDate');
        $endDate = $endDate ? Carbon::parse($endDate) : null;

        // Instance query
        $query = Instance::query()->with([
            'survivor',
            'relapse',
            'followups',
            'treatments',
            'companions',
            'companions.user',
            'healthCenter',
            'healthCenter.healthZone',
        ]);

        // Health Center query
        $healthCenters = empty($healthCenterIds) ? HealthCenter::all() : HealthCenter::findMany($healthCenterIds);
        $healthCenterNames = $healthCenters->map(fn (HealthCenter $center) => $center->name)->toArray();

        // Record health centers count
        $healthCenters->map(function ($healthCenter) use ($startDate, $endDate) {
            $healthCenter->instances_count = $this->applyDateFilter(
                query: $healthCenter->instances(),
                startDate: $startDate,
                endDate: $endDate,
            )->count();

            $healthCenter->relapses_count = $this->applyDateFilter(
                query: $healthCenter->instances()->has('relapse'),
                startDate: $startDate,
                endDate: $endDate,
            )->count();

            return $healthCenter;
        });

        // Filter instance by $healthCenterIds
        if (filled($healthCenterIds)) {
            $query = $query->whereHas('healthCenter', fn (Builder $query) => $query->whereIn('id', $healthCenterIds));
        }

        // Only relapsed instances
        if ($type === 'relapse') {
            $query = $query->has('relapse');
        }

        // Apply dates filter
        $query = $this->applyDateFilter(
            query: $query,
            startDate: $startDate,
            endDate: $endDate,
        );

        $items = $query->get();

        $appname = config('app.name');
        $today = now()->format('d-m-Y');

        // Generate report in Excel
        if (strtoupper($format) !== 'PDF') {
            $export = new Exports\ParameterizedReportExport(instances: $items);

            return Excel::download($export, "$appname-report-parameterized-$today.xlsx");
        }

        // Generate report in PDF
        return pdf()
            ->margins(top: 32, right: 32, bottom: 32, left: 32, unit: 'px')
            ->view('reports.parameterized', [
                'healthCenterNames' => filled($healthCenterIds) ? $healthCenterNames : null,
                'healthCenters' => $healthCenters,
                'cases' => $items,
                'type' => $type,
                'startDate' => $startDate,
                'endDate' => $endDate,
            ])
            ->name("$appname-report-parameterized-$today.pdf");
    }

    private function applyDateFilter($query, ?Carbon $startDate, ?Carbon $endDate)
    {
        $table = (new Instance)->getTable();

        if (filled($startDate) && filled($endDate)) {
            $query->where(
                fn ($query) => $query
                    ->whereDate("$table.created_at", '>=', $startDate)
                    ->whereDate("$table.created_at", '<=', $endDate)
            );
        }

        if (filled($startDate) && blank($endDate)) {
            $query->where(
                fn ($query) => $query
                    ->whereDate("$table.created_at", '>=', $startDate)
                    ->whereDate("$table.created_at", '<=', now())
            );
        }

        if (blank($startDate) && filled($endDate)) {
            $query->whereDate("$table.created_at", '<=', $endDate);
        }

        return $query;
    }
}
