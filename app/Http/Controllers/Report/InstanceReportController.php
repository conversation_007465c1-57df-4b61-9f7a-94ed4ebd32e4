<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use App\Models\Victim\Instance;
use Illuminate\Http\Request;

use function Spatie\LaravelPdf\Support\pdf;

class InstanceReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $instanceId = $request->query('id');

        abort_if($instanceId === null, 403);

        $instance = Instance::with([
            'survivor',
            'relapse',
            'followups',
            'treatments',
            'companions',
            'companions.user',
            'healthCenter',
            'healthCenter.healthZone',
        ])->findOrFail($instanceId);

        $appname = config('app.name');
        $today = now()->format('d-m-Y');

        return pdf()
            ->margins(top: 32, right: 32, bottom: 32, left: 32, unit: 'px')
            ->view('reports.instance', ['case' => $instance])
            ->name("$appname-report-case-$today.pdf");
    }
}
