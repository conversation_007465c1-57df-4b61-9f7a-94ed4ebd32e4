<?php

namespace App\Http\Controllers;

use App\Models\AdviceLanguage;
use Illuminate\Http\Request;

class GuestController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function index(Request $request)
    {
        $lang = $request->query('lang');

        $advicesLangs = AdviceLanguage::all();

        $activeLang = $advicesLangs->first();

        foreach ($advicesLangs as $adviceLang) {
            if ($adviceLang->name === $lang) {
                $activeLang = $adviceLang;
            }
        }

        return view('welcome', [
            'languages' => $advicesLangs,
            'lang' => $activeLang,
            'advices' => $activeLang ? $activeLang->advices : [],
        ]);
    }
}
