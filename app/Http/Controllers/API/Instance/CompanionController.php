<?php

namespace App\Http\Controllers\API\Instance;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Http\Resources\Companion\CompanionCollection;
use App\Http\Resources\Companion\CompanionResource;
use App\Models\Victim\Companion;
use App\Models\Victim\Instance;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class CompanionController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-instance-healthCenter'),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Instance $instance)
    {
        return new CompanionCollection(
            $instance->companions()
                ->withTrashed()
                ->get()
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(Instance $instance, Companion $companion)
    {
        $companion = $instance->companions()
            ->withTrashed()
            ->where('companions.id', $companion->id)->firstOrFail();

        return new CompanionResource($companion);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Requests\CreateInstanceCompanionRequest $request, Instance $instance)
    {
        $data = $request->validated();

        $companion = $instance->companions()->create($data);

        return new CompanionResource($companion);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Requests\UpdateInstanceCompanionRequest $request, Instance $instance, Companion $companion)
    {
        $data = $request->validated();

        $companion->fill($data);
        $companion->save();

        $companion->refresh();

        return new CompanionResource($companion);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Instance $instance, Companion $companion)
    {
        $companion->forceDeleteQuietly();

        return [
            'message' => __('Accompagnant supprimé avec succès.'),
        ];
    }
}
