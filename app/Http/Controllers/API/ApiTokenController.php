<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\User\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class ApiTokenController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'phone' => 'required|phone',
            'password' => 'required',
            'device_name' => 'required',
        ]);

        $user = User::where('phone', $request->phone)->first();

        if (! $user || ! Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'phone' => [__('Les informations fournies sont incorrectes.')],
            ]);
        }

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: $user,
                model: $user->getTable(),
                action: \App\Enums\ActivityActionType::LOGIN,
                data: [
                    'type' => 'API_TOKEN',
                    'client_ip' => $request->ip(),
                    'at' => now()->toString(),
                ],
                attributes: [],
            )
        );

        return [
            'user' => (new UserResource($user))->toArray($request),
            'access_token' => $user->createToken($request->device_name)->plainTextToken,
        ];
    }
}
