<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\Notification\NotificationCollection;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function index(Request $request)
    {
        /**
         * @var \App\Models\User
         */
        $user = $request->user();

        $notifications = $user->notifications()->latest()->paginate(20);

        return new NotificationCollection($notifications);
    }

    public function unread(Request $request)
    {
        /**
         * @var \App\Models\User
         */
        $user = $request->user();

        $unreadNotifications = $user->unreadNotifications()->latest()->paginate(20);

        return new NotificationCollection($unreadNotifications);
    }

    public function markAllAsRead(Request $request)
    {
        /**
         * @var \App\Models\User
         */
        $user = $request->user();

        $user->unreadNotifications->markAsRead();

        return [
            'message' => __('Toutes les notifications ont été marquées comme lues'),
        ];
    }

    public function markAsRead(Request $request, string $notification)
    {
        /**
         * @var \App\Models\User
         */
        $user = $request->user();

        $user->unreadNotifications()->where('id', $notification)->update(['read_at' => now()]);

        return [
            'message' => __('La notification a été marquée comme lue'),
        ];
    }

    public function delete(Request $request, string $notification)
    {
        /**
         * @var \App\Models\User
         */
        $user = $request->user();

        $user->notifications()->where('id', $notification)->delete();

        return [
            'message' => __('La notification a été supprimée'),
        ];
    }

    public function deleteAll(Request $request)
    {
        /**
         * @var \App\Models\User
         */
        $user = $request->user();

        $user->notifications()->delete();

        return [
            'message' => __('Toutes les notifications ont été supprimées'),
        ];
    }
}
