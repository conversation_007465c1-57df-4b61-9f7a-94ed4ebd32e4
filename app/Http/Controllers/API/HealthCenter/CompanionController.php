<?php

namespace App\Http\Controllers\API\HealthCenter;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Http\Resources\User as UserResource;
use App\Models\Health\HealthCenter;
use App\Models\User;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Str;

class CompanionController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-healthCenter'),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(HealthCenter $healthCenter)
    {
        $companions = $healthCenter->companions()->get();

        return new UserResource\UserCollection($companions);
    }

    /**
     * Display the specified resource.
     */
    public function show(HealthCenter $healthCenter, User $user)
    {
        $user = $healthCenter->companions()->where('users.id', $user->id)->firstOrFail();

        return new UserResource\UserResource($user);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Requests\CreateCompanionRequest $request, HealthCenter $healthCenter)
    {
        $data = $request->validated();

        $createdUser = User::create([
            ...$data,
            'password' => Str::random(8),
        ]);

        $healthCenter->users()->attach($createdUser->id, [
            'responsibility' => $data['responsibility'],
        ]);

        $createdUser->companionRole()->updateOrCreate([], [
            'role' => $data['companion_role'],
        ]);

        $user = $healthCenter->users()->where('users.id', $createdUser->id)->firstOrFail();

        return new UserResource\UserResource($user);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Requests\UpdateCompanionRequest $request, HealthCenter $healthCenter, User $user)
    {
        $data = $request->validated();

        $user->fill($data)->save();

        $healthCenter->users()->updateExistingPivot($user->id, [
            'responsibility' => $data['responsibility'],
        ]);

        $user->companionRole()->updateOrCreate([], [
            'role' => $data['companion_role'],
        ]);

        $user->refresh();

        $user = $healthCenter->users()->where('users.id', $user->id)->firstOrFail();

        return new UserResource\UserResource($user);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HealthCenter $healthCenter, User $user)
    {
        $healthCenter->users()->detach($user->id);

        return ['message' => __("L'utilisateur Accompagnant a été supprimé avec succès.")];
    }
}
