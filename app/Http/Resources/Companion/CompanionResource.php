<?php

namespace App\Http\Resources\Companion;

use App\Http\Resources\Instance\InstanceShowResource;
use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'user' => $this->when(
                ! $request->user()->isCompanion() && $this->user !== null,
                fn () => (new UserResource($this->user))->toArray($request)
            ),
            'instance' => $this->when(
                $request->user()->isCompanion(),
                fn () => (new InstanceShowResource($this->instance))->toArray($request)
            ),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
