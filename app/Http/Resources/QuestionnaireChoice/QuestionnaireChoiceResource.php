<?php

namespace App\Http\Resources\QuestionnaireChoice;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuestionnaireChoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'questionnaire_id' => $this->questionnaire_id,
            'choice' => $this->choice,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
