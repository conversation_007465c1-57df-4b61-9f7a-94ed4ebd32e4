<?php

namespace App\Http\Resources\HealthZone;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HealthZoneResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'province' => $this->province,
            'population_served' => $this->population_served,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
