<?php

namespace App\Http\Resources\Survivor;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SurvivorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
