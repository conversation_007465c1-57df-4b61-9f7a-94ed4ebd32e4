<?php

namespace App\Http\Resources\Questionnaire;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuestionnaireResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'question' => $this->question,
            'type' => $this->type,
            'hint' => $this->hint,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
