<?php

namespace App\Http\Resources\Instance;

use App\Http\Resources\HealthCenter\HealthCenterResource;
use App\Http\Resources\Relapse\RelapseResource;
use App\Http\Resources\Survivor\SurvivorResource;
use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InstanceShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $survivor = $this->survivor;
        $healthCenter = $this->healthCenter;
        $relapse = $this->relapse;
        $creator = $this->creator;

        return [
            'id' => $this->id,
            'code' => $this->code,
            'type' => $this->type,
            'status' => $this->status,
            'relapse' => $this->when(
                $relapse !== null,
                fn () => (new RelapseResource($relapse))->toArray($request)
            ),
            'survivor' => (new SurvivorResource($survivor))->toArray($request),
            'health_center' => (new HealthCenterResource($healthCenter))->toArray($request),
            'creator' => $this->when(
                $creator !== null && $creator->user !== null,
                fn () => (new UserResource($creator->user))->toArray($request)
            ),
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
