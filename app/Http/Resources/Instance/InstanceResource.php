<?php

namespace App\Http\Resources\Instance;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InstanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $survivor = $this->survivor;
        $healthCenter = $this->healthCenter;
        $relapse = $this->relapse;

        return [
            'id' => $this->id,
            'code' => $this->code,
            'type' => $this->type,
            'status' => $this->status,
            'survivor_code' => $survivor->code,
            'relapsed_at' => $relapse?->created_at ?? null,
            'health_center_id' => $healthCenter->id,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
