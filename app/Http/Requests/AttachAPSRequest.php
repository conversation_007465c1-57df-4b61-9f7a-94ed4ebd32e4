<?php

namespace App\Http\Requests;

use App\Enums;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AttachAPSRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->user();

        return $user->isRoot() || $user->belongsToHealthCenter(
            healthCenter: $this->route('health_center'),
            responsibility: Enums\UserResponsibility::ADMINISTRATOR
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'responsibility' => [
                'required',
                Rule::enum(Enums\UserResponsibility::class)->only([
                    Enums\UserResponsibility::OPERATOR,
                    Enums\UserResponsibility::ADMINISTRATOR,
                ]),
            ],
        ];
    }
}
