<?php

namespace App\Http\Requests;

use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateDiagnosticRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $instance = $this->route('instance');

        return [
            'questionnaire_id' => [
                'required',
                'exists:questionnaires,id',
                Rule::unique('diagnostics')->where(fn (Builder $query) => $query->where('instance_id', $instance->id)),
            ],
            'response' => ['required'],
        ];
    }
}
