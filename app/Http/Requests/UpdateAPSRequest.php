<?php

namespace App\Http\Requests;

use App\Enums;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAPSRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->user();

        return $user->isRoot() || $user->belongsToHealthCenter(
            $this->route('health_center'),
            Enums\UserResponsibility::ADMINISTRATOR
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->route('user');

        return [
            'name' => ['required', 'string', 'min:3', 'max:255'],
            'phone' => [
                'required',
                'phone',
                Rule::unique('users')->ignore($user->id),
            ],
            'email' => [
                'nullable',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
            'role' => [
                'required',
                Rule::enum(Enums\UserRole::class)->only([Enums\UserRole::APS]),
            ],
            'responsibility' => [
                'required',
                Rule::enum(Enums\UserResponsibility::class)->only([
                    Enums\UserResponsibility::OPERATOR,
                    Enums\UserResponsibility::ADMINISTRATOR,
                ]),
            ],
            'description' => ['nullable', 'string'],
        ];
    }
}
