<?php

namespace App\Http\Requests;

use App\Enums;
use App\Validator\Attachment;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

class CreateTreatmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $instance = $this->route('instance');

        return [
            'type' => [
                'required',
                Rule::enum(Enums\TreatmentType::class),
                Rule::unique('treatments')->where(fn (Builder $query) => $query->where('instance_id', $instance->id)),
            ],
            'observation' => ['required', 'string', 'min:5'],
            'attachment' => [
                'nullable',
                File::types(Attachment::ATTACHMENT_MIME_TYPE)
                    ->min(5)
                    ->max(100 * 1024),
            ],
        ];
    }
}
