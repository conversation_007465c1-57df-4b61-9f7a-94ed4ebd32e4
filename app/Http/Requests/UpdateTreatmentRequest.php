<?php

namespace App\Http\Requests;

use App\Validator\Attachment;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\File;

class UpdateTreatmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'observation' => ['required', 'string', 'min:5'],
            'attachment' => [
                'nullable',
                File::types(Attachment::ATTACHMENT_MIME_TYPE)
                    ->min(5)
                    ->max(100 * 1024),
            ],
        ];
    }
}
