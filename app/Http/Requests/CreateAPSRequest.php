<?php

namespace App\Http\Requests;

use App\Enums;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateAPSRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->user();

        return $user->isRoot() || $user->belongsToHealthCenter(
            healthCenter: $this->route('health_center'),
            responsibility: Enums\UserResponsibility::ADMINISTRATOR
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:3', 'max:255'],
            'phone' => ['required', 'phone', 'unique:users'],
            'email' => ['nullable', 'email', 'unique:users', 'max:255'],
            'role' => ['required', Rule::enum(Enums\UserRole::class)->only([Enums\UserRole::APS])],
            'responsibility' => [
                'required',
                Rule::enum(Enums\UserResponsibility::class)->only([
                    Enums\UserResponsibility::OPERATOR,
                    Enums\UserResponsibility::ADMINISTRATOR,
                ]),
            ],
            'description' => ['nullable', 'string'],
        ];
    }
}
