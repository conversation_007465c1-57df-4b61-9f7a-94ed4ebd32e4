<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserBelongsToHealthCenter
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $healthCenter = $request->route('health_center');

        if (! $user->isRoot() && ! $user->belongsToHealthCenter($healthCenter)) {
            abort(403, __("Vous n'êtes pas autorisé à accéder à cette ressource."));
        }

        return $next($request);
    }
}
