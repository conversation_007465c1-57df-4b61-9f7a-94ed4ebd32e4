<?php

namespace App\Console\Commands;

use App\Services\ExceptionNotificationService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;

class TestExceptionSmsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:exception-sms {--force : Force sending even in non-production environments}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test SMS exception notifications by sending a test exception notification';

    /**
     * Execute the console command.
     */
    public function handle(ExceptionNotificationService $notificationService): int
    {
        $this->info('Testing SMS exception notifications...');

        // Check if phone number is configured
        $phone = config('app.sms_exception_notifications_phone');
        if (! $phone) {
            $this->error('❌ No phone number configured for SMS exception notifications.');
            $this->info('Set SMS_EXCEPTION_NOTIFICATIONS_PHONE in your .env file.');

            return self::FAILURE;
        }

        $this->info("Phone number configured: {$phone}");

        // Create a test exception
        $testException = new Exception('This is a test exception for SMS notification system');

        // Create a mock request
        $mockRequest = Request::create('/test-exception', 'GET');
        $mockRequest->headers->set('User-Agent', 'Test Command');

        // Temporarily override environment check if --force is used
        if ($this->option('force')) {
            config(['app.sms_exception_notifications_environment' => config('app.env')]);
            $this->warn('Forcing SMS notification in '.config('app.env').' environment');
        }

        try {
            $notificationService->notifyException($testException, $mockRequest);
            $this->info('✅ Test exception notification sent successfully!');
            $this->info('Check your SMS to verify the notification was received.');

            return self::SUCCESS;
        } catch (\Throwable $e) {
            $this->error('❌ Failed to send test exception notification:');
            $this->error($e->getMessage());

            return self::FAILURE;
        }
    }
}
