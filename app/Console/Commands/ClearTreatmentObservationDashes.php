<?php

namespace App\Console\Commands;

use App\Models\Victim\Treatment;
use Illuminate\Console\Command;

class ClearTreatmentObservationDashes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:clear-treatment-observation-dashes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear dashes in treatment observation text';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->clearTreatmentObservationDashes('-----');
        $this->clearTreatmentObservationDashes('----');

        $this->info('Cleared dashes in treatment observation text');
    }

    protected function clearTreatmentObservationDashes(string $symbol)
    {
        Treatment::query()
            ->where('observation', 'like', "%$symbol%")
            ->each(function (Treatment $treatment) use ($symbol) {
                // Update the observation only if not equal to $symbol
                if (trim($treatment->observation) !== $symbol) {
                    $treatment->observation = str_replace($symbol, '', $treatment->observation);
                    $treatment->saveQuietly();
                }
            });

    }
}
