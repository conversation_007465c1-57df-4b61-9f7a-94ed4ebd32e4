<?php

namespace App\Console\Commands;

use App\Enums\UserRole;
use App\Models\User;
use Hash;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class RootUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:root-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create Application Root User';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->ask('Name?');
        $phone = $this->ask('Phone Number?');

        $input = Validator::make(
            ['name' => $name, 'phone' => $phone],
            ['name' => ['required', 'string', 'min:2'], 'phone' => ['required', 'unique:users', 'phone']]
        )->validate();

        User::create([
            'name' => $input['name'],
            'phone' => $input['phone'],
            'password' => Hash::make(Str::random(6)),
            'role' => UserRole::ROOT,
        ]);

        $this->info("User {$name} created successfully");
    }
}
