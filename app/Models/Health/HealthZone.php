<?php

namespace App\Models\Health;

use App\Observers\HealthZoneObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperHealthZone
 */
#[ObservedBy([HealthZoneObserver::class])]
class HealthZone extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'province',
        'population_served',
    ];

    public function healthCenters(): Relations\HasMany
    {
        return $this->hasMany(HealthCenter::class);
    }
}
