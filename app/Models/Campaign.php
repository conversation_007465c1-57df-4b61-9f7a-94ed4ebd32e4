<?php

namespace App\Models;

use App\Enums;
use App\Models\Health\HealthCenter;
use App\Observers\CampaignObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations;

/**
 * @mixin IdeHelperCampaign
 */
#[ObservedBy([CampaignObserver::class])]
class Campaign extends Model
{
    use HasFactory;

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'user_role' => Enums\UserRole::class,
        ];
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'health_center_id',
        'user_role',
        'title',
        'body',
    ];

    public function healthCenter(): Relations\BelongsTo
    {
        return $this->belongsTo(HealthCenter::class);
    }
}
