<?php

namespace App\Models;

use App\Observers\AdviceObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperAdvice
 */
#[ObservedBy([AdviceObserver::class])]
class Advice extends Model
{
    use HasFactory, HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'advice_language_id',
    ];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'advices';

    public function adviceLanguage()
    {
        return $this->belongsTo(AdviceLanguage::class);
    }
}
