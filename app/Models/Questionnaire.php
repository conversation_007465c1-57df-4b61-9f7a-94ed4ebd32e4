<?php

namespace App\Models;

use App\Enums\QuestionnaireResponseType;
use App\Observers\QuestionnaireObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperQuestionnaire
 */
#[ObservedBy([QuestionnaireObserver::class])]
class Questionnaire extends Model
{
    use HasFactory, HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'type',
        'question',
        'hint',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => QuestionnaireResponseType::class,
        ];
    }

    public function choices(): HasMany
    {
        return $this->hasMany(QuestionnaireChoice::class);
    }
}
