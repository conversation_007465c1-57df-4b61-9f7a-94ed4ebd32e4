<?php

namespace App\Models\Victim;

use App\Enums\TreatmentType;
use App\Observers\TreatmentObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations;

/**
 * @mixin IdeHelperTreatment
 */
#[ObservedBy([TreatmentObserver::class])]
class Treatment extends Model
{
    use HasFactory, HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'type',
        'observation',
        'attachment',
        'instance_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => TreatmentType::class,
        ];
    }

    public function instance(): Relations\BelongsTo
    {
        return $this->belongsTo(Instance::class);
    }
}
