<?php

namespace App\Models\Victim;

use App\Enums;
use App\Models\Health\HealthCenter;
use App\Observers\InstanceObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperInstance
 */
#[ObservedBy([InstanceObserver::class])]
class Instance extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'description',
        'type',
        'status',
        'survivor_id',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'type' => Enums\InstanceType::NEW,
        'status' => Enums\InstanceStatus::OPEN,
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => Enums\InstanceType::class,
            'status' => Enums\InstanceStatus::class,
        ];
    }

    protected function opened(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status === Enums\InstanceStatus::OPEN,
        );
    }

    protected function closed(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status === Enums\InstanceStatus::CLOSED,
        );
    }

    public function toggleStatus(): void
    {
        $this->update([
            'status' => $this->status === Enums\InstanceStatus::OPEN ?
                Enums\InstanceStatus::CLOSED :
                Enums\InstanceStatus::OPEN,
        ]);
    }

    public static function getUniqueCode(): string
    {
        return 'CS-'.random_int(10000, 999999);
    }

    public function treatmentsLabels(): ?string
    {
        $labels = $this->treatments->map(fn ($treatment) => $treatment->type->getLabel());

        return $labels->isEmpty() ? null : implode(', ', $labels->toArray());
    }

    public function creator()
    {
        return $this->hasOne(InstanceCreator::class);
    }

    public function healthCenter(): Relations\BelongsTo
    {
        return $this->belongsTo(HealthCenter::class);
    }

    public function survivor(): Relations\BelongsTo
    {
        return $this->belongsTo(Survivor::class);
    }

    public function relapse(): Relations\HasOne
    {
        return $this->hasOne(Relapse::class);
    }

    public function diagnostics(): Relations\HasMany
    {
        return $this->hasMany(Diagnostic::class);
    }

    public function treatments(): Relations\HasMany
    {
        return $this->hasMany(Treatment::class);
    }

    public function companions(): Relations\HasMany
    {
        return $this->hasMany(Companion::class);
    }

    public function followups(): Relations\HasMany
    {
        return $this->hasMany(Followup::class);
    }
}
