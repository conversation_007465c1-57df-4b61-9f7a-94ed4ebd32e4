<?php

namespace App\Observers;

use App\Models\Advice;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class AdviceObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Advice "created" event.
     */
    public function created(Advice $advice): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $advice->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $advice->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Advice "updated" event.
     */
    public function updated(Advice $advice): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $advice->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $advice->getChanges(),
                data: $advice->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Advice "deleted" event.
     */
    public function deleted(Advice $advice): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $advice->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $advice->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Advice "restored" event.
     */
    public function restored(Advice $advice): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $advice->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $advice->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Advice "force deleted" event.
     */
    public function forceDeleted(Advice $advice): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $advice->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $advice->withoutRelations()->toArray(),
            )
        );
    }
}
