<?php

namespace App\Observers;

use App\Models\User;
use App\Models\Victim\Followup;
use App\Notifications\FollowupCreated;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class FollowupObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Followup "created" event.
     */
    public function created(Followup $Followup): void
    {
        // Notify APS users in the same health center
        $this->notifyAPSUsers($Followup);

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Notify APS users in the same health center about the new followup.
     */
    private function notifyAPSUsers(Followup $followup): void
    {
        // Get the health center from the followup's instance
        $healthCenter = $followup->instance->healthCenter;

        // Get all APS users in the same health center
        $apsUsers = $healthCenter->aps()->get();

        // Send notification to each APS user
        $apsUsers->each(function (User $user) use ($followup) {
            $user->notify(new FollowupCreated($followup));
        });
    }

    /**
     * Handle the Followup "updated" event.
     */
    public function updated(Followup $Followup): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $Followup->getChanges(),
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Followup "deleted" event.
     */
    public function deleted(Followup $Followup): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Followup "restored" event.
     */
    public function restored(Followup $Followup): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Followup "force deleted" event.
     */
    public function forceDeleted(Followup $Followup): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $Followup->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $Followup->withoutRelations()->toArray(),
            )
        );
    }
}
