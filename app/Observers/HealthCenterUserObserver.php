<?php

namespace App\Observers;

use App\Models\HealthCenterUser;
use App\Models\Victim\Companion;
use App\Notifications;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class HealthCenterUserObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the HealthCenterUser "created" event.
     */
    public function created(HealthCenterUser $healthCenterUser): void
    {
        $healthCenterUser->user->notify(
            new Notifications\HealthCenterUserAttached($healthCenterUser)
        );

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenterUser->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $healthCenterUser->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthCenterUser "updated" event.
     */
    public function updated(HealthCenterUser $healthCenterUser): void
    {
        if ($healthCenterUser->wasChanged('responsibility')) {
            $healthCenterUser->user->notify(
                new Notifications\HealthCenterUserResponsibilityChanged($healthCenterUser)
            );
        }

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenterUser->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $healthCenterUser->getChanges(),
                data: $healthCenterUser->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthCenterUser "deleted" event.
     */
    public function deleted(HealthCenterUser $healthCenterUser): void
    {
        // Clean up
        Companion::whereRelation('instance', 'health_center_id', '=', $healthCenterUser->health_center_id)
            ->where('user_id', $healthCenterUser->user_id)
            ->get()
            ->each(fn (Companion $companion) => $companion->forceDeleteQuietly());

        $healthCenterUser->user->notify(
            new Notifications\HealthCenterUserDeleted($healthCenterUser->healthCenter)
        );

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenterUser->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $healthCenterUser->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthCenterUser "restored" event.
     */
    public function restored(HealthCenterUser $healthCenterUser): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenterUser->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $healthCenterUser->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthCenterUser "force deleted" event.
     */
    public function forceDeleted(HealthCenterUser $healthCenterUser): void
    {
        // Clean up
        Companion::whereRelation('instance', 'health_center_id', '=', $healthCenterUser->health_center_id)
            ->where('user_id', $healthCenterUser->user_id)
            ->get()
            ->each(fn (Companion $companion) => $companion->forceDeleteQuietly());

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenterUser->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $healthCenterUser->withoutRelations()->toArray(),
            )
        );
    }
}
