<?php

namespace App\Observers;

use App\Models\Health\HealthCenter;

class HealthCenterObserver
{
    /**
     * Handle the HealthCenter "created" event.
     */
    public function created(HealthCenter $healthCenter): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenter->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $healthCenter->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthCenter "updated" event.
     */
    public function updated(HealthCenter $healthCenter): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenter->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $healthCenter->getChanges(),
                data: $healthCenter->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthCenter "deleted" event.
     */
    public function deleted(HealthCenter $healthCenter): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenter->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $healthCenter->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthCenter "restored" event.
     */
    public function restored(HealthCenter $healthCenter): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenter->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $healthCenter->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthCenter "force deleted" event.
     */
    public function forceDeleted(HealthCenter $healthCenter): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthCenter->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $healthCenter->withoutRelations()->toArray(),
            )
        );
    }
}
