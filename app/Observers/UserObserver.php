<?php

namespace App\Observers;

use App\Models\User;
use App\Notifications;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Support\Facades\Hash;

class UserObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        $password = User::randomPassword();

        logger(
            "User {$user->phone} created with password: $password"
        );

        $user->fill(['password' => Hash::make($password)])->saveQuietly();

        $user->notify(new Notifications\UserCreated($password));

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $user->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $user->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $user->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $user->getChanges(),
                data: $user->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $user->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $user->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $user->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $user->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $user->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $user->withoutRelations()->toArray(),
            )
        );
    }
}
