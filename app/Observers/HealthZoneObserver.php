<?php

namespace App\Observers;

use App\Models\Health\HealthZone;

class HealthZoneObserver
{
    /**
     * Handle the HealthZone "created" event.
     */
    public function created(HealthZone $healthZone): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthZone->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $healthZone->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthZone "updated" event.
     */
    public function updated(HealthZone $healthZone): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthZone->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $healthZone->getChanges(),
                data: $healthZone->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthZone "deleted" event.
     */
    public function deleted(HealthZone $healthZone): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthZone->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $healthZone->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthZone "restored" event.
     */
    public function restored(HealthZone $healthZone): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthZone->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $healthZone->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the HealthZone "force deleted" event.
     */
    public function forceDeleted(HealthZone $healthZone): void
    {
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $healthZone->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $healthZone->withoutRelations()->toArray(),
            )
        );
    }
}
