<?php

namespace App\Observers;

use App\Models\Victim\Companion;
use App\Notifications;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class CompanionObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Companion "created" event.
     */
    public function created(Companion $companion): void
    {
        $companion->user->notify(
            new Notifications\CompanionCreated($companion)
        );

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $companion->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $companion->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Companion "updated" event.
     */
    public function updated(Companion $companion): void
    {
        if ($companion->wasChanged('type')) {
            $companion->user->notify(
                new Notifications\CompanionTypeChanged($companion)
            );
        }

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $companion->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $companion->getChanges(),
                data: $companion->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Companion "deleted" event.
     */
    public function deleted(Companion $companion): void
    {
        $instance = $companion->instance;

        $companion->user->notify(
            new Notifications\CompanionDeleted(
                survivor: $instance->survivor,
                healthCenter: $instance->healthCenter,
            )
        );

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $companion->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $companion->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Companion "restored" event.
     */
    public function restored(Companion $companion): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $companion->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $companion->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Companion "force deleted" event.
     */
    public function forceDeleted(Companion $companion): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $companion->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $companion->withoutRelations()->toArray(),
            )
        );
    }
}
