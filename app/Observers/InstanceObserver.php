<?php

namespace App\Observers;

use App\Enums;
use App\Models\Victim;
use App\Notifications;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class InstanceObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Instance "created" event.
     */
    public function created(Victim\Instance $instance): void
    {
        $userAPS = auth()->user();

        if ($userAPS !== null) {
            if ($userAPS->role !== Enums\UserRole::APS) {
                $userAPS = $instance->healthCenter->aps()->first() ?? auth()->user();
            }

            $instance->creator()->create(['user_id' => $userAPS->id]);
        }

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $instance->getTable(),
                action: \App\Enums\ActivityActionType::CREATE,
                attributes: [],
                data: $instance->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Instance "updated" event.
     */
    public function updated(Victim\Instance $instance): void
    {
        if (! $instance->wasChanged('status')) {
            return;
        }

        $instance->companions->each(function (Victim\Companion $companion) use ($instance) {
            $companion->user->notify(
                new Notifications\InstanceStatusUpdated(
                    instance: $instance,
                    companion: $companion
                )
            );
        });

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $instance->getTable(),
                action: \App\Enums\ActivityActionType::UPDATE,
                attributes: $instance->getChanges(),
                data: $instance->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Instance "deleted" event.
     */
    public function deleted(Victim\Instance $instance): void
    {
        // Delete companions
        $instance->companions()
            ->withTrashed()
            ->each(fn (Victim\Companion $companion) => $companion->deleteQuietly());

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $instance->getTable(),
                action: \App\Enums\ActivityActionType::DELETE,
                attributes: [],
                data: $instance->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Instance "restored" event.
     */
    public function restored(Victim\Instance $instance): void
    {
        // Restore companions
        $instance->companions()
            ->withTrashed()
            ->each(fn (Victim\Companion $companion) => $companion->restoreQuietly());

        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $instance->getTable(),
                action: \App\Enums\ActivityActionType::RESTORE,
                attributes: [],
                data: $instance->withoutRelations()->toArray(),
            )
        );
    }

    /**
     * Handle the Instance "force deleted" event.
     */
    public function forceDeleted(Victim\Instance $instance): void
    {
        // Record activity
        event(
            new \App\Events\RecordActivityEvent(
                user: auth()->user(),
                model: $instance->getTable(),
                action: \App\Enums\ActivityActionType::FORCE_DELETE,
                attributes: [],
                data: $instance->withoutRelations()->toArray(),
            )
        );
    }
}
