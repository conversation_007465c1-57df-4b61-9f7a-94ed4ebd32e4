<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON>l <<EMAIL>>
 */


namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string|null $user_id
 * @property string $model
 * @property \App\Enums\ActivityActionType $action
 * @property array<array-key, mixed>|null $attributes
 * @property array<array-key, mixed>|null $data
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperActivity {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $advice_language_id
 * @property string $title
 * @property string $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\AdviceLanguage $adviceLanguage
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Advice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Advice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Advice query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Advice whereAdviceLanguageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Advice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Advice whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Advice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Advice whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Advice whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperAdvice {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Advice> $advices
 * @property-read int|null $advices_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdviceLanguage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdviceLanguage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdviceLanguage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdviceLanguage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdviceLanguage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdviceLanguage whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdviceLanguage whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperAdviceLanguage {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string|null $health_center_id
 * @property \App\Enums\UserRole|null $user_role
 * @property string $title
 * @property string $body
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Health\HealthCenter|null $healthCenter
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign whereBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign whereHealthCenterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign whereUserRole($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperCampaign {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $user_id
 * @property \App\Enums\CompanionType $role
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompanionRole newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompanionRole newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompanionRole query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompanionRole whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompanionRole whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompanionRole whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompanionRole whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CompanionRole whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperCompanionRole {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $user_id
 * @property string $token
 * @property int $expired
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FcmToken newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FcmToken newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FcmToken query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FcmToken whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FcmToken whereExpired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FcmToken whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FcmToken whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FcmToken whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FcmToken whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperFcmToken {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $health_center_id
 * @property string $user_id
 * @property \App\Enums\UserResponsibility $responsibility
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Health\HealthCenter $healthCenter
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenterUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenterUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenterUser query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenterUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenterUser whereHealthCenterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenterUser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenterUser whereResponsibility($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenterUser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenterUser whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperHealthCenterUser {}
}

namespace App\Models\Health{
/**
 * 
 *
 * @property string $id
 * @property string $health_zone_id
 * @property string $name
 * @property string $address
 * @property string $phone
 * @property string|null $services_offered
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Health\HealthZone $healthZone
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Instance> $instances
 * @property-read int|null $instances_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Relapse> $relapses
 * @property-read int|null $relapses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Survivor> $survivors
 * @property-read int|null $survivors_count
 * @property-read \App\Models\HealthCenterUser|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $users
 * @property-read int|null $users_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter whereHealthZoneId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter whereServicesOffered($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthCenter withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperHealthCenter {}
}

namespace App\Models\Health{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property int|null $population_served
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $province
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Health\HealthCenter> $healthCenters
 * @property-read int|null $health_centers_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone wherePopulationServed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone whereProvince($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthZone withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperHealthZone {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $question
 * @property \App\Enums\QuestionnaireResponseType $type
 * @property string|null $hint
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\QuestionnaireChoice> $choices
 * @property-read int|null $choices_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Questionnaire newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Questionnaire newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Questionnaire query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Questionnaire whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Questionnaire whereHint($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Questionnaire whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Questionnaire whereQuestion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Questionnaire whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Questionnaire whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperQuestionnaire {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $questionnaire_id
 * @property string $choice
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QuestionnaireChoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QuestionnaireChoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QuestionnaireChoice query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QuestionnaireChoice whereChoice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QuestionnaireChoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QuestionnaireChoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QuestionnaireChoice whereQuestionnaireId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QuestionnaireChoice whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperQuestionnaireChoice {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $phone
 * @property string $token
 * @property string $password
 * @property \Illuminate\Support\Carbon $expires_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ResetPasswordRequest whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperResetPasswordRequest {}
}

namespace App\Models{
/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property string|null $email
 * @property string $phone
 * @property \App\Enums\UserRole $role
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\CompanionRole|null $companionRole
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Companion> $companions
 * @property-read int|null $companions_count
 * @property-read \App\Models\FcmToken|null $fcmToken
 * @property-read \App\Models\HealthCenterUser|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Health\HealthCenter> $healthCenters
 * @property-read int|null $health_centers_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperUser {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property \App\Enums\CompanionType $type
 * @property string $instance_id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Followup> $followups
 * @property-read int|null $followups_count
 * @property-read \App\Models\Victim\Instance $instance
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Companion withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperCompanion {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $instance_id
 * @property string $questionnaire_id
 * @property string $response
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Instance $instance
 * @property-read \App\Models\Questionnaire $questionnaire
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Diagnostic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Diagnostic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Diagnostic query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Diagnostic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Diagnostic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Diagnostic whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Diagnostic whereQuestionnaireId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Diagnostic whereResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Diagnostic whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperDiagnostic {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $title
 * @property string $description
 * @property string $instance_id
 * @property string|null $companion_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Companion|null $companion
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup whereCompanionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Followup whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperFollowup {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $health_center_id
 * @property string $survivor_id
 * @property \App\Enums\InstanceType $type
 * @property \App\Enums\InstanceStatus $status
 * @property string $code
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $closed
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Companion> $companions
 * @property-read int|null $companions_count
 * @property-read \App\Models\Victim\InstanceCreator|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Diagnostic> $diagnostics
 * @property-read int|null $diagnostics_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Followup> $followups
 * @property-read int|null $followups_count
 * @property-read \App\Models\Health\HealthCenter $healthCenter
 * @property-read mixed $opened
 * @property-read \App\Models\Victim\Relapse|null $relapse
 * @property-read \App\Models\Victim\Survivor $survivor
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Victim\Treatment> $treatments
 * @property-read int|null $treatments_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereHealthCenterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereSurvivorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Instance withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperInstance {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property int $id
 * @property string $user_id
 * @property string $instance_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Instance $instance
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InstanceCreator newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InstanceCreator newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InstanceCreator query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InstanceCreator whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InstanceCreator whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InstanceCreator whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InstanceCreator whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InstanceCreator whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperInstanceCreator {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $instance_id
 * @property string $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Instance $instance
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Relapse newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Relapse newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Relapse query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Relapse whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Relapse whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Relapse whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Relapse whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Relapse whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperRelapse {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property string $code
 * @property string|null $description
 * @property string $health_center_id
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Health\HealthCenter $healthCenter
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor whereHealthCenterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Survivor withoutTrashed()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperSurvivor {}
}

namespace App\Models\Victim{
/**
 * 
 *
 * @property string $id
 * @property \App\Enums\TreatmentType $type
 * @property string $observation
 * @property string|null $attachment
 * @property string $instance_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Victim\Instance $instance
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment whereAttachment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment whereObservation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Treatment whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperTreatment {}
}

