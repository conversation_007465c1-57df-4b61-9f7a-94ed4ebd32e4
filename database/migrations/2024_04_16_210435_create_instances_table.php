<?php

use App\Enums;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('instances', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('health_center_id')->constrained()->cascadeOnDelete();
            $table->foreignUlid('survivor_id')->constrained()->cascadeOnDelete();
            $table->string('type')->default(Enums\InstanceType::NEW->value);
            $table->string('status')->default(Enums\InstanceStatus::OPEN->value);
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('instances');
    }
};
