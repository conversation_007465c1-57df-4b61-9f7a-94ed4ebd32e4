- name: Deploy
  hosts: all
  become: true
  gather_facts: true
  remote_user: ubuntu

  vars:
    required_vars:
      - domain_name
      - repository_url

      - database_name
      - database_username
      - database_password

      - aws_access_key_id
      - aws_secret_access_key
      - aws_default_region
      - aws_bucket

      - twilio_account_sid
      - twilio_auth_token
      - twilio_messaging_service_sid

      - firebase_credentials # b64 encoded

  vars_files:
    - vars/main.yml

  pre_tasks:
    - name: Assert each variable in the list is defined and non-empty
      assert:
        that:
          - item is defined
          - item | length > 0
        fail_msg: "The variable '{{ item }}' must be defined and non-empty"
        success_msg: "'{{ item }}' is defined and non-empty"
      loop: "{{ required_vars }}"
      loop_control:
        loop_var: item

  roles:
    - role: tools
    - role: firewall
    - role: mysql
    - role: redis
    - role: php
    - role: nginx
    - role: nodejs
    - role: puppeteer # must come after nodejs since it depends on NPM
    - role: app
