---
# APPLICATION
domain_name: innovation-fund-healthcare.org
admin_email: <EMAIL>

# PROJECT
project_name: s3g
deploy_path: /var/www/{{ project_name }}

# REPOSITORY
repository_version: main

# DATABASE
database_name: s3g
database_username: s3g

# OPcache
opcache_enable: true

# AWS
aws_bucket: innovation-fund-healthcare
aws_default_region: us-east-2

# MYSQL
mysql_root_password: "{{ database_password  }}"
mysql_databases:
  - name: "{{ database_name }}"
    encoding: utf8
    collation: utf8_general_ci
mysql_users:
  - name: "{{ database_username }}"
    host: "%"
    password: "{{ database_password }}"
    priv: "{{ database_name }}.*:ALL"

# PHP
php_version: "8.3"
