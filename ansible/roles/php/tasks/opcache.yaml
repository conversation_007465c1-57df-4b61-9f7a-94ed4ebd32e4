- name: Ensure opcache configuration file exists with specific content
  copy:
    dest: /etc/php/{{ php_version }}/fpm/conf.d/10-opcache.ini
    content: |
      ; configuration for php opcache module
      ; priority=10
      opcache.memory_consumption=256
      opcache.interned_strings_buffer=64
      opcache.max_accelerated_files=32531
      opcache.validate_timestamps=0
      opcache.enable_cli=1
      opcache.fast_shutdown=0
      zend_extension=opcache.so
      opcache.jit=off
      opcache.enable=1
    force: yes
  notify: restart php-fpm
