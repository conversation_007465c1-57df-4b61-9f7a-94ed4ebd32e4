---
- name: "PHP Version"
  debug:
    var: php_version

- name: "PHP Extensions"
  debug:
    var: php_extensions

- name: Install prerequisite packages
  become: true
  ansible.builtin.apt:
    name: "{{ item }}"
    state: present
    update_cache: yes
    cache_valid_time: 3600
  loop:
    - software-properties-common
    - ca-certificates
    - lsb-release
    - apt-transport-https
    - libsodium-dev

- name: Add repository for PHP from Ondřej Surý
  become: true
  ansible.builtin.apt_repository:
    repo: ppa:ondrej/php
    state: present
    update_cache: yes
  environment:
    LC_ALL: C.UTF-8

- name: Update apt cache
  become: true
  ansible.builtin.apt:
    update_cache: yes
    cache_valid_time: 3600

- name: Install PHP with a specific version
  become: true
  ansible.builtin.apt:
    name: "php{{ php_version }}"
    state: latest
    update_cache: yes

- name: Install PHP extensions
  become: true
  ansible.builtin.apt:
    name: "php{{ php_version }}-{{ item }}"
    state: latest
    update_cache: yes
  loop: "{{ php_extensions }}"

- stat:
    path: "/etc/php/{{ php_version }}/fpm/php.ini"
  register: php_config_file

- name: Block | Update PHP INIT File
  block:
    - name: Update PHP Post Size directive
      lineinfile:
        path: /etc/php/{{ php_version }}/fpm/php.ini
        regexp: "^{{ item.key }}"
        line: "{{ item.key }} = {{ item.value }}"
        state: present
      loop:
        - key: post_max_size
          value: "{{ php_post_size }}"
        - key: upload_max_filesize
          value: "{{ php_post_size }}"
        - key: max_file_uploads
          value: "40"
      notify: restart php-fpm
  when: php_config_file.stat.exists

- name: Check if Composer is already installed
  stat:
    path: "/usr/local/bin/composer"
  register: composer_file

- block:
    - name: Download Composer installer
      become: true
      ansible.builtin.get_url:
        url: https://getcomposer.org/installer
        dest: /tmp/composer-setup.php
        mode: "0755"

    - name: Run Composer installer
      become: true
      ansible.builtin.command: php /tmp/composer-setup.php --install-dir=/usr/local/bin --filename=composer

    - name: Remove Composer installer
      become: true
      ansible.builtin.file:
        path: /tmp/composer-setup.php
        state: absent

  when: php_install_composer and not composer_file.stat.exists

- name: Create the directory for PHP FPM conf.d if it doesn't exist
  file:
    path: /etc/php/{{ php_version }}/fpm/conf.d
    state: directory
    mode: "0755"

- include_tasks: opcache.yaml
  when: opcache_enable
