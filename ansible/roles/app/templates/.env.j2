APP_NAME=S3G
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_TIMEZONE=UTC
APP_PLAYSTORE_ID=
APP_URL=https://{{ domain_name }}

APP_LOCALE=fr
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE={{ database_name }}
DB_USERNAME={{ database_username }}
DB_PASSWORD={{ database_password }}

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

PULSE_INGEST_DRIVER=redis

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=redis
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID={{ aws_access_key_id }}
AWS_SECRET_ACCESS_KEY={{ aws_secret_access_key }}
AWS_DEFAULT_REGION={{ aws_default_region }}
AWS_BUCKET={{ aws_bucket }}
AWS_USE_PATH_STYLE_ENDPOINT=false

TWILIO_ACCOUNT_SID={{ twilio_account_sid }}
TWILIO_AUTH_TOKEN={{ twilio_auth_token }}
TWILIO_MESSAGING_SERVICE_SID={{ twilio_messaging_service_sid }}

VITE_APP_NAME="${APP_NAME}"

FIREBASE_CREDENTIALS=storage/app/firebase-auth.json
FIREBASE_DATABASE_URL=https://innovation-fund-healthca-5e6cc-default-rtdb.firebaseio.com
