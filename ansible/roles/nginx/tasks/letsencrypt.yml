---
- name: LetsEncrypt | Install certbot
  apt:
    name:
      - certbot
      - python3-certbot-nginx
    state: present

- name: LetsEncrypt | Check if certificate exists
  stat:
    path: "/etc/letsencrypt/live/{{ domain_name }}/fullchain.pem"
  register: certificate

- block:
    - name: LetsEncrypt | Remove cert configuration from vhost
      shell:
        cmd: |
          sed -i -r 's/(listen (.*443))/\1;#/g; s/(ssl_(certificate|certificate_key|trusted_certificate) )/#;#\1/g' /etc/nginx/sites-available/{{ domain_name }}
      ignore_errors: true

    - name: LetsEncrypt | Certbot (--dry-run)
      shell:
        cmd: "certbot certonly --webroot -d {{ domain_name }} --email {{ admin_email }} -w /var/www/_letsencrypt -n --agree-tos --dry-run"

    - name: LetsEncrypt | Certbot
      command: "certbot certonly --webroot -d {{ domain_name }} --email {{ admin_email }} -w /var/www/_letsencrypt -n --agree-tos"

  when: not certificate.stat.exists
