---
- name: <PERSON>in<PERSON> | _letsencrypt directory
  file:
    state: directory
    path: /var/www/_letsencrypt
    owner: www-data
    group: www-data

- name: Nginx | letsencrypt certificates directories
  file:
    state: directory
    path: "/etc/letsencrypt/{{ item }}"
  loop:
    - dev
    - live

- name: Nginx | SSL init
  shell:
    cmd: openssl dhparam -out /etc/nginx/dhparam.pem 2048
    creates: /etc/nginx/dhparam.pem
