---
- name: Stop Apache service
  systemd:
    name: apache2
    state: stopped
    enabled: no
  ignore_errors: true

- name: Remove Apache packages
  apt:
    name:
      - apache2
      - apache2-utils
      - apache2.2-bin
      - apache2-common
    state: absent
    purge: yes
    autoremove: yes

- name: Nginx | Install
  apt:
    name:
      - nginx
      - fail2ban
    state: present

- name: Nginx | Create folder /etc/nginx/sites-enabled
  file:
    path: /etc/nginx/sites-enabled
    state: directory

- name: Nginx | Create folder /etc/nginx/sites-available
  file:
    path: /etc/nginx/sites-available
    state: directory

- name: Nginx | Create folder /etc/nginx/conf.d
  file:
    path: /etc/nginx/conf.d
    state: directory

- name: Nginx | Increase Client Max Body Size
  copy:
    content: "client_max_body_size 100M;"
    dest: /etc/nginx/conf.d/client_max_body_size.conf
    force: yes
  notify: nginx restart

- name: Nginx | Default vhost
  template:
    src: default.j2
    dest: "/etc/nginx/sites-available/default"
    force: yes

- name: Nginx | Create /etc/nginx/nginxconfig.io
  file:
    state: directory
    path: /etc/nginx/nginxconfig.io

- name: Template Nginx | config gile
  template:
    src: "{{ item }}.j2"
    dest: "/etc/nginx/{{ item }}"
  loop:
    - nginxconfig.io/letsencrypt.conf
  notify: nginx restart

- name: "Nginx Vhost | Copy Template"
  template:
    src: vhost.j2
    dest: "/etc/nginx/sites-available/{{ domain_name }}"
  notify: nginx restart

- include_tasks: certificates.yml

- name: "Nginx Vhost | SymbolLink "
  file:
    path: "/etc/nginx/sites-enabled/{{ domain_name }}"
    src: "/etc/nginx/sites-available/{{ domain_name }}"
    state: link
  notify: nginx restart

- include_tasks: letsencrypt.yml

- name: "Nginx | Vhost {{ domain_name }}"
  template:
    src: vhost.j2
    dest: "/etc/nginx/sites-available/{{ domain_name }}"
  notify: nginx restart

# CRON | Certbot new
- name: CRON | Certbot new
  cron:
    name: certbot renew
    special_time: daily
    job: "/usr/bin/certbot renew  --quiet"

- name: LetsEncrypt | Reload nginx on renewal
  copy:
    content: "#!/bin/bash\nnginx -t && systemctl reload nginx"
    dest: /etc/letsencrypt/renewal-hooks/post/nginx-reload.sh
    mode: 0755

# fail2ban
- name: "Nginx | NginxReqLimit fail2ban filter"
  template:
    src: fail2ban/nginx-req-limit.conf.j2
    dest: /etc/fail2ban/filter.d/nginx-req-limit.conf
    force: yes

- name: "Nginx | fail2ban Jail"
  template:
    src: fail2ban/jail.local.j2
    dest: /etc/fail2ban/jail.local
  notify: fail2ban restart
