server {
    listen  443 ssl http2;
    listen  [::]:443 ssl http2;
    server_name {{ domain_name }};
    root {{ deploy_path }}/public;
 
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
 
    index index.php;
 
    charset utf-8;

    ssl_certificate         /etc/letsencrypt/live/{{ domain_name }}/fullchain.pem;
    ssl_certificate_key     /etc/letsencrypt/live/{{ domain_name }}/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/{{ domain_name }}/chain.pem;
 
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
 
    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }
 
    error_page 404 /index.php;
 
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php{{ php_version }}-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
 
    location ~ /\.(?!well-known).* {
        deny all;
    }

    # gzip
    gzip            on;
    gzip_vary       on;
    gzip_proxied    any;
    gzip_comp_level 6;
    gzip_types      text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;
}


server {
    listen      80;
    listen      [::]:80;
    server_name {{ domain_name }};
    include     nginxconfig.io/letsencrypt.conf;

    location / {
        return 301 https://{{ domain_name }}$request_uri;
    }
}
