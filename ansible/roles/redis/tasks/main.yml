---
# tasks file for ansible/roles/redis

- name: Convert Redis GPG key to gpg format
  shell: curl -fsSL https://packages.redis.io/gpg | sudo gpg --dearmor -o /usr/share/keyrings/redis-archive-keyring.gpg
  args:
    creates: /usr/share/keyrings/redis-archive-keyring.gpg
    executable: /bin/bash

- name: Add Redis repository to sources list
  shell: echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/redis.list
  args:
    creates: /etc/apt/sources.list.d/redis.list
    executable: /bin/bash

- name: Update apt cache
  ansible.builtin.apt:
    update_cache: yes

- name: Enable Memory overcommit
  command: sysctl vm.overcommit_memory=1

- name: Add 'vm.overcommit_memory = 1' to /etc/sysctl.conf if not present
  lineinfile:
    path: /etc/sysctl.conf
    state: present
    regexp: "^vm.overcommit_memory"
    line: "vm.overcommit_memory = 1"
    backup: yes

- name: Apply sysctl settings
  command: sysctl -p

- name: Install Redis
  ansible.builtin.apt:
    name: redis
    state: present
