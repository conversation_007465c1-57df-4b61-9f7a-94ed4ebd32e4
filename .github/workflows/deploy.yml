name: Deploy with Ansible

on:
  push:
    # branches: [main]
    tags: [v**]

env:
  GITHUB_SERVER_URL: ${{ github.server_url }}
  GITHUB_REPOSITORY: ${{ github.repository }}
  GITHUB_REPOSITORY_OWNER: ${{ github.repository_owner }}

jobs:
  deploy:
    runs-on: ubuntu-24.04
    permissions:
      contents: read

    steps:
      - uses: actions/checkout@v4

      # Sets up Python and installs Ansible
      - name: Set up Python for Ansible
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r ansible/requirements.txt

      - name: Install Ansible Requirements
        run: |
          ansible-galaxy install -r ansible/requirements.yml

      - name: Configure SSH
        env:
          SSH_PRIVATE_KEY: ${{ secrets.ANSIBLE_DEPLOY_KEY }}
        run: |
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_ed25519
          chmod 600 ~/.ssh/id_ed25519

      # Run the Ansible playbook
      - name: Deploy with Ansible playbook
        run: |
          ansible-playbook -u "ubuntu" -i "${{ vars.ANSIBLE_SERVER_HOST }}," ansible/deploy.yml --extra-vars '{
              "repository_url": "https://${{ env.GITHUB_REPOSITORY_OWNER }}:${{ secrets.GITHUB_TOKEN }}@github.com/${{ env.GITHUB_REPOSITORY }}",
              "database_password":"${{ vars.DATABASE_PASSWORD }}",
              "aws_access_key_id":"${{ secrets.AWS_ACCESS_KEY_ID }}",
              "aws_secret_access_key":"${{ secrets.AWS_SECRET_ACCESS_KEY }}",
              "twilio_account_sid":"${{ secrets.TWILIO_ACCOUNT_SID }}",
              "twilio_auth_token":"${{ secrets.TWILIO_AUTH_TOKEN }}",
              "twilio_messaging_service_sid":"${{ secrets.TWILIO_MESSAGING_SERVICE_SID }}",
              "firebase_credentials":"${{ secrets.FIREBASE_CREDENTIALS }}"
          }'
        env:
          ANSIBLE_HOST_KEY_CHECKING: False
