/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./resources/**/*.vue",
        "node_modules/preline/dist/*.js",
    ],
    theme: {
        extend: {
            colors: {
                amaranth: {
                    50: "#fff1f1",
                    100: "#fee5e6",
                    200: "#fdced1",
                    300: "#fba6ac",
                    400: "#f87480",
                    500: "#ef3a4f",
                    600: "#dc2241",
                    700: "#ba1635",
                    800: "#9c1533",
                    900: "#851632",
                    950: "#4a0716",
                },
            },
        },
    },
    plugins: [require("@tailwindcss/typography"), require("preline/plugin")],
};
