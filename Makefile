SHELL:=/bin/bash
isDocker:=$(shell docker info > /dev/null 2>&1 && echo 1)
python:=python3
venv:=.venv/bin/activate
source:=source
pa:=php artisan


.DEFAULT_GOAL := help
.PHONY: help
help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'






.PHONY: install
install:
	composer install
	pnpm install







.PHONY: dev
dev:
	@pnpm dev






.PHONY: pull
pull:
	git pull origin main






.PHONY: config-cache
config-cache:
	$(pa) config:clear
	$(pa) config:cache






.PHONY: laravel-setup
laravel-setup:
	$(pa) key:generate
	$(pa) migrate
	$(pa) storage:link
	$(pa) reverb:install








.PHONY: prod
prod:
	composer install --optimize-autoloader --no-dev

	$(pa) config:clear
	$(pa) config:cache

	$(pa) event:clear
	$(pa) event:cache

	$(pa) view:clear
	$(pa) view:cache

	$(pa) icons:cache

	$(pa) filament:clear-cached-components
	$(pa) filament:cache-components

	$(pa) optimize:clear
	$(pa) optimize

	$(pa) migrate --force

	pnpm install
	pnpm build




.PHONY: queue-clear
queue-clear:
	$(pa) queue:clear
	$(pa) queue:prune-batches
	$(pa) queue:prune-failed







.PHONY: queue-dev
queue-dev: queue-clear
	$(pa) queue:listen






.PHONY: ide
ide:
	$(pa) clear-compiled
	$(pa) ide-helper:generate
	$(pa) ide-helper:models -M
