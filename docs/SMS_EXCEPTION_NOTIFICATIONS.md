# SMS Exception Notifications

This feature automatically sends SMS notifications to admin users when exceptions occur in the application.

## Configuration

Add the following environment variables to your `.env` file:

```env
# SMS Exception Notifications
SMS_EXCEPTION_NOTIFICATIONS_ENABLED=true
SMS_EXCEPTION_NOTIFICATIONS_ENVIRONMENT=production
SMS_EXCEPTION_NOTIFICATIONS_RATE_LIMIT=5
```

### Configuration Options

- **SMS_EXCEPTION_NOTIFICATIONS_ENABLED**: Enable/disable SMS notifications (default: true)
- **SMS_EXCEPTION_NOTIFICATIONS_ENVIRONMENT**: Comma-separated list of environments where notifications should be sent (default: production)
- **SMS_EXCEPTION_NOTIFICATIONS_RATE_LIMIT**: Maximum number of SMS notifications per hour for the same exception type (default: 5)

## How It Works

1. **Exception Detection**: The system automatically catches all exceptions through <PERSON><PERSON>'s exception handler
2. **Filtering**: Certain exception types are filtered out (authentication errors, validation errors, 404s, etc.)
3. **Rate Limiting**: Prevents spam by limiting notifications for the same exception type
4. **Recipient Selection**: Sends notifications to users with ROOT or APS roles who have phone numbers
5. **SMS Delivery**: Uses the existing Twilio integration to send SMS messages

## Notification Recipients

SMS notifications are sent to users who meet these criteria:
- Have role `ROOT` or `APS`
- Have a valid phone number in their profile
- Are active (not soft deleted)

## Exception Types Excluded

The following exception types do NOT trigger SMS notifications:
- Authentication exceptions
- Validation exceptions
- 404 Not Found exceptions
- Method Not Allowed exceptions
- Model Not Found exceptions

## SMS Message Format

The SMS message includes:
- Application name and environment
- Exception type and message (truncated if too long)
- File and line number where the exception occurred
- URL where the exception happened
- User ID (if authenticated)
- Timestamp

Example SMS:
```
🚨 S3G (production)
Exception: QueryException
Message: SQLSTATE[42S02]: Base table or view not found...
File: /app/Models/User.php:123
URL: https://example.com/api/users
User: user123
Time: 2024-01-15 14:30:25
```

## Testing

You can test the SMS notification system using the provided Artisan command:

```bash
# Test in production environment
php artisan test:exception-sms

# Force test in any environment
php artisan test:exception-sms --force
```

## Rate Limiting

To prevent SMS spam, the system implements rate limiting:
- Maximum 5 notifications per hour for the same exception type (configurable)
- Rate limiting is based on exception class + message hash
- Rate limit counters reset after 1 hour

## Troubleshooting

### No SMS Received

1. Check that Twilio is properly configured in your `.env` file
2. Verify admin users have valid phone numbers
3. Check the environment configuration matches your current environment
4. Review Laravel logs for any notification errors

### Too Many Notifications

1. Increase the rate limit value
2. Add more exception types to the exclusion list
3. Consider adjusting the environment restriction

### Notification Failures

The system is designed to be fault-tolerant:
- Notification failures don't break the main application
- All notification attempts are logged
- Failed notifications are logged with details for debugging

## Files Created/Modified

- `app/Notifications/ExceptionOccurredNotification.php` - Notification class
- `app/Services/ExceptionNotificationService.php` - Service handling the logic
- `app/Console/Commands/TestExceptionSmsCommand.php` - Test command
- `bootstrap/app.php` - Exception handler integration
- `config/app.php` - Configuration options
- `.env.example` - Environment variable examples
