// ignore_for_file: constant_identifier_names

import 'package:equatable/equatable.dart';

enum UserRole {
  ROOT,
  APS,
  COMPANION;

  static fromString(String role) {
    switch (role) {
      case 'ROOT':
        return ROOT;
      case 'APS':
        return APS;
      case 'COMPANION':
        return COMPANION;
    }
  }

  String getLabel() {
    return switch (this) {
      UserRole.ROOT => "Système Admin",
      UserRole.APS => "APS",
      UserRole.COMPANION => "Accompagnant",
    };
  }
}

class User extends Equatable {
  final String id;
  final String name;
  final String? email;
  final UserRole role;
  final String phone;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  const User({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.phone,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [id];
}
