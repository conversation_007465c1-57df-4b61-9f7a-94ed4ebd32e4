import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/authentication/domain/repository/fcm_token_repository.dart';

@injectable
class SetFcmToken extends UseCase<MessageResponse, SetFcmTokenParams> {
  final FcmTokenRepository _repository;

  SetFcmToken(this._repository);

  @override
  call(params) {
    return _repository.setFcmToken(fcmToken: params.fcmToken);
  }
}

class SetFcmTokenParams {
  final String fcmToken;

  SetFcmTokenParams({required this.fcmToken});
}
