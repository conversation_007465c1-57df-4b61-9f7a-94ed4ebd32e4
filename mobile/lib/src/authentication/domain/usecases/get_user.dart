import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/authentication/domain/entities/user.dart';
import 'package:s3g/src/authentication/domain/repository/authentication_repository.dart';

@injectable
class GetUser extends UseCase<Stream<User>, NoParams> {
  final AuthenticationRepository _repository;

  GetUser(this._repository);

  @override
  call(NoParams params) {
    return _repository.getUser();
  }
}
