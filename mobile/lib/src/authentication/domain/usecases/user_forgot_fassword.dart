import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/authentication_repository.dart';

@injectable
class UserForgotPassword
    extends UseCase<MessageResponse, ForgotPasswordParams> {
  final AuthenticationRepository _repository;

  UserForgotPassword(this._repository);

  @override
  Future<Either<Failure, MessageResponse>> call(ForgotPasswordParams params) {
    return _repository.forgotPassword(phone: params.phone);
  }
}

class ForgotPasswordParams {
  final String phone;

  ForgotPasswordParams({required this.phone});
}
