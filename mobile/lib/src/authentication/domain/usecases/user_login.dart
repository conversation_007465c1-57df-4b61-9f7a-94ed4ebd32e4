import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/authentication/domain/repository/authentication_repository.dart';

import '../entities/login_response.dart';

@injectable
class UserLogin extends UseCase<LoginResponse, UserLoginParams> {
  final AuthenticationRepository _repository;

  UserLogin(this._repository);

  @override
  call(UserLoginParams params) {
    return _repository.login(phone: params.phone, password: params.password);
  }
}

class UserLoginParams {
  final String phone;
  final String password;

  UserLoginParams({
    required this.phone,
    required this.password,
  });
}
