import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/authentication_repository.dart';

@injectable
class UserResetPassword extends UseCase<MessageResponse, ResetPasswordParams> {
  final AuthenticationRepository _repository;

  UserResetPassword(this._repository);

  @override
  Future<Either<Failure, MessageResponse>> call(ResetPasswordParams params) {
    return _repository.resetPassword(
      phone: params.phone,
      token: params.token,
      password: params.password,
    );
  }
}

class ResetPasswordParams {
  final String phone;
  final String token;
  final String password;

  ResetPasswordParams({
    required this.phone,
    required this.token,
    required this.password,
  });
}
