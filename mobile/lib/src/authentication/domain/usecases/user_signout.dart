import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/authentication/domain/repository/authentication_repository.dart';

@injectable
class UserSignOut extends UseCase<void, NoParams> {
  final AuthenticationRepository _repository;

  UserSignOut(this._repository);

  @override
  Future<Either<Failure, void>> call(NoParams params) {
    return _repository.signOut();
  }
}
