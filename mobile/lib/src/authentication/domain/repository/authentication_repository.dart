import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/authentication/domain/entities/user.dart';

import '../entities/login_response.dart';

abstract class AuthenticationRepository {
  RepositoryResponse<LoginResponse> login({
    required String phone,
    required String password,
  });

  RepositoryResponse<void> signOut();

  RepositoryResponse<Stream<User>> getUser();

  RepositoryResponse<MessageResponse> forgotPassword({
    required String phone,
  });

  RepositoryResponse<MessageResponse> resetPassword({
    required String phone,
    required String token,
    required String password,
  });
}
