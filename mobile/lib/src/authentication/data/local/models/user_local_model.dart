// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:objectbox/objectbox.dart';

import 'package:s3g/src/authentication/domain/entities/user.dart';

@Entity()
class UserLocalModel {
  @Id()
  int oid;

  @Index()
  @Unique()
  String id;

  String name;

  String role;

  String phone;

  String? email;

  String? description;

  @Property(type: PropertyType.date)
  late DateTime createdAt;

  @Property(type: PropertyType.date)
  late DateTime updatedAt;

  UserLocalModel({
    this.oid = 0,
    required this.id,
    required this.name,
    required this.role,
    required this.phone,
    this.email,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  User toEntity() {
    return User(
      id: id,
      name: name,
      role: UserRole.fromString(role),
      phone: phone,
      email: email,
      description: description,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  factory UserLocalModel.fromEntity(User user, {int? oid}) {
    return UserLocalModel(
      oid: oid ?? 0,
      id: user.id,
      name: user.name,
      role: user.role.name,
      phone: user.phone,
      email: user.email,
      description: user.description,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    );
  }
}
