import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';

const _accessTokenKey = 'accessToken';

@injectable
class TokenStorage {
  const TokenStorage(this._secureStorage);

  final FlutterSecureStorage _secureStorage;

  Future<void> saveToken(String accessToken) async {
    await _secureStorage.write(
      key: _accessTokenKey,
      value: accessToken,
    );
  }

  Future<void> removeToken() {
    return _secureStorage.delete(key: _accessTokenKey);
  }

  Future<String?> getToken() async {
    final accessToken = await _secureStorage.read(key: _accessTokenKey);

    return accessToken;
  }
}
