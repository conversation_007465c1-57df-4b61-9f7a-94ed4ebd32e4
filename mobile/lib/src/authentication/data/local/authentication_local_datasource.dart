import 'package:injectable/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/authentication/data/local/models/user_local_model.dart';
import 'package:s3g/src/authentication/domain/entities/user.dart';

abstract class AuthenticationLocalDataSource {
  Future<UserLocalModel?> getUser();

  Future<void> setUser(User user);

  Future<void> deleteUser();
}

@Injectable(as: AuthenticationLocalDataSource)
class AuthenticationLocalDataSourceImpl
    implements AuthenticationLocalDataSource {
  final ObjectBox objectBox;

  AuthenticationLocalDataSourceImpl({required this.objectBox});

  @override
  Future<UserLocalModel?> getUser() async {
    final box = objectBox.store.box<UserLocalModel>();
    final users = await box.getAllAsync();

    return users.firstOrNull;
  }

  @override
  Future<void> setUser(User user) async {
    final box = objectBox.store.box<UserLocalModel>();

    await box.removeAllAsync();
    await box.putAsync(UserLocalModel.fromEntity(user));
  }

  @override
  Future<void> deleteUser() async {
    final box = objectBox.store.box<UserLocalModel>();
    await box.removeAllAsync();
  }
}
