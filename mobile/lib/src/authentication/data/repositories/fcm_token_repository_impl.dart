import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/authentication/domain/entities/fcm_token.dart';
import 'package:s3g/src/authentication/domain/repository/fcm_token_repository.dart';

import '../remote/fcm_token_remote_datasource.dart';

@Injectable(as: FcmTokenRepository)
class FcmTokenRepositoryImpl extends FcmTokenRepository {
  final FcmTokenRemoteDataSource _remoteDataSource;

  FcmTokenRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<FcmToken> getFcmToken() {
    return requestHelper(() => _remoteDataSource.getFcmToken());
  }

  @override
  RepositoryResponse<MessageResponse> setFcmToken({required String fcmToken}) {
    return requestHelper(
      () => _remoteDataSource.setFcmToken(fcmToken: fcmToken),
    );
  }

  @override
  RepositoryResponse<MessageResponse> deleteFcmToken() {
    return requestHelper(
      () => _remoteDataSource.deleteFcmToken(),
    );
  }
}
