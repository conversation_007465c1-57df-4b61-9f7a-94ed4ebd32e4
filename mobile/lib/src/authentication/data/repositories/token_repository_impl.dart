import 'package:injectable/injectable.dart';
import 'package:s3g/core/exceptions/exceptions.dart';
import 'package:s3g/src/authentication/data/local/token_storage.dart';
import 'package:s3g/src/authentication/domain/repository/token_repository.dart';

@Singleton(as: TokenRepository)
class TokenRepositoryImpl extends TokenRepository {
  String? _accessToken;

  final TokenStorage _storage;

  TokenRepositoryImpl(this._storage);

  @override
  Future<String> getAccessToken() async {
    final accessToken = await _storage.getToken();

    if (accessToken == null) {
      throw RequiredAuthTokenException(
        message: 'No token found',
      );
    }

    return accessToken;
  }

  @override
  Future<void> saveToken(String accessToken) async {
    _accessToken = accessToken;
    _storage.saveToken(accessToken);
  }

  @override
  Future<String> getBearerToken() async {
    _accessToken ??= await _storage.getToken();

    return 'Bearer $_accessToken';
  }

  @override
  Future<void> deleteToken() {
    return _storage.removeToken();
  }
}
