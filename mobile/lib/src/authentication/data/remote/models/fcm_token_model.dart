import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/authentication/domain/entities/fcm_token.dart';

part 'fcm_token_model.g.dart';

@JsonSerializable()
class FcmTokenModel extends FcmToken {
  const FcmTokenModel({
    required super.token,
    required super.expired,
  });

  factory FcmTokenModel.fromJson(Map<String, dynamic> json) =>
      _$FcmTokenModelFromJson(json);

  Map<String, dynamic> toJson() => _$FcmTokenModelToJson(this);
}
