// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/authentication/data/remote/models/user_model.dart';
import 'package:s3g/src/authentication/domain/entities/login_response.dart';

part 'login_response_model.g.dart';

@JsonSerializable()
class LoginResponseModel extends LoginResponse {
  @override
  @JsonKey(name: "access_token")
  final String accessToken;

  @override
  final UserModel user;

  const LoginResponseModel({required this.accessToken, required this.user})
      : super(accessToken: accessToken, user: user);

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseModelToJson(this);
}
