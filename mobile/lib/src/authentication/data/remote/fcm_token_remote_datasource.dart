import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/src/authentication/data/remote/models/fcm_token_model.dart';

abstract class FcmTokenRemoteDataSource {
  Future<FcmTokenModel> getFcmToken();

  Future<MessageResponse> setFcmToken({required String fcmToken});

  Future<MessageResponse> deleteFcmToken();
}

@Injectable(as: FcmTokenRemoteDataSource)
class FcmTokenRemoteDataSourceImpl extends FcmTokenRemoteDataSource {
  final Dio _httpClient;

  FcmTokenRemoteDataSourceImpl(this._httpClient);

  @override
  Future<FcmTokenModel> getFcmToken() async {
    final response = await _httpClient.get("/user/fcm-token");

    return FcmTokenModel.fromJson(response.data);
  }

  @override
  Future<MessageResponse> setFcmToken({required String fcmToken}) async {
    final response = await _httpClient.post(
      "/user/fcm-token",
      data: {"fcm_token": fcmToken},
    );

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<MessageResponse> deleteFcmToken() async {
    final response = await _httpClient.delete("/user/fcm-token");

    return MessageResponse.fromJson(response.data);
  }
}
