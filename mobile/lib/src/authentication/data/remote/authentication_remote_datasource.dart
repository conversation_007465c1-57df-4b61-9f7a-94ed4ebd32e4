import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/utils/device_info.dart';
import 'package:s3g/src/authentication/data/remote/models/login_response_model.dart';
import 'package:s3g/src/authentication/data/remote/models/user_model.dart';

abstract class AuthenticationRemoteDataSource {
  Future<LoginResponseModel> login({
    required String phone,
    required String password,
  });

  Future<UserModel> getUser();

  Future<MessageResponse> forgotPassword({
    required String phone,
  });

  Future<MessageResponse> resetPassword({
    required String phone,
    required String token,
    required String password,
  });
}

@Injectable(as: AuthenticationRemoteDataSource)
class AuthenticationRemoteDataSourceImpl
    implements AuthenticationRemoteDataSource {
  final DeviceInfo _deviceInfo;

  final Dio unauthorizedClient;

  final Dio authorizedClient;

  AuthenticationRemoteDataSourceImpl({
    @Named('Unauthorized') required this.unauthorizedClient,
    required this.authorizedClient,
    required DeviceInfo deviceInfo,
  }) : _deviceInfo = deviceInfo;

  @override
  Future<LoginResponseModel> login({
    required String phone,
    required String password,
  }) async {
    final deviceName = await _deviceInfo.deviceName();

    final body = {
      'phone': phone,
      'password': password,
      'device_name': deviceName,
    };

    final response = await unauthorizedClient.post('/auth/token', data: body);

    return LoginResponseModel.fromJson(response.data);
  }

  @override
  Future<UserModel> getUser() async {
    final response = await authorizedClient.get('/user');

    return UserModel.fromJson(response.data['data']);
  }

  @override
  Future<MessageResponse> forgotPassword({required String phone}) async {
    final body = {'phone': phone};

    final response = await unauthorizedClient.post(
      '/password/forgot',
      data: body,
    );

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<MessageResponse> resetPassword({
    required String phone,
    required String token,
    required String password,
  }) async {
    final body = {
      'phone': phone,
      'token': token,
      'password': password,
    };

    final response = await unauthorizedClient.post(
      '/password/reset',
      data: body,
    );

    return MessageResponse.fromJson(response.data);
  }
}
