import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';

import '../../../domain/usecases/user_reset_password.dart';

part 'reset_password_state.dart';

@injectable
class ResetPasswordCubit extends Cubit<ResetPasswordState> {
  final UserResetPassword _userResetPassword;

  ResetPasswordCubit(this._userResetPassword) : super(ResetPasswordInitial());

  void resetPassword({
    required String phone,
    required String token,
    required String password,
  }) async {
    emit(ResetPasswordLoading());

    final response = await _userResetPassword(ResetPasswordParams(
      phone: phone,
      token: token,
      password: password,
    ));

    response.fold(
      (failure) => emit(ResetPasswordFailure(failure.message)),
      (success) => emit(ResetPasswordSuccess(success.message)),
    );
  }
}
