import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';

import '../../../domain/usecases/user_forgot_fassword.dart';

part 'forgot_password_state.dart';

@injectable
class ForgotPasswordCubit extends Cubit<ForgotPasswordState> {
  final UserForgotPassword _userForgotPassword;

  ForgotPasswordCubit(this._userForgotPassword)
      : super(ForgotPasswordInitial());

  void forgotPassword(String phone) async {
    emit(ForgotPasswordLoading());

    final response = await _userForgotPassword(
      ForgotPasswordParams(phone: phone),
    );

    response.fold(
      (failure) => emit(ForgotPasswordFailure(failure.message)),
      (success) => emit(ForgotPasswordSuccess(success.message)),
    );
  }
}
