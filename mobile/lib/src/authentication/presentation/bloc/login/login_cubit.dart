import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/authentication/domain/usecases/user_login.dart';

import '../../../domain/entities/user.dart';

part 'login_state.dart';

@injectable
class LoginCubit extends Cubit<LoginState> {
  final UserLogin _userLogin;

  LoginCubit(this._userLogin) : super(LoginInitial());

  Future<void> login({required String phone, required String password}) async {
    emit(LoginLoading());

    final response = await _userLogin(
      UserLoginParams(phone: phone, password: password),
    );

    response.fold(
      (failure) => emit(LoginFailure(failure.message)),
      (success) => emit(LoginSuccess(success.user)),
    );
  }
}
