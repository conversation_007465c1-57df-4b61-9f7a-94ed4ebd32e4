import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/authentication/authentication.dart';

part 'authenticated_state.dart';

@singleton
class AuthenticatedCubit extends Cubit<AuthenticatedState> {
  final GetUser _getUser;

  StreamSubscription<User>? _userSubscription;

  AuthenticatedCubit(this._getUser) : super(const AuthenticatedState());

  void checkUserAuthenticated() async {
    final user = await _getUser(NoParams());

    await _userSubscription?.cancel();

    user.fold(
      (failure) => emit(const AuthenticatedState(
        status: AuthenticatedStatus.unauthenticated,
        user: null,
      )),
      (user) {
        _userSubscription = user.listen((user) => authenticated(user));
      },
    );
  }

  void authenticated(User user) {
    emit(AuthenticatedState(
      status: AuthenticatedStatus.authenticated,
      user: user,
    ));
  }

  void unauthenticated() {
    emit(const AuthenticatedState(
      status: AuthenticatedStatus.unauthenticated,
      user: null,
    ));
  }

  void dispose() async {
    await _userSubscription?.cancel();
  }
}
