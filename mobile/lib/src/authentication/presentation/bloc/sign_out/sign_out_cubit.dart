import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/authentication/authentication.dart';

part 'sign_out_state.dart';

@injectable
class SignOutCubit extends Cubit<SignOutState> {
  final UserSignOut _userSignOut;

  SignOutCubit(this._userSignOut) : super(const SignOutState());

  void logout() async {
    emit(const SignOutState(status: SignOutStatus.progress));

    final response = await _userSignOut(NoParams());

    response.fold(
      (failure) => emit(SignOutState(
        status: SignOutStatus.failure,
        message: failure.message,
      )),
      (_) => emit(const SignOutState(
        status: SignOutStatus.success,
      )),
    );
  }
}
