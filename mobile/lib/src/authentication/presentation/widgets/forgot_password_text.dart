import 'package:flutter/material.dart';

@immutable
class ForgotPasswordText extends StatelessWidget {
  final void Function()? onForgotPasswordTap;
  final void Function()? onResetPasswordTap;

  const ForgotPasswordText({
    super.key,
    this.onForgotPasswordTap,
    this.onResetPasswordTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text("Mot de passe oublié ?"),
        const Sized<PERSON><PERSON>(height: 10),
        InkWell(
          onTap: onForgotPasswordTap,
          child: Text(
            'Obtenir un code',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: onResetPasswordTap,
          child: Text(
            'Réinitialiser le mot de passe',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      ],
    );
  }
}
