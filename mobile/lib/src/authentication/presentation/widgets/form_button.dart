import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';

class FormButton extends StatelessWidget {
  final String text;
  final void Function()? onPressed;
  final bool loading;

  const FormButton({
    super.key,
    this.onPressed,
    required this.text,
    this.loading = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 65,
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: const EdgeInsets.only(top: 20.0),
        child: FilledButton(
          onPressed: loading ? null : onPressed,
          child: loading
              ? const GFLoader()
              : Text(
                  text,
                  style: const TextStyle(
                    color: Colors.white,
                  ),
                ),
        ),
      ),
    );
  }
}
