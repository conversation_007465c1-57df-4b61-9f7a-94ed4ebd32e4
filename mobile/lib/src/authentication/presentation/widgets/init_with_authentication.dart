import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/authentication/domain/usecases/set_fcm_token.dart';
import 'package:s3g/src/user_notification/user_notification.dart';

class InitWithAuthentication extends StatelessWidget {
  final Widget? child;

  const InitWithAuthentication({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticatedCubit, AuthenticatedState>(
      listener: (_, state) async {
        // Set FCM Token
        if (state.status == AuthenticatedStatus.authenticated &&
            state.user != null) {
          final fcmToken = await FirebaseMessagingService().getToken();

          if (fcmToken != null) {
            final setFcmToken = getIt<SetFcmToken>();

            setFcmToken(SetFcmTokenParams(fcmToken: fcmToken));
          }
        }
      },
      child: child,
    );
  }
}
