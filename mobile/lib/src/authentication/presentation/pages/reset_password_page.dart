import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/authentication/presentation/widgets/text_header.dart';

import '../bloc/reset_password/reset_password_cubit.dart';
import '../widgets/form_button.dart';

class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key});

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: GFIconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () => context.pop(),
          type: GFButtonType.transparent,
        ),
        centerTitle: true,
        title: const Text("Reinitialiser Mot de passe"),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: bodyPadding,
          physics: const ScrollPhysics(),
          child: BlocProvider(
            create: (context) => getIt<ResetPasswordCubit>(),
            child: const FormResetPassword(),
          ),
        ),
      ),
    );
  }
}

class FormResetPassword extends StatefulWidget {
  const FormResetPassword({super.key});

  @override
  State<FormResetPassword> createState() => _FormResetPasswordState();
}

class _FormResetPasswordState extends State<FormResetPassword> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          // Text Header
          const TextHeaderAuth(
            text:
                "Avant de remplir les champs ci-dessous, assurez-vous d'avoir fait une requête d'un code de réinitialisation.",
          ),

          // Phone field
          FormBuilderTextField(
            name: 'phone',
            keyboardType: TextInputType.phone,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Numéro de téléphone',
              hintText: "Entrez votre numéro de téléphone",
            ),
          ),

          // Some space
          columnSizedBox,

          // Code field
          FormBuilderTextField(
            name: 'token',
            keyboardType: TextInputType.number,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
              FormBuilderValidators.minLength(6),
              FormBuilderValidators.numeric(),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Code de réinitialisation',
              hintText: "Entrez votre Code de réinitialisation",
            ),
          ),

          // Some space
          columnSizedBox,

          // Password field
          FormBuilderTextField(
            name: 'password',
            obscureText: true,
            keyboardType: TextInputType.visiblePassword,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
              FormBuilderValidators.minLength(3),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Nouveau mot de passe',
              hintText: "Entrez votre nouveau mot de passe",
            ),
          ),

          // Show error message
          BlocListener<ResetPasswordCubit, ResetPasswordState>(
            listener: (_, state) {
              switch (state) {
                case ResetPasswordSuccess():
                  showToast(
                    context,
                    message: state.message,
                    type: ToastType.success,
                  );

                  _formKey.currentState?.reset();
                  context.pop();
                  break;

                case ResetPasswordFailure():
                  showToast(
                    context,
                    message: state.message,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          ),

          // BlocBuilder on submit
          BlocBuilder<ResetPasswordCubit, ResetPasswordState>(
            builder: (_, state) {
              return FormButton(
                text: 'Réinitialiser',
                loading: state is ResetPasswordLoading,
                onPressed: () {
                  final isValid = _formKey.currentState?.saveAndValidate();
                  final value = _formKey.currentState?.value;

                  if (isValid != null && isValid && value != null) {
                    final phone = value['phone'] as String;
                    final token = value['token'] as String;
                    final password = value['password'] as String;

                    context.read<ResetPasswordCubit>().resetPassword(
                          phone: phone,
                          token: token,
                          password: password,
                        );
                  }
                },
              );
            },
          )
        ],
      ),
    );
  }
}
