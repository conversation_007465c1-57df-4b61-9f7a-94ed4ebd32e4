import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/authentication/presentation/bloc/login/login_cubit.dart';

import '../../authentication.dart';
import '../widgets/forgot_password_text.dart';
import '../widgets/form_button.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: GFIconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () => context.pop(),
          type: GFButtonType.transparent,
        ),
        centerTitle: true,
        title: const Text("Se connecter"),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: bodyPadding,
          physics: const ScrollPhysics(),
          child: BlocProvider(
            create: (context) => getIt<LoginCubit>(),

            // BlocListener on state
            child: BlocListener<LoginCubit, LoginState>(
              listener: (context, state) {
                switch (state) {
                  case LoginSuccess():
                    context.read<AuthenticatedCubit>()
                      ..authenticated(state.user)
                      ..checkUserAuthenticated();

                    context.go(AppRoute.manager);
                    break;

                  case LoginFailure():
                    showToast(
                      context,
                      message: state.error,
                      type: ToastType.error,
                    );
                    break;

                  default:
                }
              },

              // LoginForm
              child: LoginForm(),
            ),
          ),
        ),
      ),
    );
  }
}

class LoginForm extends StatelessWidget {
  LoginForm({super.key});

  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        children: <Widget>[
          // S3G logo
          Padding(
            padding: const EdgeInsets.only(top: 50.0, bottom: 20),
            child: Center(
              child: SizedBox(
                width: 200,
                height: 100,
                child: Image.asset('assets/s3g.png'),
              ),
            ),
          ),

          // Phone number text field
          FormBuilderTextField(
            name: 'phone',
            keyboardType: TextInputType.phone,
            autocorrect: false,
            enableSuggestions: false,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Numéro de téléphone',
              hintText: "Entrez votre numéro de téléphone",
            ),
          ),

          // Some space
          columnSizedBox,

          // Password text field
          FormBuilderTextField(
            name: 'password',
            obscureText: true,
            keyboardType: TextInputType.visiblePassword,
            autocorrect: false,
            enableSuggestions: false,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
              FormBuilderValidators.minLength(3),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: "Mot de passe",
              hintText: "Entrez votre mot de passe",
            ),
          ),

          // BlocBuilder on submit
          BlocBuilder<LoginCubit, LoginState>(
            builder: (_, state) {
              return FormButton(
                text: 'Connexion',
                loading: state is LoginLoading || state is LoginSuccess,
                onPressed: () {
                  final isValid = _formKey.currentState?.saveAndValidate();
                  final value = _formKey.currentState?.value;

                  if (isValid != null && isValid && value != null) {
                    final phone = value['phone'] as String;
                    final password = value['password'] as String;

                    context.read<LoginCubit>().login(
                          phone: phone,
                          password: password,
                        );
                  }
                },
              );
            },
          ),

          // Some space
          const SizedBox(
            height: 50,
          ),

          // Forgot password texts
          Center(
            child: ForgotPasswordText(
              onForgotPasswordTap: () {
                context.push(AppRoute.forgotPassword);
              },
              onResetPasswordTap: () {
                context.push(AppRoute.resetPassword);
              },
            ),
          ),
        ],
      ),
    );
  }
}
