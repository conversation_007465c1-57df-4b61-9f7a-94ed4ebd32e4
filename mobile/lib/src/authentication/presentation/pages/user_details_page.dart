import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/authentication/authentication.dart';

import '../bloc/sign_out/sign_out_cubit.dart';

class UserDetailsPage extends StatelessWidget {
  const UserDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final user = context.read<AuthenticatedCubit>().state.user!;

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Profil"),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: bodyPadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              columnSizedBox,
              DetailTitle(
                leading: InitialIcon(
                  text: user.name,
                ),
                titleText: user.name,
              ),
              columnSizedBox,
              const GreyDivider(),

              // Account
              const SizedBox(height: 26.0),
              Text("Compte", style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 26.0),

              // Email
              if (user.email != null) ...[
                DetailTitle(
                  leading: const Icon(Icons.email),
                  titleText: user.email,
                ),
                columnSizedBox,
                const GreyDivider(),
                columnSizedBox,
              ],

              // Phone
              DetailTitle(
                leading: const Icon(Icons.phone),
                titleText: user.phone,
              ),
              columnSizedBox,
              const GreyDivider(),
              columnSizedBox,

              // Role
              DetailTitle(
                leading: const Icon(Icons.rule),
                titleText: user.role.getLabel(),
              ),
              columnSizedBox,
              const GreyDivider(),
              columnSizedBox,

              // Created at
              DetailTitle(
                leading: const Icon(Icons.date_range),
                title: LocalTimeago(
                  date: user.createdAt,
                  builder: (_, value) => Text(
                    value,
                    style: const TextStyle(fontSize: 20.0),
                  ),
                ),
              ),
              columnSizedBox,
              const GreyDivider(),
              columnSizedBox,

              // Description
              if (user.description != null) ...[
                DetailTitle(
                  leading: const Icon(Icons.description),
                  titleText: user.description,
                ),
                columnSizedBox,
                const GreyDivider(),
                columnSizedBox,
              ],

              BlocListener<SignOutCubit, SignOutState>(
                listener: (_, state) {
                  if (state.status == SignOutStatus.success) {
                    context.go(AppRoute.advice);
                    context.read<AuthenticatedCubit>().unauthenticated();
                  } else if (state.status == SignOutStatus.failure) {
                    showToast(
                      context,
                      message: state.message ?? "",
                      type: ToastType.error,
                    );
                  }
                },
                child: const SizedBox.shrink(),
              ),

              // Disconnect
              BlocBuilder<SignOutCubit, SignOutState>(
                builder: (context, state) {
                  return DetailTitle(
                    leading: state.status == SignOutStatus.progress
                        ? const GFLoader()
                        : const Icon(Icons.logout),
                    onTap: state.status == SignOutStatus.progress
                        ? null
                        : () {
                            context.read<SignOutCubit>().logout();
                          },
                    title: Text(
                      "Déconnexion",
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 20.0,
                      ),
                    ),
                  );
                },
              ),

              BlocListener<SignOutCubit, SignOutState>(
                listener: (_, state) {
                  if (state.status == SignOutStatus.failure) {
                    showToast(
                      context,
                      type: ToastType.error,
                      message: state.message ?? "",
                    );
                  }
                },
                child: const SizedBox.shrink(),
              )
            ],
          ),
        ),
      ),
    );
  }
}
