import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';

import '../bloc/forgot_password/forgot_password_cubit.dart';
import '../widgets/form_button.dart';
import '../widgets/text_header.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: GFIconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () => context.pop(),
          type: GFButtonType.transparent,
        ),
        centerTitle: true,
        title: const Text("Mot de passe oublié"),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: bodyPadding,
          physics: const ScrollPhysics(),
          child: BlocProvider(
            create: (context) => getIt<ForgotPasswordCubit>(),
            child: const ForgotPasswordForm(),
          ),
        ),
      ),
    );
  }
}

class ForgotPasswordForm extends StatefulWidget {
  const ForgotPasswordForm({super.key});

  @override
  State<ForgotPasswordForm> createState() => _ForgotPasswordFormState();
}

class _ForgotPasswordFormState extends State<ForgotPasswordForm> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          const TextHeaderAuth(
            text:
                "Entrez votre numéro de téléphone et nous vous enverrons des instructions pour réinitialiser votre mot de passe.",
          ),

          // Phone field
          FormBuilderTextField(
            name: 'phone',
            keyboardType: TextInputType.phone,
            autocorrect: false,
            enableSuggestions: false,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Numéro de téléphone',
              hintText: "Entrez votre numéro de téléphone",
            ),
          ),

          // BlocBuilder on submit
          BlocBuilder<ForgotPasswordCubit, ForgotPasswordState>(
            builder: (_, state) {
              return FormButton(
                text: 'Obtenir un code',
                loading: state is ForgotPasswordLoading,
                onPressed: () {
                  final isValid = _formKey.currentState?.saveAndValidate();
                  final value = _formKey.currentState?.value;

                  if (isValid != null && isValid && value != null) {
                    context.read<ForgotPasswordCubit>().forgotPassword(
                          value['phone'] as String,
                        );
                  }
                },
              );
            },
          ),

          // Show Toast
          BlocListener<ForgotPasswordCubit, ForgotPasswordState>(
            listener: (context, state) {
              switch (state) {
                case ForgotPasswordSuccess():
                  showToast(
                    context,
                    message: state.message,
                    type: ToastType.success,
                  );

                  _formKey.currentState?.reset();
                  context.pop();
                  break;

                case ForgotPasswordFailure():
                  showToast(
                    context,
                    message: state.message,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          )
        ],
      ),
    );
  }
}
