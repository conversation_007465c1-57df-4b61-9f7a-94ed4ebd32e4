import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';

abstract class FollowupRepository {
  RepositoryResponse<FollowupShow> getFollowup({
    required String companionId,
    required String followupId,
  });

  RepositoryResponse<Paginated<Followup>> getFollowupList({
    required String companionId,
    int? page,
  });

  RepositoryResponse<FollowupShow> createFollowup({
    required String companionId,
    required String title,
    required String description,
  });

  RepositoryResponse<FollowupShow> updateFollowup({
    required String companionId,
    required String followupId,
    required String title,
    required String description,
  });

  RepositoryResponse<MessageResponse> deleteFollowup({
    required String companionId,
    required String followupId,
  });
}
