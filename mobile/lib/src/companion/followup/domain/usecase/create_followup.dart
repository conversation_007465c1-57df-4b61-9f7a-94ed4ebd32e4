import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';

import '../repository/followup_repository.dart';

@injectable
class CreateFollowup extends UseCase<FollowupShow, CreateFollowupParams> {
  final FollowupRepository repository;

  CreateFollowup(this.repository);

  @override
  call(CreateFollowupParams params) {
    return repository.createFollowup(
      companionId: params.companionId,
      title: params.title,
      description: params.description,
    );
  }
}

class CreateFollowupParams {
  final String companionId;
  final String title;
  final String description;

  CreateFollowupParams({
    required this.companionId,
    required this.title,
    required this.description,
  });
}
