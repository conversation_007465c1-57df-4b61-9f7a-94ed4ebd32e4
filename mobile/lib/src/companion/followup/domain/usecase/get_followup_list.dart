// ignore_for_file: overridden_fields

import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';

import '../repository/followup_repository.dart';

@injectable
class GetFollowupList
    extends UseCase<Paginated<Followup>, GetFollowupListParams> {
  final FollowupRepository repository;

  GetFollowupList({required this.repository});

  @override
  call(params) {
    return repository.getFollowupList(
      page: params.page,
      companionId: params.companionId,
    );
  }
}

class GetFollowupListParams extends PaginationParams {
  @override
  final int? page;

  final String companionId;

  GetFollowupListParams({
    this.page,
    required this.companionId,
  }) : super(page: page);
}
