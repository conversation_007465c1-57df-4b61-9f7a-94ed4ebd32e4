import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';

import '../repository/followup_repository.dart';

@injectable
class UpdateFollowup extends UseCase<FollowupShow, UpdateFollowupParams> {
  final FollowupRepository repository;

  UpdateFollowup(this.repository);

  @override
  call(UpdateFollowupParams params) {
    return repository.updateFollowup(
      companionId: params.companionId,
      followupId: params.followupId,
      title: params.title,
      description: params.description,
    );
  }
}

class UpdateFollowupParams {
  final String companionId;
  final String followupId;
  final String title;
  final String description;

  UpdateFollowupParams({
    required this.companionId,
    required this.followupId,
    required this.title,
    required this.description,
  });
}
