import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/followup_repository.dart';

@injectable
class DeleteFollowup extends UseCase<MessageResponse, DeleteFollowupParams> {
  final FollowupRepository _repository;

  DeleteFollowup(this._repository);

  @override
  call(DeleteFollowupParams params) {
    return _repository.deleteFollowup(
      companionId: params.companionId,
      followupId: params.followupId,
    );
  }
}

class DeleteFollowupParams {
  final String companionId;
  final String followupId;

  DeleteFollowupParams({required this.companionId, required this.followupId});
}
