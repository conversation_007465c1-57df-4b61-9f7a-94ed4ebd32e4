import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';
import 'package:s3g/src/companion/followup/domain/usecase/get_followup.dart';

part 'get_followup_state.dart';

class GetFollowupCubit extends Cubit<GetFollowupState> {
  final GetFollowup getFollowupUseCase;
  final String companionId;

  GetFollowupCubit(
    this.companionId, {
    required this.getFollowupUseCase,
  }) : super(GetFollowupInitial());

  void getFollowup({required String followupId}) async {
    emit(GetFollowupLoading());

    final result = await getFollowupUseCase(
      GetFollowupParams(companionId: companionId, followupId: followupId),
    );

    result.fold(
      (l) => emit(GetFollowupFailure(message: l.message)),
      (r) => emit(GetFollowupSuccess(r)),
    );
  }
}
