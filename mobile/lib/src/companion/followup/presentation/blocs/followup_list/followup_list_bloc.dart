import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/companion/followup/domain/usecase/get_followup_list.dart';
import 'package:s3g/src/companion/followup/followup.dart';

class FollowupListBloc extends PaginatedItemBloc<Followup> {
  final GetFollowupList getFollowupListUseCase;
  final String companionId;

  FollowupListBloc(
    this.companionId, {
    required this.getFollowupListUseCase,
  }) : super();

  @override
  callUseCase(PaginationParams params) {
    return getFollowupListUseCase(
      GetFollowupListParams(
        companionId: companionId,
        page: params.page,
      ),
    );
  }
}
