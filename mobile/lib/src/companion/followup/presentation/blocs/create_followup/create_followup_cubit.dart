import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/companion/followup/domain/usecase/create_followup.dart';
import 'package:s3g/src/companion/followup/followup.dart';

part 'create_followup_state.dart';

@injectable
class CreateFollowupCubit extends Cubit<CreateFollowupState> {
  final CreateFollowup _createFollowup;

  CreateFollowupCubit(this._createFollowup) : super(CreateFollowupInitial());

  Future<void> createFollowup(CreateFollowupParams params) async {
    emit(CreateFollowupLoading());

    final result = await _createFollowup(params);

    result.fold(
      (l) => emit(CreateFollowupFailure(message: l.message)),
      (r) => emit(CreateFollowupSuccess(followup: r)),
    );
  }
}
