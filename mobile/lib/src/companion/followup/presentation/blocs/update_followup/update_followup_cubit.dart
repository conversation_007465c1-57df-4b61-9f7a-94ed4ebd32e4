import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/companion/followup/followup.dart';

import '../../../domain/usecase/update_followup.dart';

part 'update_followup_state.dart';

@injectable
class UpdateFollowupCubit extends Cubit<UpdateFollowupState> {
  final UpdateFollowup _updateFollowup;

  UpdateFollowupCubit(this._updateFollowup) : super(UpdateFollowupInitial());

  Future<void> updateFollowup(UpdateFollowupParams params) async {
    emit(UpdateFollowupLoading());

    final result = await _updateFollowup(params);

    result.fold(
      (l) => emit(UpdateFollowupError(message: l.message)),
      (r) => emit(UpdateFollowupSuccess(followup: r)),
    );
  }
}
