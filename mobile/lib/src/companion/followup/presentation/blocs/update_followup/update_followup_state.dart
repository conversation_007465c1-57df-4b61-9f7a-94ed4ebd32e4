part of 'update_followup_cubit.dart';

@immutable
sealed class UpdateFollowupState {}

final class UpdateFollowupInitial extends UpdateFollowupState {}

final class UpdateFollowupLoading extends UpdateFollowupState {}

final class UpdateFollowupError extends UpdateFollowupState {
  final String message;

  UpdateFollowupError({required this.message});
}

final class UpdateFollowupSuccess extends UpdateFollowupState {
  final FollowupShow followup;

  UpdateFollowupSuccess({required this.followup});
}
