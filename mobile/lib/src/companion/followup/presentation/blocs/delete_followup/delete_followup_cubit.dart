import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/companion/followup/domain/usecase/delete_followup.dart';

part 'delete_followup_state.dart';

class DeleteFollowupCubit extends Cubit<DeleteFollowupState> {
  final DeleteFollowup _deleteFollowupUseCase;
  final String companionId;

  DeleteFollowupCubit(
    this._deleteFollowupUseCase, {
    required this.companionId,
  }) : super(DeleteFollowupInitial());

  Future<void> deleteFollowup(String followupId) async {
    emit(DeleteFollowupLoading());

    final result = await _deleteFollowupUseCase(
      DeleteFollowupParams(
        companionId: companionId,
        followupId: followupId,
      ),
    );

    result.fold(
      (failure) => emit(DeleteFollowupFailure(message: failure.message)),
      (success) => emit(DeleteFollowupSuccess(message: success.message)),
    );
  }
}
