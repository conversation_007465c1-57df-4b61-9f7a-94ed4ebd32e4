import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/companion/companion/companion.dart';
import 'package:s3g/src/companion/followup/presentation/widgets/followup_form.dart';

import '../../domain/entity/followup.dart';
import '../blocs/delete_followup/delete_followup_cubit.dart';
import '../blocs/followup_list/followup_list_bloc.dart';
import '../widgets/paginated_items.dart';

class FollowupListPage extends StatelessWidget {
  const FollowupListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final companion =
        (context.watch<CompanionShowCubit>().state as CompanionShowLoaded)
            .companion;

    return MultiBlocProvider(
      key: ValueKey(companion.id),
      providers: [
        // Followup List Cubit
        BlocProvider(
          create: (_) => FollowupListBloc(
            companion.id,
            getFollowupListUseCase: getIt(),
          )..add(PaginatedItemFetched()),
        ),

        // Followup Form Cubit
        BlocProvider(
          create: (_) => InlineFormCubit<Followup>(),
        ),

        // Followup Form Cubit
        BlocProvider(
          create: (_) => DeleteFollowupCubit(
            getIt(),
            companionId: companion.id,
          ),
        ),
      ], // Content
      child: _FollowupListPageContent(companion),
    );
  }
}

class _FollowupListPageContent extends StatefulWidget {
  final Companion companion;

  const _FollowupListPageContent(this.companion);

  @override
  State<_FollowupListPageContent> createState() =>
      _FollowupListPageContentState();
}

class _FollowupListPageContentState extends State<_FollowupListPageContent> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          context
              .read<FollowupListBloc>()
              .add(PaginatedItemFetched(refresh: true));
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          controller: _scrollController,
          padding: bodyPadding,

          // Content
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const PageTitle(title: "Suivis"),

              // Form
              BlocBuilder<InlineFormCubit<Followup>, InlineFormState<Followup>>(
                builder: (_, state) {
                  if (state.enabled) {
                    return FollowupForm(
                      companion: widget.companion,
                      editable: state.editable,
                      closeEditMode: () {
                        context.read<InlineFormCubit<Followup>>().disableForm();
                      },
                    );
                  }

                  return PaginatedItems(
                    scrollController: _scrollController,
                  );
                },
              ),
            ],
          ),
        ),
      ),

      // FAB Button
      floatingActionButton:
          BlocBuilder<InlineFormCubit<Followup>, InlineFormState<Followup>>(
        builder: (_, state) {
          if (state.enabled) {
            return const SizedBox.shrink();
          }

          return FloatingActionButton(
            heroTag: "FollowupListPage",
            onPressed: () {
              context.read<InlineFormCubit<Followup>>().enableForm();
            },
            child: const Icon(Icons.add),
          );
        },
      ),
    );
  }
}
