import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/companion/companion/companion.dart';
import 'package:s3g/src/companion/followup/presentation/widgets/followup_form.dart';

import '../../domain/entity/followup.dart';
import '../blocs/delete_followup/delete_followup_cubit.dart';
import '../blocs/followup_list/followup_list_bloc.dart';
import '../widgets/paginated_items.dart';

class FollowupListPage extends StatelessWidget {
  const FollowupListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final companion =
        (context.watch<CompanionShowCubit>().state as CompanionShowLoaded)
            .companion;

    return MultiBlocProvider(
      key: ValueKey(companion.id),
      providers: [
        // Followup List Cubit
        BlocProvider(
          create: (_) => FollowupListBloc(
            companion.id,
            getFollowupListUseCase: getIt(),
          )..add(PaginatedItemFetched()),
        ),

        // Followup Form Cubit
        BlocProvider(
          create: (_) => InlineFormCubit<Followup>(),
        ),

        // Followup Form Cubit
        BlocProvider(
          create: (_) => DeleteFollowupCubit(
            getIt(),
            companionId: companion.id,
          ),
        ),
      ], // Content
      child: _FollowupListPageContent(companion),
    );
  }
}

class _FollowupListPageContent extends StatefulWidget {
  final Companion companion;

  const _FollowupListPageContent(this.companion);

  @override
  State<_FollowupListPageContent> createState() =>
      _FollowupListPageContentState();
}

class _FollowupListPageContentState extends State<_FollowupListPageContent> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: RefreshIndicator(
        color: AppTheme.primary,
        onRefresh: () async {
          context
              .read<FollowupListBloc>()
              .add(PaginatedItemFetched(refresh: true));
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          controller: _scrollController,
          padding: bodyPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Modern Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(cardRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.04),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: const Color(0xFF059669).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.assignment_outlined,
                        color: Color(0xFF059669),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Suivis médicaux",
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            "Historique et gestion des suivis",
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Content
              BlocBuilder<InlineFormCubit<Followup>, InlineFormState<Followup>>(
                builder: (_, state) {
                  if (state.enabled) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(cardRadius),
                        border: Border.all(
                          color: const Color(0xFF3B82F6).withValues(alpha: 0.2),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.04),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: FollowupForm(
                        companion: widget.companion,
                        editable: state.editable,
                        closeEditMode: () {
                          context
                              .read<InlineFormCubit<Followup>>()
                              .disableForm();
                        },
                      ),
                    );
                  }

                  return PaginatedItems(
                    scrollController: _scrollController,
                  );
                },
              ),
            ],
          ),
        ),
      ),

      // Modern FAB
      floatingActionButton:
          BlocBuilder<InlineFormCubit<Followup>, InlineFormState<Followup>>(
        builder: (_, state) {
          if (state.enabled) {
            return const SizedBox.shrink();
          }

          return FloatingActionButton(
            heroTag: "FollowupListPage",
            backgroundColor: AppTheme.primary,
            foregroundColor: Colors.white,
            onPressed: () {
              final parentHeight = MediaQuery.of(context).size.height * 0.7;

              showMaterialModalBottomSheet(
                context: context,
                shape: bottomSheetRadius,
                builder: (_) => BlocProvider.value(
                  value: context.read<FollowupListBloc>(),
                  child: SizedBox(
                    height: parentHeight,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 20,
                      ),
                      child: FollowupForm(
                        companion: widget.companion,
                        editable: null,
                        closeEditMode: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ),
                ),
              );
            },
            child: const Icon(Icons.add),
          );
        },
      ),
    );
  }
}
