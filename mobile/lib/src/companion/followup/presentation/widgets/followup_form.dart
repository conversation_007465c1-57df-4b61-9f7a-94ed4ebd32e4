import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/companion/companion/companion.dart';
import 'package:s3g/src/companion/followup/domain/usecase/create_followup.dart';
import 'package:s3g/src/companion/followup/followup.dart';
import 'package:s3g/src/companion/followup/presentation/blocs/create_followup/create_followup_cubit.dart';

import '../../domain/usecase/update_followup.dart';
import '../blocs/followup_list/followup_list_bloc.dart';
import '../blocs/get_followup/get_followup_cubit.dart';
import '../blocs/update_followup/update_followup_cubit.dart';

class FollowupForm extends StatelessWidget {
  final Companion companion;
  final Followup? editable;
  final VoidCallback closeEditMode;

  const FollowupForm({
    super.key,
    required this.companion,
    required this.closeEditMode,
    this.editable,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      key: ValueKey(companion.id),
      providers: [
        // Bloc to create the followup
        BlocProvider(
          create: (_) => getIt<CreateFollowupCubit>(),
        ),

        // Bloc to update the followup
        BlocProvider(
          create: (_) => getIt<UpdateFollowupCubit>(),
        ),

        // Bloc to get the followup
        BlocProvider(
          create: (_) {
            return GetFollowupCubit(
              companion.id,
              getFollowupUseCase: getIt(),
            );
          },
        )
      ],

      // Create the form
      child: _FollowupFormContent(
        closeEditMode: closeEditMode,
        companion: companion,
        editable: editable,
      ),
    );
  }
}

class _FollowupFormContent extends StatefulWidget {
  final Companion companion;
  final VoidCallback closeEditMode;
  final Followup? editable;

  const _FollowupFormContent({
    required this.closeEditMode,
    required this.companion,
    this.editable,
  });

  @override
  State<_FollowupFormContent> createState() => _FollowupFormContentState();
}

class _FollowupFormContentState extends State<_FollowupFormContent> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  void initState() {
    if (widget.editable != null) {
      context
          .read<GetFollowupCubit>()
          .getFollowup(followupId: widget.editable!.id);
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.editable != null ? "Modifier un suivi" : "Ajouté un suivi",
            style: const TextStyle(fontSize: 18),
          ),
          columnSizedBox,

          // title field
          FormBuilderTextField(
            name: 'title',
            minLines: 1,
            maxLines: 10,
            initialValue: widget.editable?.title,
            keyboardType: TextInputType.text,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
              FormBuilderValidators.min(2),
              FormBuilderValidators.min(150),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Sujet',
              hintText: "Entrez le sujet",
            ),
          ),

          // Some space
          columnSizedBox,

          // description field
          BlocBuilder<GetFollowupCubit, GetFollowupState>(
            builder: (_, state) {
              String? description;

              if (state is GetFollowupSuccess) {
                description = state.followup.description;
              }

              return FormBuilderTextField(
                key: ValueKey(description ?? 'description'),
                name: 'description',
                minLines: 3,
                maxLines: 10,
                initialValue: description,
                keyboardType: TextInputType.multiline,
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                  FormBuilderValidators.min(10),
                ]),
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Description',
                  hintText: "Entrez la description",
                ),
              );
            },
          ),

          // Some space
          columnSizedBox,

          // Create or update buttons
          if (widget.editable != null)
            BlocBuilder<UpdateFollowupCubit, UpdateFollowupState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is UpdateFollowupLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          if (widget.editable == null)
            BlocBuilder<CreateFollowupCubit, CreateFollowupState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is CreateFollowupLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          // Show error message on create
          BlocListener<CreateFollowupCubit, CreateFollowupState>(
            listener: (_, state) {
              switch (state) {
                case CreateFollowupSuccess():
                  widget.closeEditMode();
                  _formKey.currentState?.reset();

                  // Refresh the list
                  context
                      .read<FollowupListBloc>()
                      .add(PaginatedItemFetched(refresh: true));
                  break;

                case CreateFollowupFailure(message: String message):
                  showToast(
                    context,
                    message: message,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          ),

          // Show error message on edit
          BlocListener<UpdateFollowupCubit, UpdateFollowupState>(
            listener: (_, state) {
              switch (state) {
                case UpdateFollowupSuccess():
                  widget.closeEditMode();
                  _formKey.currentState?.reset();

                  // Refresh the list
                  context
                      .read<FollowupListBloc>()
                      .add(PaginatedItemFetched(refresh: true));
                  break;

                case UpdateFollowupError(message: String message):
                  showToast(
                    context,
                    message: message,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          )
        ],
      ),
    );
  }

  void _save() {
    final isValid = _formKey.currentState?.saveAndValidate();
    final value = _formKey.currentState?.value;

    if (isValid != null && isValid && value != null) {
      final title = value['title'] as String;
      final description = value['description'] as String;

      if (widget.editable != null) {
        context.read<UpdateFollowupCubit>().updateFollowup(
              UpdateFollowupParams(
                companionId: widget.companion.id,
                followupId: widget.editable!.id,
                title: title,
                description: description,
              ),
            );
      } else {
        context.read<CreateFollowupCubit>().createFollowup(
              CreateFollowupParams(
                companionId: widget.companion.id,
                title: title,
                description: description,
              ),
            );
      }
    }
  }
}
