import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/paginated.dart';

import 'package:s3g/core/http/response.dart';

import 'package:s3g/core/repository/repository.dart';

import 'package:s3g/src/companion/followup/domain/entity/followup.dart';

import '../../domain/repository/followup_repository.dart';
import '../remote/followup_remote_datasource.dart';

@Injectable(as: FollowupRepository)
class FollowupRepositoryImpl extends FollowupRepository {
  final FollowupRemoteDataSource _remoteDataSource;

  FollowupRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<FollowupShow> createFollowup({
    required String companionId,
    required String title,
    required String description,
  }) {
    return requestHelper(
      () => _remoteDataSource.createFollowup(
        companionId: companionId,
        title: title,
        description: description,
      ),
    );
  }

  @override
  RepositoryResponse<MessageResponse> deleteFollowup({
    required String companionId,
    required String followupId,
  }) {
    return requestHelper(
      () => _remoteDataSource.deleteFollowup(
        companionId: companionId,
        followupId: followupId,
      ),
    );
  }

  @override
  RepositoryResponse<FollowupShow> getFollowup({
    required String companionId,
    required String followupId,
  }) {
    return requestHelper(
      () => _remoteDataSource.getFollowup(
        companionId: companionId,
        followupId: followupId,
      ),
    );
  }

  @override
  RepositoryResponse<Paginated<Followup>> getFollowupList({
    required String companionId,
    int? page,
  }) {
    return requestHelper(
      () => _remoteDataSource.getFollowupList(
        companionId: companionId,
        page: page,
      ),
    );
  }

  @override
  RepositoryResponse<FollowupShow> updateFollowup({
    required String companionId,
    required String followupId,
    required String title,
    required String description,
  }) {
    return requestHelper(
      () => _remoteDataSource.updateFollowup(
        companionId: companionId,
        followupId: followupId,
        title: title,
        description: description,
      ),
    );
  }
}
