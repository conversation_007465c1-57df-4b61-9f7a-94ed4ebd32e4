// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';

part 'followup_model.g.dart';

@JsonSerializable()
class FollowupModel extends Followup {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const FollowupModel({
    required super.id,
    required super.title,
    required this.createdAt,
    required this.updatedAt,
  }) : super(createdAt: createdAt, updatedAt: updatedAt);

  factory FollowupModel.fromJson(Map<String, dynamic> json) =>
      _$FollowupModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowupModelToJson(this);
}

@JsonSerializable()
class FollowupShowModel extends FollowupShow {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const FollowupShowModel({
    required super.id,
    required super.title,
    required this.createdAt,
    required this.updatedAt,
    required super.description,
  }) : super(createdAt: createdAt, updatedAt: updatedAt);

  factory FollowupShowModel.fromJson(Map<String, dynamic> json) =>
      _$FollowupShowModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowupShowModelToJson(this);
}
