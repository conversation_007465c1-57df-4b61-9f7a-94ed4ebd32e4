import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';

import 'models/followup_model.dart';

abstract class FollowupRemoteDataSource {
  Future<FollowupShowModel> createFollowup({
    required String companionId,
    required String title,
    required String description,
  });

  Future<MessageResponse> deleteFollowup({
    required String companionId,
    required String followupId,
  });

  Future<FollowupShowModel> getFollowup({
    required String companionId,
    required String followupId,
  });

  Future<Paginated<FollowupModel>> getFollowupList({
    required String companionId,
    int? page,
  });

  Future<FollowupShowModel> updateFollowup({
    required String companionId,
    required String followupId,
    required String title,
    required String description,
  });
}

@Injectable(as: FollowupRemoteDataSource)
class FollowupRemoteDataSourceImpl implements FollowupRemoteDataSource {
  final Dio _httpClient;

  FollowupRemoteDataSourceImpl(this._httpClient);

  @override
  Future<FollowupShowModel> createFollowup({
    required String companionId,
    required String title,
    required String description,
  }) async {
    final data = {
      'title': title,
      'description': description,
    };

    final response = await _httpClient
        .post('/companions/$companionId/followups', data: data);

    return FollowupShowModel.fromJson(response.data['data']);
  }

  @override
  Future<MessageResponse> deleteFollowup({
    required String companionId,
    required String followupId,
  }) async {
    final response = await _httpClient.delete(
      '/companions/$companionId/followups/$followupId',
    );

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<FollowupShowModel> getFollowup({
    required String companionId,
    required String followupId,
  }) async {
    final response = await _httpClient.get(
      '/companions/$companionId/followups/$followupId',
    );

    return FollowupShowModel.fromJson(response.data['data']);
  }

  @override
  Future<Paginated<FollowupModel>> getFollowupList({
    required String companionId,
    int? page,
  }) async {
    final response = await _httpClient.get(
      '/companions/$companionId/followups',
      queryParameters: {'page': page},
    );

    return Paginated.fromJson(
      response.data,
      (data) => FollowupModel.fromJson(data as Map<String, dynamic>),
    );
  }

  @override
  Future<FollowupShowModel> updateFollowup({
    required String companionId,
    required String followupId,
    required String title,
    required String description,
  }) async {
    final data = {
      "title": title,
      "description": description,
    };

    final response = await _httpClient.put(
      '/companions/$companionId/followups/$followupId',
      data: data,
    );

    return FollowupShowModel.fromJson(response.data['data']);
  }
}
