import 'package:go_router/go_router.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/companion/companion/companion.dart';
import 'package:s3g/src/companion/followup/followup.dart';
import 'package:s3g/src/companion/relapse/relapse.dart';
import 'package:s3g/src/companion/widgets/companion_scaffold.dart';

final companionRoutes = GoRoute(
  path: 'companion',
  builder: (_, __) => const CompanionsListPage(),
  routes: [
    GoRoute(
      path: ':id',
      redirect: (context, state) => null,
      routes: [
        // Companion stack
        StatefulShellRoute.indexedStack(
          builder: (context, state, navigationShell) {
            return CompanionScaffoldWrapper(
              navigationShell: navigationShell,
              companionId: state.pathParameters['id']!,
            );
          },
          branches: <StatefulShellBranch>[
            StatefulShellBranch(
              routes: [
                GoRoute(
                  name: AppRoute.companionDetail,
                  path: 'detail',
                  builder: (context, state) => const CompanionDetail(),
                ),
              ],
            ),
            StatefulShellBranch(
              routes: [
                GoRoute(
                  name: AppRoute.companionFollowups,
                  path: 'followups',
                  builder: (context, state) => const FollowupListPage(),
                ),
              ],
            ),
            StatefulShellBranch(
              routes: [
                GoRoute(
                  name: AppRoute.companionRelapse,
                  path: 'relapse',
                  builder: (context, state) => const RelapsePage(),
                ),
              ],
            ),
          ],
        ),
      ],
    ),
  ],
);
