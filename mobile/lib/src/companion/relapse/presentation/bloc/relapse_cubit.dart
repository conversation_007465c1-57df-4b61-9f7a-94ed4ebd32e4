import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

import '../../domain/usecase/create_relapse.dart';
import '../../domain/usecase/delete_relapse.dart';
import '../../domain/usecase/get_relapse.dart';
import '../../domain/usecase/update_relapse.dart';

part 'relapse_state.dart';

class RelapseCubit extends Cubit<RelapseState> {
  final CreateRelapse createRelapseUseCase;
  final DeleteRelapse deleteRelapseUseCase;
  final UpdateRelapse updateRelapseUseCase;
  final GetRelapse getRelapseUseCase;
  final String companionId;

  RelapseCubit(
    this.companionId, {
    required this.createRelapseUseCase,
    required this.deleteRelapseUseCase,
    required this.updateRelapseUseCase,
    required this.getRelapseUseCase,
  }) : super(RelapseInitial());

  Future<void> getRelapse() async {
    emit(RelapseLoading());

    final result = await getRelapseUseCase(
      GetRelapseParams(companionId: companionId),
    );

    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess(relapse: success)),
    );
  }

  Future<void> createRelapse({required String description}) async {
    emit(RelapseLoading());

    final result = await createRelapseUseCase(
      CreateRelapseParams(
        companionId: companionId,
        description: description,
      ),
    );

    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess(relapse: success)),
    );
  }

  Future<void> deleteRelapse() async {
    emit(RelapseLoading());

    final result = await deleteRelapseUseCase(
      DeleteRelapseParams(companionId: companionId),
    );

    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess()),
    );
  }

  Future<void> updateRelapse({required String description}) async {
    emit(RelapseLoading());

    final result = await updateRelapseUseCase(
      UpdateRelapseParams(
        companionId: companionId,
        description: description,
      ),
    );

    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess(relapse: success)),
    );
  }
}
