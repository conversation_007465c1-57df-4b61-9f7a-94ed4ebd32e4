import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:intl/intl.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/companion/companion/companion.dart';
import 'package:s3g/src/companion/relapse/presentation/bloc/relapse_cubit.dart';

class RelapsePage extends StatelessWidget {
  const RelapsePage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: bodyPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const PageTitle(title: "Rechute"),
          BlocBuilder<CompanionShowCubit, CompanionShowState>(
            builder: (_, state) {
              switch (state) {
                case CompanionShowLoaded(companion: Companion companion):
                  return _RelapsePageContentWrapper(companion);
                default:
              }

              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }
}

class _RelapsePageContentWrapper extends StatelessWidget {
  final Companion companion;

  const _RelapsePageContentWrapper(this.companion);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      key: ValueKey(companion.id),
      create: (context) => RelapseCubit(
        companion.id,
        createRelapseUseCase: getIt(),
        updateRelapseUseCase: getIt(),
        deleteRelapseUseCase: getIt(),
        getRelapseUseCase: getIt(),
      ),
      child: _RelapsePageContent(companion),
    );
  }
}

class _RelapsePageContent extends StatefulWidget {
  final Companion companion;

  const _RelapsePageContent(this.companion);

  @override
  State<_RelapsePageContent> createState() => _RelapsePageContentState();
}

class _RelapsePageContentState extends State<_RelapsePageContent> {
  bool _editMode = false;

  @override
  Widget build(BuildContext context) {
    final companion = widget.companion;
    final relapse = widget.companion.instance.relapse;

    if (relapse != null && !_editMode) {
      return Column(
        children: [
          columnSizedBox,
          DetailTitle(
            leading: const Icon(Icons.trending_down_outlined),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "Rechute signalé le",
                  style: TextStyle(fontSize: 18.0),
                ),
                GFIconButton(
                  size: GFSize.SMALL,
                  color: Theme.of(context).colorScheme.primary,
                  type: GFButtonType.outline,
                  icon: const Icon(Icons.edit),
                  onPressed: () {
                    setState(() {
                      _editMode = true;
                    });
                  },
                )
              ],
            ),
            subtitle: Text(
              DateFormat.yMMMd('fr').format(
                companion.instance.relapse!.createdAt.toLocal(),
              ),
              style: const TextStyle(
                fontSize: 16.0,
                color: Colors.red,
              ),
            ),
          ),
          columnSizedBox,
          const GreyDivider(),

          // Description
          columnSizedBox,
          Text(
            companion.instance.relapse!.description,
            style: const TextStyle(fontSize: 18.0),
          ),
          columnSizedBox,
          columnSizedBox,

          BlocBuilder<RelapseCubit, RelapseState>(
            builder: (_, state) {
              switch (state) {
                case RelapseLoading():
                  return const GFLoader(size: GFSize.SMALL);
                default:
              }

              return GFButton(
                color: Theme.of(context).colorScheme.primary,
                onPressed: () async {
                  if (await pressConfirm(context)) {
                    return _deleteRelapse();
                  }
                },
                child: const Text("Supprimer le signalement"),
              );
            },
          ),
        ],
      );
    }

    return _RelapsePageForm(
      key: ValueKey(_editMode),
      companion: companion,
      editMode: _editMode,
      closeEditMode: () {
        setState(() {
          _editMode = false;
        });
      },
    );
  }

  void _deleteRelapse() async {
    await context.read<RelapseCubit>().deleteRelapse();

    if (mounted) {
      await context.read<CompanionShowCubit>().getFetchCompanion();
    }

    if (mounted) {
      context.read<RefreshDataBloc>().add(RefreshDataCompanionsEvent());
    }

    setState(() {
      _editMode = false;
    });
  }
}

class _RelapsePageForm extends StatefulWidget {
  final bool editMode;
  final Companion companion;
  final void Function() closeEditMode;

  const _RelapsePageForm({
    super.key,
    required this.companion,
    required this.editMode,
    required this.closeEditMode,
  });

  @override
  State<_RelapsePageForm> createState() => _RelapsePageFormState();
}

class _RelapsePageFormState extends State<_RelapsePageForm> {
  bool _formEnabled = false;
  final _textController = TextEditingController();

  @override
  void initState() {
    final relapse = widget.companion.instance.relapse;
    _textController.text = relapse?.description ?? '';

    super.initState();
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final relapseBlocState = context.watch<RelapseCubit>().state;

    if (!widget.editMode && !_formEnabled) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: GFButton(
            color: Theme.of(context).colorScheme.primary,
            onPressed: () async {
              setState(() {
                _formEnabled = true;
              });
            },
            child: const Text("Signaler une rechute"),
          ),
        ),
      );
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        const Text("Entrez votre texte"),

        const SizedBox(height: 10),

        // Text input
        TextField(
          enabled: true,
          controller: _textController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'Description',
          ),
          keyboardType: TextInputType.multiline,
          minLines: 5,
          maxLines: 20,
        ),

        columnSizedBox,

        // Listener and show toast
        BlocListener<RelapseCubit, RelapseState>(
          listener: (_, state) {
            if (state case RelapseError(message: String message)) {
              showToast(context, type: ToastType.error, message: message);
            }
          },
          child: const SizedBox.shrink(),
        ),

        // Close and save buttons
        FormButtons(
          loading: relapseBlocState is RelapseLoading,
          onSubmit: _save,
          onCancel: () {
            if (widget.editMode) {
              widget.closeEditMode();
            } else {
              setState(() {
                _formEnabled = false;
              });
            }
          },
        )
      ],
    );
  }

  void _save() async {
    // Create or update relapse status
    if (widget.editMode) {
      // Update existing relapse
      await context
          .read<RelapseCubit>()
          .updateRelapse(description: _textController.value.text);
    } else {
      // Create new relapse
      await context
          .read<RelapseCubit>()
          .createRelapse(description: _textController.value.text);
    }

    // Refresh companion
    if (mounted) {
      await context.read<CompanionShowCubit>().getFetchCompanion();
    }

    // Refresh companions list
    if (mounted) {
      context.read<RefreshDataBloc>().add(RefreshDataCompanionsEvent());
    }

    widget.closeEditMode();
  }
}
