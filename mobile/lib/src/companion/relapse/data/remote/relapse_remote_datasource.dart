import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

abstract class RelapseRemoteDataSource {
  Future<RelapseModel> createRelapse({
    required String companionId,
    required String description,
  });

  Future<MessageResponse> deleteRelapse({required String companionId});

  Future<RelapseModel> getRelapse({
    required String companionId,
  });

  Future<RelapseModel> updateRelapse({
    required String companionId,
    required String description,
  });
}

@Injectable(as: RelapseRemoteDataSource)
class RelapseRemoteDataSourceImpl implements RelapseRemoteDataSource {
  final Dio _httpClient;

  RelapseRemoteDataSourceImpl(this._httpClient);

  @override
  Future<RelapseModel> createRelapse({
    required String companionId,
    required String description,
  }) async {
    final response = await _httpClient.post(
      '/companions/$companionId/relapse',
      data: {'description': description},
    );

    return RelapseModel.fromJson(response.data['data']);
  }

  @override
  Future<MessageResponse> deleteRelapse({required String companionId}) async {
    final response =
        await _httpClient.delete('/companions/$companionId/relapse');

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<RelapseModel> getRelapse({required String companionId}) async {
    final response = await _httpClient.get('/companions/$companionId/relapse');

    return RelapseModel.fromJson(response.data['data']);
  }

  @override
  Future<RelapseModel> updateRelapse({
    required String companionId,
    required String description,
  }) async {
    final response = await _httpClient.put(
      "/companions/$companionId/relapse",
      data: {'description': description},
    );

    return RelapseModel.fromJson(response.data['data']);
  }
}
