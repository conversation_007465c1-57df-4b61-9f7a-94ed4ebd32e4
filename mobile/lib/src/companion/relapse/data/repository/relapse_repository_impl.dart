import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/instance_features/relapse/domain/entity/relapse.dart';

import '../../domain/repository/relapse_repository.dart';
import '../remote/relapse_remote_datasource.dart';

@Injectable(as: RelapseRepository)
class RelapseRepositoryImpl extends RelapseRepository {
  final RelapseRemoteDataSource _remoteDataSource;

  RelapseRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<Relapse> createRelapse({
    required String companionId,
    required String description,
  }) {
    return requestHelper(
      () => _remoteDataSource.createRelapse(
        companionId: companionId,
        description: description,
      ),
    );
  }

  @override
  RepositoryResponse<MessageResponse> deleteRelapse({
    required String companionId,
  }) {
    return requestHelper(
      () => _remoteDataSource.deleteRelapse(companionId: companionId),
    );
  }

  @override
  RepositoryResponse<Relapse> getRelapse({required String companionId}) {
    return requestHelper(
      () => _remoteDataSource.getRelapse(companionId: companionId),
    );
  }

  @override
  RepositoryResponse<Relapse> updateRelapse({
    required String companionId,
    required String description,
  }) {
    return requestHelper(() => _remoteDataSource.updateRelapse(
          companionId: companionId,
          description: description,
        ));
  }
}
