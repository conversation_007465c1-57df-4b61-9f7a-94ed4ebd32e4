import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

abstract class RelapseRepository {
  RepositoryResponse<Relapse> getRelapse({
    required String companionId,
  });

  RepositoryResponse<Relapse> updateRelapse({
    required String companionId,
    required String description,
  });

  RepositoryResponse<MessageResponse> deleteRelapse({
    required String companionId,
  });

  RepositoryResponse<Relapse> createRelapse({
    required String companionId,
    required String description,
  });
}
