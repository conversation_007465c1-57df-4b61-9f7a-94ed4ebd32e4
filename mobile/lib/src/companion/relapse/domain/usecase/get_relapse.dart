import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

import '../repository/relapse_repository.dart';

@injectable
class GetRelapse extends UseCase<Relapse, GetRelapseParams> {
  final RelapseRepository repository;

  GetRelapse({required this.repository});

  @override
  call(GetRelapseParams params) {
    return repository.getRelapse(companionId: params.companionId);
  }
}

class GetRelapseParams {
  final String companionId;

  GetRelapseParams({required this.companionId});
}
