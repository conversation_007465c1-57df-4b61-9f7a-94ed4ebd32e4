import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/relapse_repository.dart';

@injectable
class DeleteRelapse extends UseCase<MessageResponse, DeleteRelapseParams> {
  final RelapseRepository repository;

  DeleteRelapse({required this.repository});

  @override
  call(DeleteRelapseParams params) {
    return repository.deleteRelapse(companionId: params.companionId);
  }
}

class DeleteRelapseParams {
  final String companionId;

  DeleteRelapseParams({required this.companionId});
}
