import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

import '../repository/relapse_repository.dart';

@injectable
class UpdateRelapse extends UseCase<Relapse, UpdateRelapseParams> {
  final RelapseRepository repository;

  UpdateRelapse({required this.repository});

  @override
  call(UpdateRelapseParams params) async {
    return repository.updateRelapse(
      companionId: params.companionId,
      description: params.description,
    );
  }
}

class UpdateRelapseParams {
  final String companionId;
  final String description;

  UpdateRelapseParams({required this.companionId, required this.description});
}
