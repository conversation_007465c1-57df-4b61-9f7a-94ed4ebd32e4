import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/companion/companion/companion.dart';
import 'package:s3g/src/manager/instance/instance.dart';

class CompanionDetail extends StatelessWidget {
  const CompanionDetail({super.key});

  @override
  Widget build(BuildContext context) {
    final companion =
        (context.watch<CompanionShowCubit>().state as CompanionShowLoaded)
            .companion;

    return RefreshIndicator(
      onRefresh: () async {
        return context.read<CompanionShowCubit>().getFetchCompanion();
      },
      child: SingleChildScrollView(
        padding: bodyPadding,
        child: _CompanionDetailContent(companion),
      ),
    );
  }
}

class _CompanionDetailContent extends StatelessWidget {
  final Companion companion;
  const _CompanionDetailContent(this.companion);

  @override
  Widget build(BuildContext context) {
    final date = DateFormat.yMMMd('fr').format(companion.createdAt.toLocal());

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Survivor code
        columnSizedBox,
        DetailTitle(
          leading: const Icon(Icons.info),
          titleText: "Code Survivant",
          subtitleText: companion.instance.survivor.code,
        ),
        columnSizedBox,
        const GreyDivider(),

        // Companion role
        columnSizedBox,
        DetailTitle(
          leading: const Icon(Icons.person_3_outlined),
          titleText: "Role",
          subtitleText: companion.type.getLabel(),
        ),
        columnSizedBox,
        const GreyDivider(),

        // Case code
        columnSizedBox,
        DetailTitle(
          leading: const Icon(Icons.cases_outlined),
          titleText: "Code Cas",
          subtitleText: companion.instance.code,
        ),
        columnSizedBox,
        const GreyDivider(),

        // Case type
        columnSizedBox,
        DetailTitle(
          leading: const Icon(Icons.difference_outlined),
          titleText: "Type Cas",
          subtitleText: companion.instance.type.getLabel(),
        ),
        columnSizedBox,
        const GreyDivider(),

        // Case type
        columnSizedBox,
        DetailTitle(
          leading: switch (companion.instance.status) {
            InstanceStatus.OPEN => const Icon(Icons.folder),
            InstanceStatus.CLOSED => const Icon(Icons.folder_off),
          },
          titleText: "Statut Cas",
          subtitleText: companion.instance.status.getLabel(),
        ),
        columnSizedBox,
        const GreyDivider(),

        // Health center
        columnSizedBox,
        DetailTitle(
          leading: const Icon(Icons.local_hospital_outlined),
          titleText: "Cante de santé",
          subtitleText: companion.instance.healthCenter.name,
        ),
        columnSizedBox,
        const GreyDivider(),

        // Creator
        if (companion.instance.creator != null) ...[
          columnSizedBox,
          DetailTitle(
            leading: const Icon(Icons.person_outline),
            titleText: "APS",
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 3),

                // Creator name
                Text(
                  companion.instance.creator!.name,
                  style: TextStyle(
                    fontSize: 16.0,
                    color: Colors.grey[600],
                  ),
                ),

                const SizedBox(height: 5),

                // Creator phone number
                Text(
                  companion.instance.creator!.phone,
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          columnSizedBox,
          const GreyDivider(),
        ],

        // Description
        if (companion.instance.description != null) ...[
          columnSizedBox,
          DetailTitle(
            leading: const Icon(Icons.description),
            titleText: "Description du cas",
            subtitleText: companion.instance.description,
          ),
          columnSizedBox,
          const GreyDivider(),
        ],

        // Relapse at
        if (companion.instance.relapse != null) ...[
          columnSizedBox,
          DetailTitle(
            leading: const Icon(Icons.trending_down_outlined),
            titleText: "Rechute signalé le",
            subtitle: Text(
              DateFormat.yMMMd('fr').format(
                companion.instance.relapse!.createdAt.toLocal(),
              ),
              style: const TextStyle(
                fontSize: 16.0,
                color: Colors.red,
              ),
            ),
          ),
          columnSizedBox,
          const GreyDivider(),
        ],

        // Assigned at
        columnSizedBox,
        DetailTitle(
          leading: const Icon(Icons.local_hospital_outlined),
          titleText: "Assigné le",
          subtitleText: date,
        ),
        columnSizedBox,
        const GreyDivider(),
      ],
    );
  }
}
