import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';
import 'package:s3g/src/manager/instance/instance.dart';

class ListCompanionItem extends StatelessWidget {
  const ListCompanionItem({super.key, required this.state});

  final PaginatedItemState<Companion> state;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: state.itemCountWithLoader,
      itemBuilder: (context, index) {
        if (index >= state.items.length) {
          return const Center(child: GFLoader());
        }

        final companion = state.items[index];

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: CompanionItem(
            companion: companion,
          ),
        );
      },
    );
  }
}

class CompanionItem extends StatelessWidget {
  final Companion companion;
  const CompanionItem({super.key, required this.companion});

  @override
  Widget build(BuildContext context) {
    final instance = companion.instance;
    final bool isOpen = instance.status == InstanceStatus.OPEN;
    final bool hasRelapse = instance.relapse?.createdAt != null;

    return InkWell(
      onTap: isOpen
          ? () {
              context.pushNamed(
                AppRoute.companionDetail,
                pathParameters: {'id': companion.id},
              );
            }
          : null,
      borderRadius: BorderRadius.circular(cardRadius),
      child: Container(
        padding: cardSpacing,
        decoration: BoxDecoration(
          color: isOpen ? Colors.white : Colors.grey.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(cardRadius),
          border: Border.all(
            color: hasRelapse
                ? AppTheme.primary.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.08),
            width: 1,
          ),
          boxShadow: isOpen
              ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.04),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            // Status Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isOpen
                    ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                isOpen ? Icons.people : Icons.people_outline,
                color: isOpen ? const Color(0xFF3B82F6) : Colors.grey[600],
                size: 24,
              ),
            ),
            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Row
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "Survivant: ${instance.survivor.code}",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isOpen
                                ? const Color(0xFF1F2937)
                                : Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (hasRelapse) ...{
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppTheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Text(
                            "Rechute",
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.primary,
                            ),
                          ),
                        ),
                      },
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Instance Code
                  Text(
                    instance.code,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  const SizedBox(height: 4),

                  // Companion Role
                  Text(
                    "Rôle: ${companion.type.getLabel()}",
                    style: TextStyle(
                      fontSize: 13,
                      color: const Color(0xFF059669),
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  const SizedBox(height: 4),

                  // Health Center
                  Text(
                    "Centre: ${instance.healthCenter.name}",
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Bottom Info Row
                  Row(
                    children: [
                      // Date
                      Expanded(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 14,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(width: 4),
                            Flexible(
                              child: LocalTimeago(
                                date: companion.createdAt,
                                builder: (_, value) => Text(
                                  value,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[500],
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(width: 8),

                      // Status Badge
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isOpen
                              ? const Color(0xFF059669).withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          instance.status.getLabel(),
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                            color: isOpen
                                ? const Color(0xFF059669)
                                : Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Relapse Date if exists
                  if (hasRelapse) ...{
                    const SizedBox(height: 8),
                    RelapseDate(
                      createdAt: instance.relapse!.createdAt,
                    ),
                  },
                ],
              ),
            ),

            // Arrow (only if open)
            if (isOpen) ...{
              const SizedBox(width: 8),
              Icon(
                Icons.chevron_right,
                color: Colors.grey[400],
                size: 20,
              ),
            },
          ],
        ),
      ),
    );
  }
}
