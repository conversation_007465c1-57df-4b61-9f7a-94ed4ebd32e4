import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';
import 'package:s3g/src/manager/instance/instance.dart';

class ListCompanionItem extends StatelessWidget {
  const ListCompanionItem({super.key, required this.state});

  final PaginatedItemState<Companion> state;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: state.itemCountWithLoader,
      itemBuilder: (context, index) {
        if (index >= state.items.length) {
          const Center(child: GFLoader());
        }

        final companion = state.items[index];
        final instance = companion.instance;

        return IgnorePointer(
          ignoring: instance.status == InstanceStatus.CLOSED,
          child: Column(
            children: [
              // Some space
              const SizedBox(height: 10),

              // List
              ListTile(
                // Remove vertical space
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
                onTap: () {
                  context.pushNamed(
                    AppRoute.companionDetail,
                    pathParameters: {'id': companion.id},
                  );
                },

                // Icon
                leading: switch (instance.status) {
                  InstanceStatus.OPEN => Tooltip(
                      message: instance.status.getLabel(),
                      child: const GFAvatar(
                        size: 27.0,
                        child: Icon(Icons.folder_open),
                      ),
                    ),
                  InstanceStatus.CLOSED => Tooltip(
                      message: instance.status.getLabel(),
                      child: GFAvatar(
                        backgroundColor: Colors.grey[400],
                        size: 27.0,
                        child: Icon(Icons.folder_off, color: Colors.grey[700]),
                      ),
                    ),
                },

                // Title
                title: Text("Survivant: ${instance.survivor.code}"),

                // Subtitle
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Instance code
                    Text(
                      instance.code,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),

                    // Companion role
                    Text(
                      "Role: ${companion.type.getLabel()}",
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),

                    // Health center
                    Text(
                      "Centre de santé: ${instance.healthCenter.name}",
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),

                    // Relapse date
                    if (instance.relapse?.createdAt != null)
                      RelapseDate(
                        createdAt: instance.relapse!.createdAt,
                      )
                  ],
                ),
                trailing: LocalTimeago(
                  date: companion.createdAt,
                  builder: (_, value) => Text(value),
                ),
              ),
              const GreyDivider(),
            ],
          ),
        );
      },
    );
  }
}
