import 'package:injectable/injectable.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';
import 'package:s3g/src/companion/companion/domain/usecase/get_companion_list.dart';

@injectable
class CompanionsListBloc extends PaginatedItemBloc<Companion> {
  final GetCompanionList _getCompanionList;

  CompanionsListBloc(this._getCompanionList) : super();

  @override
  callUseCase(PaginationParams params) {
    return _getCompanionList(params);
  }
}
