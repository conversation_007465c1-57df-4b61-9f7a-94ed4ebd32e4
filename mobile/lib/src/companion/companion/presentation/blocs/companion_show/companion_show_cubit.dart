import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';
import 'package:s3g/src/companion/companion/domain/usecase/get_companion.dart';

part 'companion_show_state.dart';

@injectable
class CompanionShowCubit extends Cubit<CompanionShowState> {
  final GetCompanion getCompanionUseCase;
  final String companionId;

  CompanionShowCubit(
    this.companionId, {
    required this.getCompanionUseCase,
  }) : super(CompanionShowInitial());

  Future<void> getCompanion() async {
    emit(CompanionShowLoading());

    final result = await getCompanionUseCase(
      GetCompanionParams(companionId: companionId),
    );

    result.fold(
      (failure) => emit(CompanionShowError(message: failure.message)),
      (companion) => emit(CompanionShowLoaded(companion: companion)),
    );
  }

  Future<void> getFetchCompanion() async {
    final result = await getCompanionUseCase(
      GetCompanionParams(companionId: companionId),
    );

    result.fold(
      (_) => null,
      (companion) => emit(CompanionShowLoaded(companion: companion)),
    );
  }
}
