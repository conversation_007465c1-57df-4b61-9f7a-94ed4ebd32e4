part of 'companion_show_cubit.dart';

@immutable
sealed class CompanionShowState {}

final class CompanionShowInitial extends CompanionShowState {}

final class CompanionShowLoading extends CompanionShowState {}

final class CompanionShowLoaded extends CompanionShowState {
  final Companion companion;

  CompanionShowLoaded({required this.companion});
}

final class CompanionShowError extends CompanionShowState {
  final String message;

  CompanionShowError({required this.message});
}
