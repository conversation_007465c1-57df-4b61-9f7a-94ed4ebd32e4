import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';
import 'package:s3g/src/companion/companion/domain/repository/companion_repository.dart';

@injectable
class GetCompanion extends UseCase<Companion, GetCompanionParams> {
  final CompanionRepository _repository;

  GetCompanion(this._repository);

  @override
  call(GetCompanionParams params) {
    return _repository.getCompanion(params.companionId);
  }
}

class GetCompanionParams {
  final String companionId;

  GetCompanionParams({required this.companionId});
}
