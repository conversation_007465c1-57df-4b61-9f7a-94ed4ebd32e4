import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';
import 'package:s3g/src/companion/companion/domain/repository/companion_repository.dart';

@injectable
class GetCompanionList extends UseCase<Paginated<Companion>, PaginationParams> {
  final CompanionRepository _repository;

  GetCompanionList(this._repository);

  @override
  call(PaginationParams params) {
    return _repository.getCompanions(params.page);
  }
}
