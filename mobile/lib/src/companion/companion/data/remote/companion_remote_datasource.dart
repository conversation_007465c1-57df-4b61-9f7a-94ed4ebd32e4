import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';

import 'models/companion_model.dart';

abstract class CompanionRemoteDataSource {
  Future<CompanionModel> getCompanion(String companionId);

  Future<Paginated<CompanionModel>> getCompanions(int? page);
}

@Injectable(as: CompanionRemoteDataSource)
class CompanionRemoteDataSourceImpl extends CompanionRemoteDataSource {
  final Dio _httpClient;

  CompanionRemoteDataSourceImpl(this._httpClient);

  @override
  Future<CompanionModel> getCompanion(String companionId) async {
    final response = await _httpClient.get("/companions/$companionId");

    return CompanionModel.fromJson(response.data['data']);
  }

  @override
  Future<Paginated<CompanionModel>> getCompanions(int? page) async {
    final response =
        await _httpClient.get('/companions', queryParameters: {'page': page});

    return Paginated.fromJson(
      response.data,
      (data) => CompanionModel.fromJson(data as Map<String, dynamic>),
    );
  }
}
