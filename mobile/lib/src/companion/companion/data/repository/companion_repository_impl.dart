import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';

import '../../domain/repository/companion_repository.dart';
import '../remote/companion_remote_datasource.dart';

@Injectable(as: CompanionRepository)
class CompanionRepositoryImpl extends CompanionRepository {
  final CompanionRemoteDataSource _remoteDataSource;

  CompanionRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<Companion> getCompanion(String companionId) {
    return requestHelper(() => _remoteDataSource.getCompanion(companionId));
  }

  @override
  RepositoryResponse<Paginated<Companion>> getCompanions(int? page) {
    return requestHelper(() => _remoteDataSource.getCompanions(page));
  }
}
