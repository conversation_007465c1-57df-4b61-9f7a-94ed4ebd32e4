import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/advice/data/remote/advice_remote_datasource.dart';
import 'package:s3g/src/advice/domain/entity/advice.dart';
import 'package:s3g/src/advice/domain/repository/advice_repository.dart';

@Injectable(as: AdviceRepository)
class AdviceRepositoryImpl implements AdviceRepository {
  final AdviceRemoteDataSource remoteDataSource;

  AdviceRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, Paginated<Advice>>> getAdvices(int? page) {
    return requestHelper(() => remoteDataSource.getAdvices(page));
  }

  @override
  RepositoryResponse<Advice> getAdvice(String id) {
    return requestHelper(() => remoteDataSource.getAdvice(id));
  }
}
