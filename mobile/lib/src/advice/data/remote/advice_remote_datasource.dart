import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import './models/advice_model.dart';

abstract class AdviceRemoteDataSource {
  Future<Paginated<AdviceModel>> getAdvices(int? page);

  Future<AdviceModel> getAdvice(String id);
}

@Injectable(as: AdviceRemoteDataSource)
class AdviceRemoteDataSourceImpl implements AdviceRemoteDataSource {
  final Dio httpClient;

  AdviceRemoteDataSourceImpl({@Named('Unauthorized') required this.httpClient});

  @override
  Future<Paginated<AdviceModel>> getAdvices(int? page) async {
    final response = await httpClient.get(
      '/advices',
      queryParameters: {"page": page},
    );

    return Paginated.fromJson(
      response.data,
      (data) => AdviceModel.fromJson(data as Map<String, dynamic>),
    );
  }

  @override
  Future<AdviceModel> getAdvice(String id) async {
    final response = await httpClient.get('/advices/$id');

    return AdviceModel.fromJson(response.data['data']);
  }
}
