// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/advice/domain/entity/advice.dart';

part 'advice_model.g.dart';

@JsonSerializable()
class AdviceModel extends Advice {
  @override
  @J<PERSON><PERSON>ey(name: "created_at")
  final DateTime createdAt;

  @override
  @Json<PERSON>ey(name: "updated_at")
  final DateTime updatedAt;

  const AdviceModel({
    required super.id,
    required super.title,
    required super.description,
    required super.language,
    required this.createdAt,
    required this.updatedAt,
  }) : super(createdAt: createdAt, updatedAt: updatedAt);

  AdviceModel copyWith({
    String? id,
    String? title,
    String? description,
    String? language,
  }) {
    return AdviceModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      language: language ?? this.language,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  factory AdviceModel.fromJson(Map<String, dynamic> json) =>
      _$AdviceModelFromJson(json);

  Map<String, dynamic> toJson() => _$AdviceModelToJson(this);
}
