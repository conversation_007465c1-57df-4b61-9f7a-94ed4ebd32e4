import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

@immutable
class Advice extends Equatable {
  final String id;
  final String title;
  final String? description;
  final String language;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Advice({
    required this.id,
    required this.title,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    required this.language,
  });

  @override
  List<Object?> get props => [id, title];
}
