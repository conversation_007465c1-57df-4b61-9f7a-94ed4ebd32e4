import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/advice/domain/entity/advice.dart';
import 'package:s3g/src/advice/domain/repository/advice_repository.dart';

@injectable
class GetAdvices extends UseCase<Paginated<Advice>, PaginationParams> {
  final AdviceRepository adviceRepository;

  GetAdvices({required this.adviceRepository});

  @override
  Future<Either<Failure, Paginated<Advice>>> call(PaginationParams params) {
    return adviceRepository.getAdvices(params.page);
  }
}
