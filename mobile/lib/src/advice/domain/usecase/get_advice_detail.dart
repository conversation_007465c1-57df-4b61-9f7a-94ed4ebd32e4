import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/advice.dart';
import '../repository/advice_repository.dart';

@injectable
class GetAdviceDetail extends UseCase<Advice, String> {
  final AdviceRepository adviceRepository;

  GetAdviceDetail({required this.adviceRepository});

  @override
  call(String params) {
    return adviceRepository.getAdvice(params);
  }
}
