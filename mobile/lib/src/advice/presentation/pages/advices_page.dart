import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/advice/advice.dart';

import '../widgets/advices_list.dart';

@immutable
class AdvicePage extends StatelessWidget {
  const AdvicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AdvicesBloc>()..add(PaginatedItemFetched()),
      child: const AdvicesList(),
    );
  }
}
