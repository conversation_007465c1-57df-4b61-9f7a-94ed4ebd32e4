import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/widgets/retry_widget.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';

import '../../domain/entity/advice.dart';
import '../bloc/get_advice_detail/get_advice_detail_cubit.dart';

class AdviceShowPage extends StatelessWidget {
  final String adviceId;

  const AdviceShowPage({
    super.key,
    required this.adviceId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          getIt<GetAdviceDetailCubit>()..fetchAdviceDetail(adviceId),
      child: _AdviceShowPageContent(adviceId: adviceId),
    );
  }
}

class _AdviceShowPageContent extends StatelessWidget {
  final String adviceId;
  const _AdviceShowPageContent({required this.adviceId});
  @override
  Widget build(BuildContext context) {
    final state = context.watch<GetAdviceDetailCubit>().state;

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: state is GetAdviceDetailLoaded
            ? Text(state.advice.title)
            : const Text("..."),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: bodyPadding,
          physics: const ScrollPhysics(),
          child: BlocBuilder<GetAdviceDetailCubit, GetAdviceDetailState>(
            builder: (context, state) {
              switch (state) {
                case GetAdviceDetailInitial() || GetAdviceDetailLoading():
                  return _AdviceShowPageShimmer();

                case GetAdviceDetailLoaded(advice: Advice advice):
                  return HtmlWidget(
                    advice.description ?? "",
                    renderMode: RenderMode.column,
                    textStyle: const TextStyle(fontSize: 16),
                  );

                case GetAdviceDetailError(message: String message):
                  return RetryWidget(
                    message: message,
                    onPressed: () {
                      context
                          .read<GetAdviceDetailCubit>()
                          .fetchAdviceDetail(adviceId);
                    },
                  );
                // ignore: unreachable_switch_default
                default:
              }

              return const SizedBox.shrink();
            },
          ),
        ),
      ),
    );
  }
}

class _AdviceShowPageShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GFShimmer(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 20,
            width: MediaQuery.of(context).size.width,
            color: Colors.white,
          ),
          const SizedBox(height: 20),
          Container(
            height: 15,
            width: MediaQuery.of(context).size.width / 2,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          Container(
            height: 15,
            width: MediaQuery.of(context).size.width,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          Container(
            height: 15,
            width: MediaQuery.of(context).size.width,
            color: Colors.white,
          ),
          const SizedBox(height: 20),
          Container(
            height: 17,
            width: MediaQuery.of(context).size.width / 2,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          Container(
            height: 15,
            width: MediaQuery.of(context).size.width,
            color: Colors.white,
          ),
        ],
      ),
    );
  }
}
