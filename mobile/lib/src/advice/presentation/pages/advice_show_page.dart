import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/widgets/retry_widget.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';

import '../../domain/entity/advice.dart';
import '../bloc/get_advice_detail/get_advice_detail_cubit.dart';

class AdviceShowPage extends StatelessWidget {
  final String adviceId;

  const AdviceShowPage({
    super.key,
    required this.adviceId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          getIt<GetAdviceDetailCubit>()..fetchAdviceDetail(adviceId),
      child: _AdviceShowPageContent(adviceId: adviceId),
    );
  }
}

class _AdviceShowPageContent extends StatelessWidget {
  final String adviceId;
  const _AdviceShowPageContent({required this.adviceId});
  @override
  Widget build(BuildContext context) {
    final state = context.watch<GetAdviceDetailCubit>().state;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        centerTitle: true,
        title: state is GetAdviceDetailLoaded
            ? Text(
                state.advice.title,
                overflow: TextOverflow.ellipsis,
              )
            : const Text("Conseil"),
      ),
      body: SafeArea(
        child: BlocBuilder<GetAdviceDetailCubit, GetAdviceDetailState>(
          builder: (context, state) {
            switch (state) {
              case GetAdviceDetailInitial() || GetAdviceDetailLoading():
                return _AdviceShowPageShimmer();

              case GetAdviceDetailLoaded(advice: Advice advice):
                return SingleChildScrollView(
                  padding: bodyPadding,
                  physics: const ScrollPhysics(),
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(cardRadius),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.04),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title Section
                        Container(
                          padding: cardSpacing,
                          decoration: BoxDecoration(
                            color:
                                const Color(0xFF3B82F6).withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: const Color(0xFF3B82F6)
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.lightbulb_outline,
                                  color: Color(0xFF3B82F6),
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      advice.title,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xFF1F2937),
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: const Color(0xFF059669)
                                            .withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Text(
                                        advice.language.toUpperCase(),
                                        style: const TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFF059669),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Content Section
                        HtmlWidget(
                          advice.description ?? "Aucun contenu disponible",
                          renderMode: RenderMode.column,
                          textStyle: const TextStyle(
                            fontSize: 16,
                            height: 1.6,
                            color: Color(0xFF374151),
                          ),
                        ),
                      ],
                    ),
                  ),
                );

              case GetAdviceDetailError(message: String message):
                return Center(
                  child: Container(
                    margin: cardSpacing,
                    child: RetryWidget(
                      message: message,
                      onPressed: () {
                        context
                            .read<GetAdviceDetailCubit>()
                            .fetchAdviceDetail(adviceId);
                      },
                    ),
                  ),
                );
              // ignore: unreachable_switch_default
              default:
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}

class _AdviceShowPageShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: cardSpacing,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(cardRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: GFShimmer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header shimmer
            Container(
              padding: cardSpacing,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 18,
                          width: MediaQuery.of(context).size.width * 0.6,
                          color: Colors.grey[300],
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 14,
                          width: 80,
                          color: Colors.grey[300],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Content shimmer
            Container(
              height: 16,
              width: MediaQuery.of(context).size.width,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 12),
            Container(
              height: 16,
              width: MediaQuery.of(context).size.width * 0.8,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 12),
            Container(
              height: 16,
              width: MediaQuery.of(context).size.width * 0.9,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 20),
            Container(
              height: 16,
              width: MediaQuery.of(context).size.width * 0.7,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 12),
            Container(
              height: 16,
              width: MediaQuery.of(context).size.width,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 12),
            Container(
              height: 16,
              width: MediaQuery.of(context).size.width * 0.6,
              color: Colors.grey[300],
            ),
          ],
        ),
      ),
    );
  }
}
