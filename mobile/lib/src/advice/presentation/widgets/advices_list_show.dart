import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/advice/domain/entity/advice.dart';

class AdvicesListShow extends StatefulWidget {
  final PaginatedItemState<Advice> state;

  const AdvicesListShow({
    super.key,
    required this.state,
  });

  @override
  State<AdvicesListShow> createState() => _AdvicesListShowState();
}

class _AdvicesListShowState extends State<AdvicesListShow> {
  String? _selectedLanguage;
  List<String> _languages = [];
  List<Advice> _items = [];

  @override
  void initState() {
    super.initState();

    _updateLanguages();
  }

  @override
  void didUpdateWidget(covariant AdvicesListShow oldWidget) {
    super.didUpdateWidget(oldWidget);

    _updateLanguages();
  }

  void _updateLanguages() {
    _languages = widget.state.items.map((e) => e.language).toSet().toList();
    if (_languages.isNotEmpty && _selectedLanguage == null) {
      _selectedLanguage = _languages.first;
    }

    _updateAdvices();
  }

  void _updateAdvices() {
    _items = widget.state.items
        .where((element) => element.language == _selectedLanguage)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        const SizedBox(height: 10),

        LanguageDropdown(
          selectedLanguage: _selectedLanguage,
          languages: _languages,
          onChanged: (value) {
            setState(() {
              _selectedLanguage = value;
              _updateAdvices();
            });
          },
        ),

        const SizedBox(height: 15),

        // Display Paginated advices
        ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: widget.state.loading ? _items.length + 1 : _items.length,
          itemBuilder: (context, index) {
            if (index >= _items.length) {
              const Center(
                child: GFLoader(),
              );
            }

            final advice = _items[index];

            return Padding(
              padding: const EdgeInsets.only(left: 10),
              child: Column(
                children: [
                  // List
                  ListTile(
                    title: Text(advice.title),
                    contentPadding: const EdgeInsets.symmetric(vertical: 0),
                    trailing: const Icon(
                      Icons.chevron_right,
                      color: Colors.grey,
                    ),
                    onTap: () {
                      context.pushNamed(
                        AppRoute.adviceDetail,
                        pathParameters: {'id': advice.id},
                      );
                    },
                  ),
                  const GreyDivider(),
                ],
              ),
            );
          },
        )
      ],
    );
  }
}

@immutable
class LanguageDropdown extends StatelessWidget {
  final String? selectedLanguage;
  final List<String> languages;
  final void Function(String?)? onChanged;

  const LanguageDropdown({
    super.key,
    this.selectedLanguage,
    this.languages = const [],
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: DropdownButtonHideUnderline(
        child: GFDropdown(
          isExpanded: true,
          itemHeight: 50,
          hint: const Text('La langue'),
          padding: const EdgeInsets.all(15),
          borderRadius: BorderRadius.circular(10),
          border: const BorderSide(color: Colors.black12, width: 1),
          dropdownButtonColor: Colors.grey[300],
          value: selectedLanguage,
          onChanged: onChanged,
          items: languages
              .map((value) => DropdownMenuItem(
                    value: value,
                    child: Text(value),
                  ))
              .toList(),
        ),
      ),
    );
  }
}
