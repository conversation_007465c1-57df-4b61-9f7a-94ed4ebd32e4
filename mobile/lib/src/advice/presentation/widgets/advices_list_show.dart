import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/advice/domain/entity/advice.dart';

class AdvicesListShow extends StatefulWidget {
  final PaginatedItemState<Advice> state;

  const AdvicesListShow({
    super.key,
    required this.state,
  });

  @override
  State<AdvicesListShow> createState() => _AdvicesListShowState();
}

class _AdvicesListShowState extends State<AdvicesListShow> {
  String? _selectedLanguage;
  List<String> _languages = [];
  List<Advice> _items = [];

  @override
  void initState() {
    super.initState();

    _updateLanguages();
  }

  @override
  void didUpdateWidget(covariant AdvicesListShow oldWidget) {
    super.didUpdateWidget(oldWidget);

    _updateLanguages();
  }

  void _updateLanguages() {
    _languages = widget.state.items.map((e) => e.language).toSet().toList();
    if (_languages.isNotEmpty && _selectedLanguage == null) {
      _selectedLanguage = _languages.first;
    }

    _updateAdvices();
  }

  void _updateAdvices() {
    _items = widget.state.items
        .where((element) => element.language == _selectedLanguage)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        // Language Filter
        if (_languages.length > 1)
          Container(
            margin: const EdgeInsets.only(bottom: 20),
            child: LanguageDropdown(
              selectedLanguage: _selectedLanguage,
              languages: _languages,
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value;
                  _updateAdvices();
                });
              },
            ),
          ),

        // Advice Cards
        ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: widget.state.loading ? _items.length + 1 : _items.length,
          itemBuilder: (context, index) {
            if (index >= _items.length) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: GFLoader(),
                ),
              );
            }

            final advice = _items[index];

            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: AdviceCard(advice: advice),
            );
          },
        )
      ],
    );
  }
}

class AdviceCard extends StatelessWidget {
  final Advice advice;

  const AdviceCard({super.key, required this.advice});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.pushNamed(
          AppRoute.adviceDetail,
          pathParameters: {'id': advice.id},
        );
      },
      borderRadius: BorderRadius.circular(cardRadius),
      child: Container(
        padding: cardSpacing,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(cardRadius),
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.08),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.lightbulb_outline,
                color: Color(0xFF3B82F6),
                size: 24,
              ),
            ),
            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    advice.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1F2937),
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                  const SizedBox(height: 6),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF059669).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      advice.language.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF059669),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Arrow
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}

@immutable
class LanguageDropdown extends StatelessWidget {
  final String? selectedLanguage;
  final List<String> languages;
  final void Function(String?)? onChanged;

  const LanguageDropdown({
    super.key,
    this.selectedLanguage,
    this.languages = const [],
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: selectedLanguage,
          hint: const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Sélectionner une langue',
              style: TextStyle(
                color: Color(0xFF6B7280),
                fontSize: 14,
              ),
            ),
          ),
          icon: const Padding(
            padding: EdgeInsets.only(right: 16),
            child: Icon(
              Icons.keyboard_arrow_down,
              color: Color(0xFF6B7280),
            ),
          ),
          onChanged: onChanged,
          items: languages.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: AppTheme.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      value,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
