import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';

class AdviceLoadingShimmer extends StatelessWidget {
  const AdviceLoadingShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return GFShimmer(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            height: 50,
            width: MediaQuery.of(context).size.width,
            margin: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            color: Colors.white,
          ),
          const SizedBox(height: 10),
          for (var i = 0; i < 5; i++) ...[
            Container(
              width: MediaQuery.of(context).size.width,
              margin: const EdgeInsets.symmetric(
                horizontal: 5,
              ),
              height: 40,
              color: Colors.white,
            ),
            const SizedBox(height: 8),
          ],
        ],
      ),
    );
  }
}
