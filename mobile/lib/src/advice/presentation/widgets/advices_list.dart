import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/authentication/authentication.dart';

import '../../domain/entity/advice.dart';
import '../bloc/advices/advices_bloc.dart';

import 'advices_list_show.dart';

import 'shimmer.dart';

class AdvicesList extends StatefulWidget {
  const AdvicesList({super.key});

  @override
  State<AdvicesList> createState() => _AdvicesListState();
}

class _AdvicesListState extends State<AdvicesList> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const AppBarLeadingTitle(),
        centerTitle: true,
        title: const Text(
          "Conseils",
          style: TextStyle(color: Colors.white),
        ),
        actions: <Widget>[
          BlocBuilder<AuthenticatedCubit, AuthenticatedState>(
            builder: (context, state) {
              return GFIconButton(
                icon: Icon(
                  state.user == null
                      ? Icons.login_outlined
                      : Icons.account_circle_outlined,
                  size: 22,
                  color: Colors.white,
                ),
                onPressed: () => state.user == null
                    ? context.push(AppRoute.login)
                    : context.go(AppRoute.manager),
                type: GFButtonType.transparent,
              );
            },
          ),
        ],
      ),

      // body
      body: SafeArea(
        child: SingleChildScrollView(
          padding: bodyPadding,
          physics: const ScrollPhysics(),
          controller: _scrollController,
          child: Column(
            children: [
              // S3G logo
              Padding(
                padding: const EdgeInsets.only(top: 30.0, bottom: 20),
                child: Center(
                  child: SizedBox(
                    width: 200,
                    height: 100,
                    child: Image.asset('assets/s3g.png'),
                  ),
                ),
              ),

              PaginatedWidget<AdvicesBloc, Advice>(
                scrollController: _scrollController,
                loadingWidget: const AdviceLoadingShimmer(),
                widgetEmpty: const Center(
                  child: Text(
                    "Bienvenu.",
                    style: TextStyle(fontSize: 30),
                  ),
                ),
                render: (_, __, state) {
                  return AdvicesListShow(state: state);
                } as WidgetRender<Advice>,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
