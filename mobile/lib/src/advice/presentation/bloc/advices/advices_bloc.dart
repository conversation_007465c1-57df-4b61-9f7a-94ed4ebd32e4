import 'package:injectable/injectable.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/advice/advice.dart';

import '../../../domain/entity/advice.dart';

@injectable
class AdvicesBloc extends PaginatedItemBloc<Advice> {
  final GetAdvices _getAdvices;

  AdvicesBloc(this._getAdvices) : super();

  @override
  callUseCase(PaginationParams params) => _getAdvices(params);
}
