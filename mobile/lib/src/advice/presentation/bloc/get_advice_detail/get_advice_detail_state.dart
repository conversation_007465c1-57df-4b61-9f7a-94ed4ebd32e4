part of 'get_advice_detail_cubit.dart';

@immutable
sealed class GetAdviceDetailState {}

final class GetAdviceDetailInitial extends GetAdviceDetailState {}

final class GetAdviceDetailLoading extends GetAdviceDetailState {}

final class GetAdviceDetailLoaded extends GetAdviceDetailState {
  final Advice advice;

  GetAdviceDetailLoaded({required this.advice});
}

final class GetAdviceDetailError extends GetAdviceDetailState {
  final String message;

  GetAdviceDetailError({required this.message});
}
