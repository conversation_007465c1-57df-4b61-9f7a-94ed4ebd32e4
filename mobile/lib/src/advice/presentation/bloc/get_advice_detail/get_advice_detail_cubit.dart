import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/advice/domain/entity/advice.dart';
import 'package:s3g/src/advice/domain/usecase/get_advice_detail.dart';

part 'get_advice_detail_state.dart';

@injectable
class GetAdviceDetailCubit extends Cubit<GetAdviceDetailState> {
  final GetAdviceDetail _getAdviceDetail;

  GetAdviceDetailCubit(this._getAdviceDetail) : super(GetAdviceDetailInitial());

  Future<void> fetchAdviceDetail(String id) async {
    emit(GetAdviceDetailLoading());

    final result = await _getAdviceDetail(id);

    result.fold(
      (failure) => emit(GetAdviceDetailError(message: failure.message)),
      (advice) => emit(GetAdviceDetailLoaded(advice: advice)),
    );
  }
}
