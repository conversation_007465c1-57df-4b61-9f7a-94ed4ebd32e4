import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/routes/config.dart';

import 'instance/instance.dart';
import 'package:s3g/src/manager/member/member.dart';

import 'widgets/health_center_scaffold.dart';
import 'widgets/instance_show_scaffold.dart';

import 'health_center/health_center.dart';

final _healthCenterDetail = GlobalKey<NavigatorState>();
final _healthCenterInstances = GlobalKey<NavigatorState>();
final _healthCenterCompanions = GlobalKey<NavigatorState>();
final _healthCenterAPS = GlobalKey<NavigatorState>();

final apsRoutes = GoRoute(
  path: 'manager',
  parentNavigatorKey: rootNavigatorKey,
  redirect: (context, state) {
    return state.fullPath == AppRoute.manager
        ? AppRoute.managerHealthCenterDetail
        : null;
  },
  routes: [
    StatefulShellRoute.indexedStack(
      builder: (context, state, navigationShell) {
        return HealthCenterScaffoldWrapper(
          navigationShell: navigationShell,
        );
      },
      branches: <StatefulShellBranch>[
        // Health Center details
        StatefulShellBranch(
          navigatorKey: _healthCenterDetail,
          routes: [
            GoRoute(
              name: AppRoute.managerHealthCenterDetail,
              path: 'health-center/detail',
              builder: (context, state) => const HealthCenterDetailPage(),
            ),
          ],
        ),

        // Instances
        StatefulShellBranch(
          navigatorKey: _healthCenterInstances,
          routes: [
            GoRoute(
              name: AppRoute.managerHealthCenterInstances,
              path: 'health-center/instances',
              builder: (context, state) => const HealthCenterInstancesPage(),
            ),
          ],
        ),

        // Companions
        StatefulShellBranch(
          navigatorKey: _healthCenterCompanions,
          routes: [
            GoRoute(
              path: 'health-center/companions',
              name: AppRoute.managerHealthCenterCompanions,
              builder: (context, state) => const HealthCenterMembersPage(
                memberType: MemberType.COMPANION,
              ),
            ),
          ],
        ),

        // APS page
        StatefulShellBranch(
          navigatorKey: _healthCenterAPS,
          routes: [
            GoRoute(
              name: AppRoute.managerHealthCenterAPS,
              path: 'health-center/aps',
              builder: (context, state) => const HealthCenterMembersPage(
                memberType: MemberType.APS,
              ),
            ),
          ],
        ),
      ],
    ),

    // Instance Stack
    GoRoute(
      path: 'instances/:id',
      name: AppRoute.managerInstance,
      builder: (context, state) =>
          InstanceShowScaffoldWrapper(instanceId: state.pathParameters['id']!),
      parentNavigatorKey: rootNavigatorKey,
    ),
  ],
);
