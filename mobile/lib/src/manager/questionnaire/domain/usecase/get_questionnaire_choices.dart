import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/questionnaire.dart';
import '../repository/questionnaire_repository.dart';

@injectable
class GetQuestionnaireChoices
    extends UseCase<List<QuestionnaireChoice>, GetQuestionnaireChoicesParams> {
  final QuestionnaireRepository _repository;

  GetQuestionnaireChoices(this._repository);

  @override
  call(GetQuestionnaireChoicesParams params) {
    return _repository.getQuestionnaireChoices(
      questionnaireId: params.questionnaireId,
    );
  }
}

class GetQuestionnaireChoicesParams {
  final String questionnaireId;

  GetQuestionnaireChoicesParams({required this.questionnaireId});
}
