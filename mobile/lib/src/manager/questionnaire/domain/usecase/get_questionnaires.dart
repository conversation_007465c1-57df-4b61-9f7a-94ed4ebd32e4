import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/questionnaire.dart';
import '../repository/questionnaire_repository.dart';

@injectable
class GetQuestionnaires extends UseCase<List<Questionnaire>, NoParams> {
  final QuestionnaireRepository _repository;

  GetQuestionnaires(this._repository);

  @override
  call(NoParams params) {
    return _repository.getQuestionnaires();
  }
}
