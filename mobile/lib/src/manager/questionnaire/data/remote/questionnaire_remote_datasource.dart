import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import 'models/questionnaire_model.dart';

abstract class QuestionnaireRemoteDataSource {
  Future<List<QuestionnaireChoiceModel>> getQuestionnaireChoices({
    required String questionnaireId,
  });

  Future<List<QuestionnaireModel>> getQuestionnaires();
}

@Injectable(as: QuestionnaireRemoteDataSource)
class QuestionnaireRemoteDataSourceImpl
    implements QuestionnaireRemoteDataSource {
  final Dio _httpClient;

  QuestionnaireRemoteDataSourceImpl(this._httpClient);

  @override
  Future<List<QuestionnaireChoiceModel>> getQuestionnaireChoices({
    required String questionnaireId,
  }) async {
    final response =
        await _httpClient.get('/questionnaires/$questionnaireId/choices');

    return (response.data['data'] as List<dynamic>)
        .map((item) => QuestionnaireChoiceModel.fromJson(item))
        .toList();
  }

  @override
  Future<List<QuestionnaireModel>> getQuestionnaires() async {
    final response = await _httpClient.get('/questionnaires');

    return (response.data['data'] as List<dynamic>)
        .map((item) => QuestionnaireModel.fromJson(item))
        .toList();
  }
}
