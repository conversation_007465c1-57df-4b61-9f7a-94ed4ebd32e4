// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/manager/questionnaire/domain/entity/questionnaire.dart';

part 'questionnaire_model.g.dart';

@JsonSerializable()
class QuestionnaireModel extends Questionnaire {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const QuestionnaireModel({
    required super.id,
    required super.question,
    required super.type,
    required super.hint,
    required this.createdAt,
    required this.updatedAt,
  }) : super(createdAt: createdAt, updatedAt: updatedAt);

  factory QuestionnaireModel.fromJson(Map<String, dynamic> json) =>
      _$QuestionnaireModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionnaireModelToJson(this);
}

@JsonSerializable()
class QuestionnaireChoiceModel extends QuestionnaireChoice {
  @override
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  @JsonKey(name: 'questionnaire_id')
  final String questionnaireId;

  const QuestionnaireChoiceModel({
    required super.id,
    required super.choice,
    required this.questionnaireId,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          questionnaireId: questionnaireId,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  factory QuestionnaireChoiceModel.fromJson(Map<String, dynamic> json) =>
      _$QuestionnaireChoiceModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionnaireChoiceModelToJson(this);
}
