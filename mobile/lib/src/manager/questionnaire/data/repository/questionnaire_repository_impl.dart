import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/repository/repository.dart';

import 'package:s3g/src/manager/questionnaire/domain/entity/questionnaire.dart';

import '../../domain/repository/questionnaire_repository.dart';
import '../remote/questionnaire_remote_datasource.dart';

@Injectable(as: QuestionnaireRepository)
class QuestionnaireRepositoryImpl extends QuestionnaireRepository {
  final QuestionnaireRemoteDataSource _remoteDataSource;

  QuestionnaireRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<List<QuestionnaireChoice>> getQuestionnaireChoices({
    required String questionnaireId,
  }) {
    return requestHelper(
      () => _remoteDataSource.getQuestionnaireChoices(
        questionnaireId: questionnaireId,
      ),
    );
  }

  @override
  RepositoryResponse<List<Questionnaire>> getQuestionnaires() {
    return requestHelper(() => _remoteDataSource.getQuestionnaires());
  }
}
