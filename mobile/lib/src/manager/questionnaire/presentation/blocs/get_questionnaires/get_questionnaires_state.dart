part of 'get_questionnaires_cubit.dart';

@immutable
sealed class GetQuestionnairesState {}

final class GetQuestionnairesInitial extends GetQuestionnairesState {}

final class GetQuestionnairesLoading extends GetQuestionnairesState {}

final class GetQuestionnairesLoaded extends GetQuestionnairesState {
  final List<Questionnaire> questionnaires;

  GetQuestionnairesLoaded(this.questionnaires);
}

final class GetQuestionnairesError extends GetQuestionnairesState {
  final String error;

  GetQuestionnairesError(this.error);
}
