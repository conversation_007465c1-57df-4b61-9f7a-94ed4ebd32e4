import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/questionnaire/domain/usecase/get_questionnaires.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

part 'get_questionnaires_state.dart';

@injectable
class GetQuestionnairesCubit extends Cubit<GetQuestionnairesState> {
  final GetQuestionnaires _getQuestionnaires;

  GetQuestionnairesCubit(this._getQuestionnaires)
      : super(GetQuestionnairesInitial());

  void getQuestionnaires() async {
    emit(GetQuestionnairesLoading());

    final result = await _getQuestionnaires(NoParams());

    result.fold(
      (l) => emit(GetQuestionnairesError(l.message)),
      (r) => emit(GetQuestionnairesLoaded(r)),
    );
  }
}
