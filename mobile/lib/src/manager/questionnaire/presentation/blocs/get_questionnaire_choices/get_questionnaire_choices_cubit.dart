import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/questionnaire/domain/entity/questionnaire.dart';
import 'package:s3g/src/manager/questionnaire/domain/usecase/get_questionnaire_choices.dart';

part 'get_questionnaire_choices_state.dart';

@injectable
class GetQuestionnaireChoicesCubit extends Cubit<GetQuestionnaireChoicesState> {
  final GetQuestionnaireChoices _getQuestionnaireChoices;

  GetQuestionnaireChoicesCubit(this._getQuestionnaireChoices)
      : super(GetQuestionnaireChoicesInitial());

  void getQuestionnaireChoices({required String questionnaireId}) async {
    emit(GetQuestionnaireChoicesLoading());

    final result = await _getQuestionnaireChoices(
      GetQuestionnaireChoicesParams(questionnaireId: questionnaireId),
    );

    result.fold(
      (failure) => emit(GetQuestionnaireChoicesError(failure.message)),
      (choices) => emit(GetQuestionnaireChoicesLoaded(choices)),
    );
  }
}
