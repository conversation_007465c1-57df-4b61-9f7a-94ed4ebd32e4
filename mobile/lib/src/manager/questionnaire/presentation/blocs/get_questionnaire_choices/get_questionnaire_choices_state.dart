part of 'get_questionnaire_choices_cubit.dart';

@immutable
sealed class GetQuestionnaireChoicesState {}

final class GetQuestionnaireChoicesInitial
    extends GetQuestionnaireChoicesState {}

final class GetQuestionnaireChoicesLoading
    extends GetQuestionnaireChoicesState {}

final class GetQuestionnaireChoicesLoaded extends GetQuestionnaireChoicesState {
  final List<QuestionnaireChoice> choices;

  GetQuestionnaireChoicesLoaded(this.choices);
}

final class GetQuestionnaireChoicesError extends GetQuestionnaireChoicesState {
  final String error;

  GetQuestionnaireChoicesError(this.error);
}
