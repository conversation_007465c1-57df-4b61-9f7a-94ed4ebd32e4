// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

import 'survivor_model.dart';

part 'instance_model.g.dart';

@JsonSerializable()
class InstanceModel extends Instance {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  @Json<PERSON>ey(name: 'survivor_code')
  final String survivorCode;

  @override
  @JsonKey(name: 'health_center_id')
  final String healthCenterId;

  @override
  @JsonKey(name: 'relapsed_at')
  final DateTime? relapsedAt;

  const InstanceModel({
    required super.id,
    required super.code,
    required super.type,
    required this.survivorCode,
    required this.relapsedAt,
    required this.healthCenterId,
    required super.description,
    required super.status,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
          relapsedAt: relapsedAt,
          survivorCode: survivorCode,
          healthCenterId: healthCenterId,
        );

  factory InstanceModel.fromJson(Map<String, dynamic> json) =>
      _$InstanceModelFromJson(json);

  Map<String, dynamic> toJson() => _$InstanceModelToJson(this);
}

@JsonSerializable()
class InstanceShowModel extends InstanceShow {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  final SurvivorModel survivor;

  @override
  final RelapseModel? relapse;

  @override
  @JsonKey(name: 'health_center')
  final HealthCenterModel healthCenter;

  @override
  @JsonKey(name: 'creator')
  final UserModel? creator;

  const InstanceShowModel({
    required super.id,
    required super.code,
    required super.type,
    required super.description,
    required super.status,
    required this.survivor,
    required this.relapse,
    required this.healthCenter,
    required this.creator,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
          survivor: survivor,
          relapse: relapse,
          healthCenter: healthCenter,
          creator: creator,
        );

  factory InstanceShowModel.fromJson(Map<String, dynamic> json) =>
      _$InstanceShowModelFromJson(json);

  Map<String, dynamic> toJson() => _$InstanceShowModelToJson(this);
}
