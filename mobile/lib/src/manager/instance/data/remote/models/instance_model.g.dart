// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'instance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InstanceModel _$InstanceModelFromJson(Map<String, dynamic> json) =>
    InstanceModel(
      id: json['id'] as String,
      code: json['code'] as String,
      type: $enumDecode(_$InstanceTypeEnumMap, json['type']),
      survivorCode: json['survivor_code'] as String,
      relapsedAt: json['relapsed_at'] == null
          ? null
          : DateTime.parse(json['relapsed_at'] as String),
      healthCenterId: json['health_center_id'] as String,
      description: json['description'] as String?,
      status: $enumDecode(_$InstanceStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$InstanceModelToJson(InstanceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'type': _$InstanceTypeEnumMap[instance.type]!,
      'status': _$InstanceStatusEnumMap[instance.status]!,
      'description': instance.description,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'survivor_code': instance.survivorCode,
      'health_center_id': instance.healthCenterId,
      'relapsed_at': instance.relapsedAt?.toIso8601String(),
    };

const _$InstanceTypeEnumMap = {
  InstanceType.NEW: 'NEW',
  InstanceType.RELAPSE: 'RELAPSE',
};

const _$InstanceStatusEnumMap = {
  InstanceStatus.OPEN: 'OPEN',
  InstanceStatus.CLOSED: 'CLOSED',
};

InstanceShowModel _$InstanceShowModelFromJson(Map<String, dynamic> json) =>
    InstanceShowModel(
      id: json['id'] as String,
      code: json['code'] as String,
      type: $enumDecode(_$InstanceTypeEnumMap, json['type']),
      description: json['description'] as String?,
      status: $enumDecode(_$InstanceStatusEnumMap, json['status']),
      survivor:
          SurvivorModel.fromJson(json['survivor'] as Map<String, dynamic>),
      relapse: json['relapse'] == null
          ? null
          : RelapseModel.fromJson(json['relapse'] as Map<String, dynamic>),
      healthCenter: HealthCenterModel.fromJson(
          json['health_center'] as Map<String, dynamic>),
      creator: json['creator'] == null
          ? null
          : UserModel.fromJson(json['creator'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$InstanceShowModelToJson(InstanceShowModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'type': _$InstanceTypeEnumMap[instance.type]!,
      'status': _$InstanceStatusEnumMap[instance.status]!,
      'description': instance.description,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'survivor': instance.survivor,
      'relapse': instance.relapse,
      'health_center': instance.healthCenter,
      'creator': instance.creator,
    };
