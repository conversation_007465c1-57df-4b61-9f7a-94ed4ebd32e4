// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';

import '../../../domain/entity/instance.dart';
part 'survivor_model.g.dart';

@JsonSerializable()
class SurvivorModel extends Survivor {
  @override
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const SurvivorModel({
    required super.id,
    required super.code,
    required super.description,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  factory SurvivorModel.fromJson(Map<String, dynamic> json) =>
      _$SurvivorModelFromJson(json);

  Map<String, dynamic> toJson() => _$SurvivorModelToJson(this);
}
