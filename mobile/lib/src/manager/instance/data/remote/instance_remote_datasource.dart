import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';

import '../../domain/entity/instance.dart';
import 'models/instance_model.dart';

abstract class InstanceRemoteDataSource {
  Future<Paginated<Instance>> getAllInstance({
    required int? page,
    required String? search,
  });

  Future<InstanceShow> getInstance({required String instanceId});

  Future<Paginated<Instance>> getInstances({
    required String healthCenterId,
    required int? page,
    required String? search,
  });

  Future<Paginated<Instance>> getInstancesRelapsed({
    required String healthCenterId,
    required int? page,
    required String? search,
  });

  Future<InstanceShow> createInstance({
    required String healthCenterId,
    required String survivorCode,
    required InstanceType type,
    required String? description,
  });

  Future<InstanceShow> editInstance({
    required String healthCenterId,
    required String instanceId,
    required InstanceStatus status,
    required String? description,
  });

  Future<MessageResponse> deleteInstance({
    required String healthCenterId,
    required String instanceId,
  });
}

@Injectable(as: InstanceRemoteDataSource)
class InstanceRemoteDataSourceImpl implements InstanceRemoteDataSource {
  final Dio _httpClient;

  InstanceRemoteDataSourceImpl(this._httpClient);

  @override
  Future<Paginated<Instance>> getAllInstance({
    required int? page,
    required String? search,
  }) async {
    final response = await _httpClient.get(
      '/health-centers',
      queryParameters: {"page": page, 'search': search},
    );

    return Paginated.fromJson(
      response.data,
      (data) => InstanceModel.fromJson(data as Map<String, dynamic>),
    );
  }

  @override
  Future<InstanceShow> getInstance({required String instanceId}) async {
    final response = await _httpClient.get(
      "/instances/$instanceId",
    );

    return InstanceShowModel.fromJson(response.data['data']);
  }

  @override
  Future<Paginated<Instance>> getInstances({
    required String healthCenterId,
    required int? page,
    required String? search,
  }) async {
    final response = await _httpClient.get(
      "/health-centers/$healthCenterId/instances",
      queryParameters: {"page": page, 'search': search},
    );

    return Paginated.fromJson(
      response.data,
      (data) => InstanceModel.fromJson(data as Map<String, dynamic>),
    );
  }

  @override
  Future<Paginated<Instance>> getInstancesRelapsed({
    required String healthCenterId,
    required int? page,
    required String? search,
  }) async {
    final response = await _httpClient.get(
      '/health-centers/$healthCenterId/instances-relapsed',
      queryParameters: {"page": page, 'search': search},
    );

    return Paginated.fromJson(
      response.data,
      (data) => InstanceModel.fromJson(data as Map<String, dynamic>),
    );
  }

  @override
  Future<InstanceShow> createInstance({
    required String healthCenterId,
    required String survivorCode,
    required InstanceType type,
    required String? description,
  }) async {
    final data = {
      "survivor_code": survivorCode,
      "type": type.name,
      "description": description
    };

    final response = await _httpClient
        .post('/health-centers/$healthCenterId/instances', data: data);

    return InstanceShowModel.fromJson(response.data['data']);
  }

  @override
  Future<InstanceShow> editInstance({
    required String healthCenterId,
    required String instanceId,
    required InstanceStatus status,
    required String? description,
  }) async {
    final data = {"status": status.name, "description": description};

    final response = await _httpClient.put(
      '/health-centers/$healthCenterId/instances/$instanceId',
      data: data,
    );

    return InstanceShowModel.fromJson(response.data['data']);
  }

  @override
  Future<MessageResponse> deleteInstance({
    required String healthCenterId,
    required String instanceId,
  }) async {
    final response = await _httpClient.delete(
      '/health-centers/$healthCenterId/instances/$instanceId',
    );

    return MessageResponse.fromJson(response.data);
  }
}
