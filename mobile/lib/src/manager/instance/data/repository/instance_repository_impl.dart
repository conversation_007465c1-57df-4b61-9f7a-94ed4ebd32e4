import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';

import 'package:s3g/core/repository/repository.dart';

import 'package:s3g/src/manager/instance/domain/entity/instance.dart';

import '../../domain/repository/instance_repository.dart';
import '../remote/instance_remote_datasource.dart';

@Injectable(as: InstanceRepository)
class InstanceRepositoryImpl implements InstanceRepository {
  final InstanceRemoteDataSource _remoteDataSource;

  InstanceRepositoryImpl({required InstanceRemoteDataSource remoteDataSource})
      : _remoteDataSource = remoteDataSource;

  @override
  RepositoryResponse<Paginated<Instance>> getAllInstance({
    int? page,
    String? search,
  }) {
    return requestHelper(() => _remoteDataSource.getAllInstance(
          page: page,
          search: search,
        ));
  }

  @override
  RepositoryResponse<InstanceShow> getInstance({required String instanceId}) {
    return requestHelper(
      () => _remoteDataSource.getInstance(instanceId: instanceId),
    );
  }

  @override
  RepositoryResponse<Paginated<Instance>> getInstances({
    required String healthCenterId,
    int? page,
    String? search,
  }) {
    return requestHelper(
      () => _remoteDataSource.getInstances(
        healthCenterId: healthCenterId,
        page: page,
        search: search,
      ),
    );
  }

  @override
  RepositoryResponse<Paginated<Instance>> getInstancesRelapsed({
    required String healthCenterId,
    int? page,
    String? search,
  }) {
    return requestHelper(
      () => _remoteDataSource.getInstancesRelapsed(
        healthCenterId: healthCenterId,
        page: page,
        search: search,
      ),
    );
  }

  @override
  RepositoryResponse<InstanceShow> createInstance({
    required String healthCenterId,
    required String survivorCode,
    required InstanceType type,
    String? description,
  }) {
    return requestHelper(
      () => _remoteDataSource.createInstance(
        healthCenterId: healthCenterId,
        survivorCode: survivorCode,
        type: type,
        description: description,
      ),
    );
  }

  @override
  RepositoryResponse<InstanceShow> editInstance({
    required String healthCenterId,
    required String instanceId,
    required InstanceStatus status,
    String? description,
  }) {
    return requestHelper(
      () => _remoteDataSource.editInstance(
        healthCenterId: healthCenterId,
        instanceId: instanceId,
        status: status,
        description: description,
      ),
    );
  }

  @override
  RepositoryResponse<MessageResponse> deleteInstance({
    required String healthCenterId,
    required String instanceId,
  }) {
    return requestHelper(
      () => _remoteDataSource.deleteInstance(
        healthCenterId: healthCenterId,
        instanceId: instanceId,
      ),
    );
  }
}
