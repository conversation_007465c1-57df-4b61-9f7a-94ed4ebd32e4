import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../../instance.dart';
import '../repository/instance_repository.dart';

@injectable
class GetInstances extends UseCase<Paginated<Instance>, GetInstancesParams> {
  final InstanceRepository repository;

  GetInstances({required this.repository});

  @override
  call(GetInstancesParams params) {
    return repository.getInstances(
      healthCenterId: params.healthCenterId,
      page: params.page,
      search: params.search,
    );
  }
}

class GetInstancesParams extends PaginationParams {
  final String healthCenterId;

  GetInstancesParams({
    super.page,
    super.search,
    required this.healthCenterId,
  });
}
