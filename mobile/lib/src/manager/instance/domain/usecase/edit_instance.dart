import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/instance.dart';
import '../repository/instance_repository.dart';

@injectable
class EditInstance extends UseCase<InstanceShow, EditInstanceParams> {
  final InstanceRepository _repository;

  EditInstance(this._repository);

  @override
  call(EditInstanceParams params) {
    return _repository.editInstance(
      healthCenterId: params.healthCenterId,
      instanceId: params.instanceId,
      status: params.status,
      description: params.description,
    );
  }
}

class EditInstanceParams {
  final String healthCenterId;
  final String instanceId;
  final InstanceStatus status;
  final String? description;

  EditInstanceParams({
    required this.healthCenterId,
    required this.instanceId,
    required this.status,
    required this.description,
  });
}
