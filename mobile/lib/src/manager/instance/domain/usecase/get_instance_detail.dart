import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../../instance.dart';
import '../repository/instance_repository.dart';

@injectable
class GetInstanceDetail extends UseCase<InstanceShow, GetInstanceDetailParams> {
  final InstanceRepository repository;

  GetInstanceDetail(this.repository);

  @override
  call(GetInstanceDetailParams params) async {
    return repository.getInstance(instanceId: params.instanceId);
  }
}

class GetInstanceDetailParams {
  final String instanceId;

  GetInstanceDetailParams({required this.instanceId});
}
