import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../../instance.dart';
import '../repository/instance_repository.dart';

@injectable
class GetAllInstance extends UseCase<Paginated<Instance>, PaginationParams> {
  final InstanceRepository repository;

  GetAllInstance(this.repository);

  @override
  call(PaginationParams params) {
    return repository.getAllInstance(
      page: params.page,
      search: params.search,
    );
  }
}
