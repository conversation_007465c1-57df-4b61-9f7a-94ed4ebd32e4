import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../../instance.dart';
import '../repository/instance_repository.dart';

@injectable
class GetInstancesRelapsed
    extends UseCase<Paginated<Instance>, GetInstancesRelapsedParams> {
  final InstanceRepository repository;

  GetInstancesRelapsed({required this.repository});

  @override
  call(GetInstancesRelapsedParams params) {
    return repository.getInstancesRelapsed(
      healthCenterId: params.healthCenterId,
      page: params.page,
      search: params.search,
    );
  }
}

class GetInstancesRelapsedParams extends PaginationParams {
  final String healthCenterId;

  GetInstancesRelapsedParams({
    super.page,
    super.search,
    required this.healthCenterId,
  });
}
