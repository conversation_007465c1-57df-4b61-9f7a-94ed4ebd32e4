import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/instance.dart';
import '../repository/instance_repository.dart';

@injectable
class CreateInstance extends UseCase<InstanceShow, CreateInstanceParams> {
  final InstanceRepository _repository;

  CreateInstance(this._repository);

  @override
  call(CreateInstanceParams params) {
    return _repository.createInstance(
      healthCenterId: params.healthCenterId,
      survivorCode: params.survivorCode,
      type: params.type,
      description: params.description,
    );
  }
}

class CreateInstanceParams {
  final String healthCenterId;
  final String survivorCode;
  final InstanceType type;
  final String? description;

  CreateInstanceParams({
    required this.healthCenterId,
    required this.survivorCode,
    required this.type,
    required this.description,
  });
}
