import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/instance_repository.dart';

@injectable
class DeleteInstance extends UseCase<MessageResponse, DeleteInstanceParams> {
  final InstanceRepository _repository;

  DeleteInstance(this._repository);

  @override
  call(DeleteInstanceParams params) {
    return _repository.deleteInstance(
      instanceId: params.instanceId,
      healthCenterId: params.healthCenterId,
    );
  }
}

class DeleteInstanceParams {
  final String instanceId;
  final String healthCenterId;

  DeleteInstanceParams({
    required this.instanceId,
    required this.healthCenterId,
  });
}
