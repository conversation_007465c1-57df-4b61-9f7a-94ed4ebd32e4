import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';

import '../../instance.dart';

abstract class InstanceRepository {
  RepositoryResponse<Paginated<Instance>> getAllInstance({
    required int? page,
    required String? search,
  });

  RepositoryResponse<InstanceShow> getInstance({required String instanceId});

  RepositoryResponse<Paginated<Instance>> getInstances({
    required String healthCenterId,
    required int? page,
    required String? search,
  });

  RepositoryResponse<Paginated<Instance>> getInstancesRelapsed({
    required String healthCenterId,
    required int? page,
    required String? search,
  });

  RepositoryResponse<InstanceShow> createInstance({
    required String healthCenterId,
    required String survivorCode,
    required InstanceType type,
    required String? description,
  });

  RepositoryResponse<InstanceShow> editInstance({
    required String healthCenterId,
    required String instanceId,
    required InstanceStatus status,
    required String? description,
  });

  RepositoryResponse<MessageResponse> deleteInstance({
    required String healthCenterId,
    required String instanceId,
  });
}
