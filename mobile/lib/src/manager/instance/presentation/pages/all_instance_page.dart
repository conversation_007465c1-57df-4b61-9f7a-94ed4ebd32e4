import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/presentation/blocs/all_instances/all_instances_bloc.dart';

class AllInstancePage extends StatelessWidget {
  const AllInstancePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          getIt<AllInstancesBloc>()..add(PaginatedItemFetched()),
      child: const AllInstancePageView(),
    );
  }
}

class AllInstancePageView extends StatelessWidget {
  const AllInstancePageView({super.key});

  @override
  Widget build(BuildContext context) {
    return const Placeholder();
  }
}
