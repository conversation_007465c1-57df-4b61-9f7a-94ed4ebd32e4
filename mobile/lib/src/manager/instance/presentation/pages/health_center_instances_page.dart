import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/health_center/presentation/blocs/health_center/health_center_cubit.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance/presentation/blocs/blocs.dart';

import '../widgets/instance_form.dart';
import '../widgets/instance_type_item.dart';
import '../widgets/list_instance_item.dart';

class HealthCenterInstancesPage extends StatelessWidget {
  const HealthCenterInstancesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final healthCenter =
        context.watch<HealthCenterCubit>().state.selectedHealthCenter!;

    return MultiBlocProvider(
      key: ValueKey(healthCenter.id),
      providers: [
        BlocProvider(
          create: (context) =>
              InstancesBloc(getIt(), healthCenterId: healthCenter.id)
                ..add(PaginatedItemFetched()),
        ),
        BlocProvider(
          create: (context) =>
              InstancesRelapsedBloc(getIt(), healthCenterId: healthCenter.id)
                ..add(PaginatedItemFetched()),
        ),
        BlocProvider(
          create: (context) => InlineFormCubit<Instance>(),
        ),
        BlocProvider(
          create: (context) => getIt<DeleteInstanceCubit>(),
        ),
      ],
      child: BlocListener<RefreshDataBloc, RefreshDataState>(
        listener: (context, state) {
          if (state case RefreshDataHealthCenterInstances()) {
            context
                .read<InstancesBloc>()
                .add(PaginatedItemFetched(refresh: true));

            context
                .read<InstancesRelapsedBloc>()
                .add(PaginatedItemFetched(refresh: true));
          }
        },
        child: _HealthCenterInstancesView(healthCenter),
      ),
    );
  }
}

class _HealthCenterInstancesView extends StatefulWidget {
  final HealthCenter healthCenter;

  const _HealthCenterInstancesView(this.healthCenter);

  @override
  State<_HealthCenterInstancesView> createState() =>
      _HealthCenterInstancesViewState();
}

class _HealthCenterInstancesViewState
    extends State<_HealthCenterInstancesView> {
  String dropdownValue = InstanceTypeFilter.all.text;
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final inlineForm = context.watch<InlineFormCubit<Instance>>();

    if (inlineForm.state.enabled) {
      return SingleChildScrollView(
        physics: const ScrollPhysics(),
        padding: bodyPadding,
        child: InstanceForm(
          healthCenter: widget.healthCenter,
          editable: inlineForm.state.editable,
          closeEditMode: () {
            inlineForm.disableForm();
          },
        ),
      );
    }

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          if (dropdownValue == InstanceTypeFilter.all.text) {
            context
                .read<InstancesBloc>()
                .add(PaginatedItemFetched(refresh: true));
          }

          if (dropdownValue == InstanceTypeFilter.relapse.text) {
            context
                .read<InstancesRelapsedBloc>()
                .add(PaginatedItemFetched(refresh: true));
          }
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: bodyPadding,
          controller: _scrollController,
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: SearchInput(
                      dropdownValue: dropdownValue,
                    ),
                  ),
                  const SizedBox(width: 20),
                  Container(
                    width: 100,
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: InstanceTypeItem(
                      dropdownValue: dropdownValue,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            dropdownValue = value;
                          });
                        }
                      },
                    ),
                  )
                ],
              ),

              // All instances
              Visibility(
                visible: dropdownValue == InstanceTypeFilter.all.text,
                child: InstanceItems(
                  scrollController: _scrollController,
                  healthCenter: widget.healthCenter,
                ),
              ),

              // Relapses
              Visibility(
                visible: dropdownValue == InstanceTypeFilter.relapse.text,
                child: RelapseItems(
                  scrollController: _scrollController,
                  healthCenter: widget.healthCenter,
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "HealthCenterInstancesPage",
        onPressed: () {
          inlineForm.enableForm();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}

class SearchInput extends StatefulWidget {
  final String dropdownValue;

  const SearchInput({
    super.key,
    required this.dropdownValue,
  });

  @override
  State<SearchInput> createState() => _SearchInputState();
}

class _SearchInputState extends State<SearchInput> with WidgetsBindingObserver {
  final FocusNode inputFocusNode = FocusNode(canRequestFocus: false);

  @override
  void dispose() {
    inputFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      keyboardType: TextInputType.text,
      focusNode: inputFocusNode,
      onTapOutside: (event) {
        inputFocusNode.unfocus();
      },
      onChanged: (value) {
        if (widget.dropdownValue == InstanceTypeFilter.all.text) {
          context
              .read<InstancesBloc>()
              .add(PaginatedItemSearch(query: value.trim()));
        }

        if (widget.dropdownValue == InstanceTypeFilter.relapse.text) {
          context
              .read<InstancesRelapsedBloc>()
              .add(PaginatedItemSearch(query: value.trim()));
        }
      },
      decoration: const InputDecoration(
        border: UnderlineInputBorder(),
        labelText: 'Recherche',
      ),
    );
  }
}

class InstanceItems extends StatelessWidget {
  final HealthCenter healthCenter;
  const InstanceItems({
    super.key,
    required this.scrollController,
    required this.healthCenter,
  });

  final ScrollController scrollController;

  @override
  Widget build(BuildContext context) {
    return PaginatedWidget<InstancesBloc, Instance>(
      scrollController: scrollController,
      widgetEmpty: const EmptyList(
        message: "Pas de cas enregistré.",
      ),
      render: (_, __, state) {
        return ListInstanceItem(
          state: state,
          healthCenter: healthCenter,
        );
      } as WidgetRender<Instance>,
    );
  }
}

class RelapseItems extends StatelessWidget {
  final HealthCenter healthCenter;
  final ScrollController scrollController;

  const RelapseItems({
    super.key,
    required this.scrollController,
    required this.healthCenter,
  });

  @override
  Widget build(BuildContext context) {
    return PaginatedWidget<InstancesRelapsedBloc, Instance>(
      scrollController: scrollController,
      widgetEmpty: const EmptyList(
        message: "Aucune rechute enregistré\npour le moment.",
      ),
      render: (_, __, state) {
        return ListInstanceItem(
          state: state,
          healthCenter: healthCenter,
        );
      } as WidgetRender<Instance>,
    );
  }
}
