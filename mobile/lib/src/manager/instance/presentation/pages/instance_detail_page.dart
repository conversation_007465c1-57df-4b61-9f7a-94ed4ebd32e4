import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

import '../../domain/usecase/edit_instance.dart';
import '../blocs/blocs.dart';
import '../widgets/instance_relapse.dart';

class InstanceDetailPage extends StatelessWidget {
  const InstanceDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final instance =
        (context.read<InstanceDetailCubit>().state as InstanceDetailLoaded)
            .instance;

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<DeleteInstanceCubit>(),
        ),
        BlocProvider(
          create: (context) => getIt<EditInstanceCubit>(),
        ),
        BlocProvider(
          create: (context) => InlineFormCubit<InstanceShow>(),
        ),
        BlocProvider(
          key: ValueKey(instance.id),
          create: (context) => RelapseCubit(
            instance.id,
            createRelapseUseCase: getIt(),
            updateRelapseUseCase: getIt(),
            deleteRelapseUseCase: getIt(),
            getRelapseUseCase: getIt(),
          ),
        )
      ],
      child: _InstanceDetailContent(instance),
    );
  }
}

class _InstanceDetailContent extends StatelessWidget {
  final InstanceShow instance;

  const _InstanceDetailContent(this.instance);

  @override
  Widget build(BuildContext context) {
    final editMode = context.watch<InlineFormCubit<InstanceShow>>();

    if (editMode.state.enabled) {
      return SingleChildScrollView(
        padding: bodyPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const PageTitle(title: "Rechute"),
            InstanceRelapse(
              instance: instance,
              closeEditMode: editMode.disableForm,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<InstanceDetailCubit>().getInstance();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: bodyPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (instance.relapse != null)
                  GFButton(
                    onPressed: editMode.enableForm,
                    color: GFColors.ALT,
                    shape: GFButtonShape.standard,
                    type: GFButtonType.outline,
                    text: "Voir la rechute",
                  ),

                if (instance.relapse == null)
                  GFButton(
                    onPressed: editMode.enableForm,
                    color: GFColors.DANGER,
                    shape: GFButtonShape.standard,
                    type: GFButtonType.outline,
                    text: "Signaler une rechute",
                  ),

                // close the case
                Row(
                  children: [
                    // Change status
                    if (instance.status == InstanceStatus.OPEN)
                      GFButton(
                        onPressed: () => _editInstanceStatus(
                          context,
                          status: InstanceStatus.CLOSED,
                        ),
                        shape: GFButtonShape.standard,
                        color: Colors.grey[800] ?? Colors.grey,
                        type: GFButtonType.outline,
                        text: "Decharger le cas",
                      ),
                    if (instance.status == InstanceStatus.CLOSED)
                      GFButton(
                        onPressed: () => _editInstanceStatus(
                          context,
                          status: InstanceStatus.OPEN,
                        ),
                        shape: GFButtonShape.standard,
                        color: GFColors.INFO,
                        type: GFButtonType.outline,
                        text: "Relancer le cas",
                      ),

                    // Some space
                    const SizedBox(width: 10.0),

                    // Delete the case
                    GFIconButton(
                      onPressed: () => _onDelete(context),
                      size: GFSize.SMALL,
                      color: GFColors.DANGER,
                      type: GFButtonType.outline,
                      icon: const Icon(Icons.delete),
                    )
                  ],
                ),
              ],
            ),

            // Relapse at
            if (instance.relapse != null) ...[
              columnSizedBox,
              DetailTitle(
                leading: const Icon(Icons.trending_down_outlined),
                titleText: "Rechute signalé le",
                subtitle: Text(
                  DateFormat.yMMMd('fr').format(
                    instance.relapse!.createdAt.toLocal(),
                  ),
                  style: const TextStyle(
                    fontSize: 16.0,
                    color: Colors.red,
                  ),
                ),
              ),
              columnSizedBox,
              const GreyDivider(),
            ],

            // Survivor code
            columnSizedBox,
            DetailTitle(
              leading: const Icon(Icons.info),
              titleText: "Code Survivant",
              subtitleText: instance.survivor.code,
            ),
            columnSizedBox,
            const GreyDivider(),

            // Case code
            columnSizedBox,
            DetailTitle(
              leading: const Icon(Icons.cases_outlined),
              titleText: "Code Cas",
              subtitleText: instance.code,
            ),
            columnSizedBox,
            const GreyDivider(),

            // Case type
            columnSizedBox,
            DetailTitle(
              leading: const Icon(Icons.difference_outlined),
              titleText: "Type Cas",
              subtitleText: instance.type.getLabel(),
            ),
            columnSizedBox,
            const GreyDivider(),

            // Case type
            columnSizedBox,
            DetailTitle(
              leading: switch (instance.status) {
                InstanceStatus.OPEN => const Icon(Icons.folder),
                InstanceStatus.CLOSED => const Icon(Icons.folder_off),
              },
              titleText: "Statut Cas",
              subtitleText: instance.status.getLabel(),
            ),
            columnSizedBox,
            const GreyDivider(),

            // Description
            if (instance.description != null) ...[
              columnSizedBox,
              DetailTitle(
                leading: const Icon(Icons.description),
                titleText: "Description du cas",
                subtitleText: instance.description,
              ),
              columnSizedBox,
              const GreyDivider(),
            ],

            // Assigned at
            columnSizedBox,
            DetailTitle(
              leading: const Icon(Icons.health_and_safety),
              titleText: "Centre de santé",
              subtitleText: instance.healthCenter.name,
            ),
            columnSizedBox,
            const GreyDivider(),

            // Assigned at
            columnSizedBox,
            DetailTitle(
              leading: const Icon(Icons.date_range),
              titleText: "Créé le",
              subtitleText: DateFormat.yMMMd('fr').format(
                instance.createdAt.toLocal(),
              ),
            ),
            columnSizedBox,
            const GreyDivider(),

            ..._listeners(context)
          ],
        ),
      ),
    );
  }

  void _editInstanceStatus(
    BuildContext context, {
    required InstanceStatus status,
  }) async {
    final confirmed = await pressConfirm(
      context,
      content: status == InstanceStatus.OPEN
          ? "Voulez-vous relancer le cas ?"
          : "Voulez-vous vraiment decharger le cas ? ",
    );

    if (confirmed == false) return;

    // ignore: use_build_context_synchronously
    context.read<EditInstanceCubit>().editInstance(
          EditInstanceParams(
            healthCenterId: instance.healthCenter.id,
            instanceId: instance.id,
            status: status,
            description: instance.description,
          ),
        );
  }

  void _onDelete(BuildContext context) async {
    final confirmed = await pressConfirm(context);

    if (confirmed) {
      // ignore: use_build_context_synchronously
      context.read<DeleteInstanceCubit>().deleteInstance(
            healthCenterId: instance.healthCenter.id,
            instanceId: instance.id,
          );
    }
  }

  List<Widget> _listeners(BuildContext context) {
    return [
      // Show edit toasts message
      BlocListener<EditInstanceCubit, EditInstanceState>(
        listener: (_, state) {
          switch (state) {
            case EditInstanceLoading():
              showToast(
                context,
                type: ToastType.info,
                message: instance.status == InstanceStatus.OPEN
                    ? "Déchargement.."
                    : "Relancement..",
              );
              break;

            case EditInstanceSuccess():
              showToast(
                context,
                type: ToastType.success,
                message: instance.status == InstanceStatus.OPEN
                    ? "Déchargé avec succès"
                    : "Relancé avec succès",
              );

              context.read<InstanceDetailCubit>().getInstance();

              context
                  .read<RefreshDataBloc>()
                  .add(RefreshDataHealthCenterInstancesEvent());
              break;

            case EditInstanceError():
              showToast(
                context,
                message: state.error,
                type: ToastType.error,
              );
              break;

            default:
          }
        },
        child: const SizedBox.shrink(),
      ),

      // Show delete toasts message
      BlocListener<DeleteInstanceCubit, DeleteInstanceState>(
        listener: (_, state) {
          switch (state) {
            case DeleteInstanceLoading():
              showToast(
                context,
                type: ToastType.info,
                message: "Suppression..",
              );
              break;

            case DeleteInstanceError():
              showToast(
                context,
                type: ToastType.error,
                message: state.error,
              );
              break;

            case DeleteInstanceSuccess(message: String message):
              showToast(
                context,
                type: ToastType.success,
                message: message,
              );

              // Refresh the list after deletion
              context
                  .read<RefreshDataBloc>()
                  .add(RefreshDataHealthCenterInstancesEvent());

              // Close the dialog
              context.pop();
              break;
            default:
          }
        },
        child: const SizedBox.shrink(),
      ),
    ];
  }
}
