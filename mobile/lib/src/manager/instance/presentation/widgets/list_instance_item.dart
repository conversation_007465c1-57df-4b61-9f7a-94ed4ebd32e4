import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/instance/instance.dart';

import '../blocs/delete_instance/delete_instance_cubit.dart';
import '../blocs/instances/instances_bloc.dart';
import '../blocs/instances_relapsed/instances_relapsed_bloc.dart';

class ListInstanceItem extends StatelessWidget {
  const ListInstanceItem({
    super.key,
    required this.state,
    required this.healthCenter,
  });

  final PaginatedItemState<Instance> state;
  final HealthCenter healthCenter;

  @override
  Widget build(BuildContext context) {
    return BlocListener<DeleteInstanceCubit, DeleteInstanceState>(
      listener: (_, state) {
        switch (state) {
          case DeleteInstanceLoading():
            showToast(
              context,
              type: ToastType.info,
              message: "Suppression..",
            );
            break;

          case DeleteInstanceError():
            showToast(
              context,
              type: ToastType.error,
              message: state.error,
            );
            break;

          case DeleteInstanceSuccess(message: String message):
            showToast(
              context,
              type: ToastType.success,
              message: message,
            );

            // Refresh the list after deletion
            context
                .read<InstancesBloc>()
                .add(PaginatedItemFetched(refresh: true));

            context
                .read<InstancesRelapsedBloc>()
                .add(PaginatedItemFetched(refresh: true));
            break;
          default:
        }
      },
      child: ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: state.itemCountWithLoader,
        itemBuilder: (context, index) {
          if (index >= state.items.length) {
            return const Center(child: GFLoader());
          }

          final instance = state.items[index];

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Slidable(
              endActionPane: ActionPane(
                motion: const ScrollMotion(),
                extentRatio: 0.5,
                children: [
                  SlidableAction(
                    onPressed: (_) {
                      context
                          .read<InlineFormCubit<Instance>>()
                          .enableForm(editable: instance);
                    },
                    backgroundColor: const Color(0xFF3B82F6),
                    foregroundColor: Colors.white,
                    icon: Icons.edit_outlined,
                    label: 'Modifier',
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      bottomLeft: Radius.circular(12),
                    ),
                  ),
                  SlidableAction(
                    onPressed: (_) => _onDelete(context, instance),
                    backgroundColor: AppTheme.primary,
                    foregroundColor: Colors.white,
                    icon: Icons.delete_outline,
                    label: 'Supprimer',
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                ],
              ),
              child: InstanceItem(
                instance: instance,
              ),
            ),
          );
        },
      ),
    );
  }

  void _onDelete(BuildContext context, Instance instance) async {
    final confirmed = await pressConfirm(context);

    if (confirmed) {
      // ignore: use_build_context_synchronously
      context.read<DeleteInstanceCubit>().deleteInstance(
            healthCenterId: healthCenter.id,
            instanceId: instance.id,
          );
    }
  }
}

class InstanceItem extends StatelessWidget {
  final Instance instance;
  const InstanceItem({super.key, required this.instance});

  @override
  Widget build(BuildContext context) {
    final bool isOpen = instance.status == InstanceStatus.OPEN;
    final bool hasRelapse = instance.relapsedAt != null;

    return InkWell(
      onTap: () {
        context.pushNamed(
          AppRoute.managerInstance,
          pathParameters: {'id': instance.id},
        );
      },
      borderRadius: BorderRadius.circular(cardRadius),
      child: Container(
        padding: cardSpacing,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(cardRadius),
          border: Border.all(
            color: hasRelapse
                ? AppTheme.primary.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.08),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Status Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isOpen
                    ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                isOpen ? Icons.folder_open : Icons.folder_off,
                color: isOpen ? const Color(0xFF3B82F6) : Colors.grey[600],
                size: 24,
              ),
            ),
            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Row
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "Survivant: ${instance.survivorCode}",
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1F2937),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (hasRelapse) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppTheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Text(
                            "Rechute",
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.primary,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Instance Code
                  Text(
                    instance.code,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Bottom Info Row
                  Row(
                    children: [
                      // Date
                      Expanded(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 14,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(width: 4),
                            Flexible(
                              child: LocalTimeago(
                                date: instance.createdAt,
                                builder: (_, value) => Text(
                                  value,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[500],
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(width: 8),

                      // Status Badge
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isOpen
                              ? const Color(0xFF059669).withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          instance.status.getLabel(),
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                            color: isOpen
                                ? const Color(0xFF059669)
                                : Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Arrow
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
