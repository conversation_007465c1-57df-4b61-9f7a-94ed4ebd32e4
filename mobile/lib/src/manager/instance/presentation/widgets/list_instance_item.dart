import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/instance/instance.dart';

import '../blocs/delete_instance/delete_instance_cubit.dart';
import '../blocs/instances/instances_bloc.dart';
import '../blocs/instances_relapsed/instances_relapsed_bloc.dart';

class ListInstanceItem extends StatelessWidget {
  const ListInstanceItem({
    super.key,
    required this.state,
    required this.healthCenter,
  });

  final PaginatedItemState<Instance> state;
  final HealthCenter healthCenter;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: state.itemCountWithLoader,
      itemBuilder: (context, index) {
        if (index >= state.items.length) {
          const Center(child: GFLoader());
        }

        final instance = state.items[index];

        return Column(
          children: [
            // Some Space
            const SizedBox(height: 10),

            // List title
            Slidable(
              endActionPane: ActionPane(
                motion: const ScrollMotion(),
                children: [
                  SlidableAction(
                    onPressed: (_) {
                      context
                          .read<InlineFormCubit<Instance>>()
                          .enableForm(editable: instance);
                    },
                    backgroundColor: GFColors.INFO,
                    foregroundColor: Colors.white,
                    icon: Icons.edit,
                    label: 'Modifier',
                  ),
                  SlidableAction(
                    onPressed: (_) => _onDelete(context, instance),
                    backgroundColor: GFColors.DANGER,
                    foregroundColor: Colors.white,
                    icon: Icons.delete,
                    label: 'Supprimer',
                  ),
                ],
              ),
              child: InstanceItem(
                instance: instance,
              ),
            ),

            // Some Space
            const SizedBox(height: 5),

            const GreyDivider(),

            // Show delete message
            BlocListener<DeleteInstanceCubit, DeleteInstanceState>(
              listener: (_, state) {
                switch (state) {
                  case DeleteInstanceLoading():
                    showToast(
                      context,
                      type: ToastType.info,
                      message: "Suppression..",
                    );
                    break;

                  case DeleteInstanceError():
                    showToast(
                      context,
                      type: ToastType.error,
                      message: state.error,
                    );
                    break;

                  case DeleteInstanceSuccess(message: String message):
                    showToast(
                      context,
                      type: ToastType.success,
                      message: message,
                    );

                    // Refresh the list after deletion
                    context
                        .read<InstancesBloc>()
                        .add(PaginatedItemFetched(refresh: true));

                    context
                        .read<InstancesRelapsedBloc>()
                        .add(PaginatedItemFetched(refresh: true));
                    break;
                  default:
                }
              },
              child: const SizedBox.shrink(),
            ),
          ],
        );
      },
    );
  }

  void _onDelete(BuildContext context, Instance instance) async {
    final confirmed = await pressConfirm(context);

    if (confirmed) {
      // ignore: use_build_context_synchronously
      context.read<DeleteInstanceCubit>().deleteInstance(
            healthCenterId: healthCenter.id,
            instanceId: instance.id,
          );
    }
  }
}

class InstanceItem extends StatelessWidget {
  final Instance instance;
  const InstanceItem({super.key, required this.instance});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      // Remove vertical space
      contentPadding: const EdgeInsets.symmetric(vertical: 0),
      onTap: () {
        context.pushNamed(
          AppRoute.managerInstance,
          pathParameters: {'id': instance.id},
        );
      },

      // Icon status
      leading: switch (instance.status) {
        InstanceStatus.OPEN => Tooltip(
            message: instance.status.getLabel(),
            child: const GFAvatar(
              size: 27.0,
              child: Icon(Icons.folder_open),
            ),
          ),
        InstanceStatus.CLOSED => Tooltip(
            message: instance.status.getLabel(),
            child: GFAvatar(
              backgroundColor: Colors.grey[400],
              size: 27.0,
              child: Icon(Icons.folder_off, color: Colors.grey[700]),
            ),
          ),
      },

      // Survivor Code
      title: Text("Survivant: ${instance.survivorCode}"),

      // subtitle
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            instance.code,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),

          // Relapse date
          if (instance.relapsedAt != null)
            RelapseDate(createdAt: instance.createdAt),

          const SizedBox(height: 10),
          LocalTimeago(
            date: instance.createdAt,
            builder: (_, value) => Text(value,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                )),
          )
        ],
      ),
    );
  }
}
