import 'package:flutter/material.dart';

enum InstanceTypeFilter {
  all("Tous"),
  relapse("Rechutes");

  final String text;

  const InstanceTypeFilter(this.text);
}

class InstanceTypeItem extends StatelessWidget {
  final String dropdownValue;
  final void Function(String? value) onChanged;

  const InstanceTypeItem({
    super.key,
    required this.onChanged,
    required this.dropdownValue,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton<String>(
      value: dropdownValue,
      icon: const Icon(Icons.filter_alt_outlined),
      elevation: 16,
      iconSize: 20,
      isExpanded: true,
      style: const TextStyle(
        overflow: TextOverflow.ellipsis,
        color: Colors.black,
        fontSize: 12,
      ),
      underline: Container(
        color: Colors.transparent,
      ),
      onChanged: onChanged,
      items: InstanceTypeFilter.values
          .map<DropdownMenuItem<String>>((InstanceTypeFilter value) {
        return DropdownMenuItem<String>(
          value: value.text,
          child: Text(
            value.text,
            style: const TextStyle(
              overflow: TextOverflow.ellipsis,
              fontSize: 12,
            ),
          ),
        );
      }).toList(),
    );
  }
}
