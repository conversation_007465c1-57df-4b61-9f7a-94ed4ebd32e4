import 'package:flutter/material.dart';

enum InstanceTypeFilter {
  all("Tous"),
  relapse("Rechu<PERSON>");

  final String text;

  const InstanceTypeFilter(this.text);
}

class InstanceTypeItem extends StatelessWidget {
  final String dropdownValue;
  final void Function(String? value) onChanged;

  const InstanceTypeItem({
    super.key,
    required this.onChanged,
    required this.dropdownValue,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: dropdownValue,
          icon: const Icon(
            Icons.filter_alt_outlined,
            color: Color(0xFF3B82F6),
            size: 16,
          ),
          elevation: 8,
          isDense: true,
          style: const TextStyle(
            color: Color(0xFF3B82F6),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          onChanged: onChanged,
          items: InstanceTypeFilter.values
              .map<DropdownMenuItem<String>>((InstanceTypeFilter value) {
            return DropdownMenuItem<String>(
              value: value.text,
              child: Text(
                value.text,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF3B82F6),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
