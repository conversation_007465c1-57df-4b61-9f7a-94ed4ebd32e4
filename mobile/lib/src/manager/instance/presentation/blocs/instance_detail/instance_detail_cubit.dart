import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/manager/instance/domain/usecase/get_instance_detail.dart';

part 'instance_detail_state.dart';

class InstanceDetailCubit extends Cubit<InstanceDetailState> {
  final GetInstanceDetail _getInstanceDetail;
  final String instanceId;

  InstanceDetailCubit(this._getInstanceDetail, {required this.instanceId})
      : super(InstanceDetailInitial());

  void getInstance() async {
    emit(InstanceDetailLoading());

    final response = await _getInstanceDetail(GetInstanceDetailParams(
      instanceId: instanceId,
    ));

    response.fold(
      (failure) => emit(InstanceDetailError(message: failure.message)),
      (instance) => emit(InstanceDetailLoaded(instance: instance)),
    );
  }
}
