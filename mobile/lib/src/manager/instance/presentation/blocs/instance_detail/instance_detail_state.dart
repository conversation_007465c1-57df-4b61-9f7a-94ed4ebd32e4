part of 'instance_detail_cubit.dart';

@immutable
sealed class InstanceDetailState {}

class InstanceDetailInitial extends InstanceDetailState {}

class InstanceDetailLoading extends InstanceDetailState {}

class InstanceDetailLoaded extends InstanceDetailState {
  final InstanceShow instance;

  InstanceDetailLoaded({required this.instance});
}

class InstanceDetailError extends InstanceDetailState {
  final String message;

  InstanceDetailError({required this.message});
}
