import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance/domain/usecase/edit_instance.dart';

import '../../../domain/entity/instance.dart';

part 'edit_instance_state.dart';

@injectable
class EditInstanceCubit extends Cubit<EditInstanceState> {
  final EditInstance _editInstance;

  EditInstanceCubit(this._editInstance) : super(EditInstanceInitial());

  Future<void> editInstance(EditInstanceParams params) async {
    emit(EditInstanceLoading());

    final instance = await _editInstance(params);

    instance.fold(
      (failure) => emit(EditInstanceError(failure.message)),
      (instance) => emit(EditInstanceSuccess(instance)),
    );
  }
}
