part of 'edit_instance_cubit.dart';

@immutable
sealed class EditInstanceState {}

final class EditInstanceInitial extends EditInstanceState {}

final class EditInstanceLoading extends EditInstanceState {}

final class EditInstanceSuccess extends EditInstanceState {
  final InstanceShow instance;

  EditInstanceSuccess(this.instance);
}

final class EditInstanceError extends EditInstanceState {
  final String error;

  EditInstanceError(this.error);
}
