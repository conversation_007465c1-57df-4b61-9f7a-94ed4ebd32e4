import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance/domain/usecase/create_instance.dart';

import '../../../domain/entity/instance.dart';

part 'create_instance_state.dart';

@injectable
class CreateInstanceCubit extends Cubit<CreateInstanceState> {
  final CreateInstance _createInstance;

  CreateInstanceCubit(this._createInstance) : super(CreateInstanceInitial());

  Future<void> createInstance(CreateInstanceParams params) async {
    emit(CreateInstanceLoading());
    final instance = await _createInstance(params);

    instance.fold(
      (failure) => emit(CreateInstanceError(failure.message)),
      (instance) => emit(CreateInstanceSuccess(instance)),
    );
  }
}
