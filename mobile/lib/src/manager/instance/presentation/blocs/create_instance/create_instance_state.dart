part of 'create_instance_cubit.dart';

@immutable
sealed class CreateInstanceState {}

final class CreateInstanceInitial extends CreateInstanceState {}

final class CreateInstanceLoading extends CreateInstanceState {}

final class CreateInstanceSuccess extends CreateInstanceState {
  final InstanceShow instance;

  CreateInstanceSuccess(this.instance);
}

final class CreateInstanceError extends CreateInstanceState {
  final String error;

  CreateInstanceError(this.error);
}
