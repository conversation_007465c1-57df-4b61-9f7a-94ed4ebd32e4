part of 'delete_instance_cubit.dart';

@immutable
sealed class DeleteInstanceState {}

final class DeleteInstanceInitial extends DeleteInstanceState {}

final class DeleteInstanceLoading extends DeleteInstanceState {}

final class DeleteInstanceSuc<PERSON> extends DeleteInstanceState {
  final String message;

  DeleteInstanceSuccess(this.message);
}

final class DeleteInstanceError extends DeleteInstanceState {
  final String error;

  DeleteInstanceError(this.error);
}
