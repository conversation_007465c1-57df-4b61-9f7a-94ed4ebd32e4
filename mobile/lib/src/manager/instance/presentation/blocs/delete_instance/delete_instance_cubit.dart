import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';

import '../../../domain/usecase/delete_instance.dart';

part 'delete_instance_state.dart';

@injectable
class DeleteInstanceCubit extends Cubit<DeleteInstanceState> {
  final DeleteInstance _deleteInstance;

  DeleteInstanceCubit(this._deleteInstance) : super(DeleteInstanceInitial());

  Future<void> deleteInstance({
    required String instanceId,
    required String healthCenterId,
  }) async {
    emit(DeleteInstanceLoading());

    final response = await _deleteInstance(
      DeleteInstanceParams(
        instanceId: instanceId,
        healthCenterId: healthCenterId,
      ),
    );

    emit(
      response.fold(
        (failure) => DeleteInstanceError(failure.message),
        (success) => DeleteInstanceSuccess(success.message),
      ),
    );
  }
}
