import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/manager/instance/domain/usecase/get_instances_relapsed.dart';

class InstancesRelapsedBloc extends PaginatedItemBloc<Instance> {
  final GetInstancesRelapsed _getInstancesRelapsed;
  final String healthCenterId;

  InstancesRelapsedBloc(
    this._getInstancesRelapsed, {
    required this.healthCenterId,
  }) : super();

  @override
  callUseCase(PaginationParams params) {
    return _getInstancesRelapsed(GetInstancesRelapsedParams(
      healthCenterId: healthCenterId,
      page: params.page,
      search: params.search,
    ));
  }
}
