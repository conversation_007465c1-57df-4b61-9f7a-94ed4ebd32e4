import 'package:injectable/injectable.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/manager/instance/domain/usecase/get_all_instance.dart';

@injectable
class AllInstancesBloc extends PaginatedItemBloc<Instance> {
  final GetAllInstance _getAllInstance;

  AllInstancesBloc(this._getAllInstance) : super();

  @override
  callUseCase(PaginationParams params) => _getAllInstance(params);
}
