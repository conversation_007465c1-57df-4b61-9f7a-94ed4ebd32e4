import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/manager/instance/domain/usecase/get_instances.dart';

class InstancesBloc extends PaginatedItemBloc<Instance> {
  final GetInstances _getInstances;
  final String healthCenterId;

  InstancesBloc(this._getInstances, {required this.healthCenterId}) : super();

  @override
  callUseCase(PaginationParams params) {
    return _getInstances(GetInstancesParams(
      healthCenterId: healthCenterId,
      page: params.page,
      search: params.search,
    ));
  }
}
