import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/health_center/presentation/blocs/health_center/health_center_cubit.dart';

class HealthCenterDrawer extends StatelessWidget {
  const HealthCenterDrawer({super.key});
  @override
  Widget build(BuildContext context) {
    final user = context.read<AuthenticatedCubit>().state.user!;
    final healthCenters = context.read<HealthCenterCubit>().state.healthCenters;
    final selectedHealthCenter =
        context.read<HealthCenterCubit>().state.selectedHealthCenter;

    return GFDrawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: <Widget>[
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).appBarTheme.backgroundColor,
            ),
            child: <PERSON>umn(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const AppBarLeadingTitle(
                  centered: false,
                  color: Colors.white,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      user.phone,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          ListTile(
            title: Text(
              "Centres de santé",
              style: TextStyle(
                fontSize: 15,
                color: Colors.grey[800],
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // Display health centers
          for (final healthCenter in healthCenters)
            ListTile(
              title: Text(healthCenter.name),
              selected: selectedHealthCenter != null &&
                  selectedHealthCenter.id == healthCenter.id,
              onTap: () {
                context
                    .read<HealthCenterCubit>()
                    .selectHealthCenter(healthCenter);

                Scaffold.of(context).closeDrawer();
              },
            ),
        ],
      ),
    );
  }
}
