// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';

import 'package:s3g/common/common.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/diagnostic.dart';
import 'package:s3g/src/manager/instance_features/followup/followup.dart';
import 'package:s3g/src/manager/instance_features/treatment/treatment.dart';

class InstanceShowScaffoldWrapper extends StatelessWidget {
  final String instanceId;

  const InstanceShowScaffoldWrapper({
    Key? key,
    required this.instanceId,
  }) : super(key: key ?? const ValueKey<String>('InstanceShowScaffoldWrapper'));

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      key: ValueKey(instanceId),
      create: (_) =>
          InstanceDetailCubit(getIt(), instanceId: instanceId)..getInstance(),

      // Content
      child: const _InstanceShowScaffold(),
    );
  }
}

class _InstanceShowScaffold extends StatefulWidget {
  const _InstanceShowScaffold();

  @override
  State<_InstanceShowScaffold> createState() => _InstanceShowScaffoldState();
}

class _InstanceShowScaffoldState extends State<_InstanceShowScaffold> {
  int selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: BlocBuilder<InstanceDetailCubit, InstanceDetailState>(
          builder: (context, state) {
            switch (state) {
              case InstanceDetailLoaded(instance: InstanceShow injectable):
                return Text(injectable.code);
              default:
            }

            return const SizedBox.shrink();
          },
        ),
      ),
      body: BlocBuilder<InstanceDetailCubit, InstanceDetailState>(
        builder: (context, state) {
          switch (state) {
            case InstanceDetailInitial() || InstanceDetailLoading():
              return const Center(
                child: GFLoader(size: GFSize.LARGE),
              );

            case InstanceDetailLoaded():
              return SafeArea(
                child: _ShowScreenContent(
                  selectedIndex: selectedIndex,
                ),
              );

            case InstanceDetailError(message: String message):
              return RetryWidget(
                message: message,
                onPressed: () {
                  context.read<InstanceDetailCubit>().getInstance();
                },
              );
            // ignore: unreachable_switch_default
            default:
          }

          return const SizedBox.shrink();
        },
      ),
      bottomNavigationBar: _BottomNavigationBar(
        selectedIndex: selectedIndex,
        onDestinationSelected: (index) {
          setState(() {
            selectedIndex = index;
          });
        },
      ),
    );
  }
}

class _ShowScreenContent extends StatelessWidget {
  const _ShowScreenContent({required this.selectedIndex});

  final int selectedIndex;

  final screens = const [
    InstanceDetailPage(),
    DiagnosticListPage(),
    TreatmentListPage(),
    CompanionListPage(),
    FollowupListPage()
  ];

  @override
  Widget build(BuildContext context) {
    return LazyLoadIndexedStack(
      index: selectedIndex,
      children: screens,
    );
  }
}

class _BottomNavigationBar extends StatelessWidget {
  final void Function(int index)? onDestinationSelected;
  final int selectedIndex;

  const _BottomNavigationBar({
    required this.selectedIndex,
    required this.onDestinationSelected,
  });

  @override
  Widget build(BuildContext context) {
    return NavigationBar(
      labelBehavior: NavigationDestinationLabelBehavior.onlyShowSelected,
      selectedIndex: selectedIndex,
      indicatorColor: Theme.of(context).colorScheme.primary,
      onDestinationSelected: onDestinationSelected,
      destinations: const <Widget>[
        NavigationDestination(
          selectedIcon: Icon(Icons.info, color: Colors.white),
          icon: Icon(Icons.info_outline),
          label: 'Détail',
        ),
        NavigationDestination(
          selectedIcon: Icon(Icons.checklist_outlined, color: Colors.white),
          icon: Icon(Icons.checklist_outlined),
          label: 'Diagnost...',
        ),
        NavigationDestination(
          selectedIcon: Icon(Icons.handyman_sharp, color: Colors.white),
          icon: Icon(Icons.handyman_sharp),
          label: 'Prises e...',
        ),
        NavigationDestination(
          selectedIcon: Icon(Icons.people_alt_outlined, color: Colors.white),
          icon: Icon(Icons.people_alt_outlined),
          label: 'Accomp...',
        ),
        NavigationDestination(
          selectedIcon: Icon(Icons.trending_down_outlined, color: Colors.white),
          icon: Icon(Icons.trending_down_outlined),
          label: 'Suivis',
        )
      ],
    );
  }
}
