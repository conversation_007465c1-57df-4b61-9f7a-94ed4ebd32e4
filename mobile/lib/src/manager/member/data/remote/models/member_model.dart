// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/member/domain/entity/member.dart';

part 'member_model.g.dart';

@JsonSerializable()
class MemberModel extends Member {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  @JsonKey(name: 'companion_role')
  final MemberCompanionRole? companionRole;

  const MemberModel({
    required super.id,
    required super.name,
    required super.email,
    required super.role,
    required super.responsibility,
    required super.phone,
    required super.description,
    required this.createdAt,
    required this.updatedAt,
    required this.companionRole,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
          companionRole: companionRole,
        );

  factory MemberModel.fromJson(Map<String, dynamic> json) =>
      _$MemberModelFromJson(json);

  Map<String, dynamic> toJson() => _$MemberModelToJson(this);
}
