// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'member_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MemberModel _$MemberModelFromJson(Map<String, dynamic> json) => MemberModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String?,
      role: $enumDecode(_$UserRoleEnumMap, json['role']),
      responsibility:
          $enumDecode(_$MemberResponsibilityEnumMap, json['responsibility']),
      phone: json['phone'] as String,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      companionRole: $enumDecodeNullable(
          _$MemberCompanionRoleEnumMap, json['companion_role']),
    );

Map<String, dynamic> _$MemberModelToJson(MemberModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'role': _$UserRoleEnumMap[instance.role]!,
      'phone': instance.phone,
      'description': instance.description,
      'responsibility': _$MemberResponsibilityEnumMap[instance.responsibility]!,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'companion_role': _$MemberCompanionRoleEnumMap[instance.companionRole],
    };

const _$UserRoleEnumMap = {
  UserRole.ROOT: 'ROOT',
  UserRole.APS: 'APS',
  UserRole.COMPANION: 'COMPANION',
};

const _$MemberResponsibilityEnumMap = {
  MemberResponsibility.ADMINISTRATOR: 'ADMINISTRATOR',
  MemberResponsibility.OPERATOR: 'OPERATOR',
  MemberResponsibility.COMPANION: 'COMPANION',
};

const _$MemberCompanionRoleEnumMap = {
  MemberCompanionRole.CODESA: 'CODESA',
  MemberCompanionRole.AVEC: 'AVEC',
  MemberCompanionRole.RELIGIOUS_LEADER: 'RELIGIOUS_LEADER',
};
