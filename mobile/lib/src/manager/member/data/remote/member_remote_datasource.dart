import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/member/data/remote/models/member_model.dart';

import '../../domain/entity/member.dart';

abstract class MemberRemoteDataSource {
  Future<MemberModel> createMember(
    String healthCenterId, {
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required MemberResponsibility responsibility,
    required String? description,
    required MemberCompanionRole? companionRole,
  });

  Future<List<MemberModel>> getMembers({
    required String healthCenterId,
    required MemberType type,
  });

  Future<MessageResponse> deleteMember({
    required String healthCenterId,
    required String memberId,
    required MemberType type,
  });

  Future<MemberModel> updateMember(
    String healthCenterId, {
    required String memberId,
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required MemberResponsibility responsibility,
    required String? description,
    required MemberCompanionRole? companionRole,
  });

  Future<List<UserModel>> searchAttachMember({
    required String healthCenterId,
    required MemberType type,
    required String search,
  });

  Future<MessageResponse> attachMember({
    required String healthCenterId,
    required String userId,
    required MemberType type,
    required MemberResponsibility responsibility,
  });
}

@Injectable(as: MemberRemoteDataSource)
class MemberRemoteDataSourceImpl implements MemberRemoteDataSource {
  final Dio _httpClient;

  MemberRemoteDataSourceImpl({required Dio httpClient})
      : _httpClient = httpClient;

  @override
  Future<MemberModel> createMember(
    String healthCenterId, {
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required MemberResponsibility responsibility,
    required String? description,
    required MemberCompanionRole? companionRole,
  }) async {
    final data = {
      'name': name,
      'phone': phone,
      'email': email,
      'role': role.name,
      'responsibility': responsibility.name,
      'description': description,
      'companion_role': companionRole?.name,
    };

    var endpoint = '';

    switch (role) {
      case UserRole.APS:
        endpoint = "/health-centers/$healthCenterId/aps";
        break;
      case UserRole.COMPANION:
        endpoint = "/health-centers/$healthCenterId/companions";
        break;
      default:
        throw UnimplementedError("User role cannot be $role");
    }

    final response = await _httpClient.post(endpoint, data: data);

    return MemberModel.fromJson(response.data['data']);
  }

  @override
  Future<List<MemberModel>> getMembers({
    required String healthCenterId,
    required MemberType type,
  }) async {
    var endpoint = '';

    switch (type) {
      case MemberType.APS:
        endpoint = "/health-centers/$healthCenterId/aps";
        break;
      case MemberType.COMPANION:
        endpoint = "/health-centers/$healthCenterId/companions";
        break;
    }

    final response = await _httpClient.get(endpoint);

    return (response.data['data'] as List)
        .map<MemberModel>((e) => MemberModel.fromJson(e))
        .toList();
  }

  @override
  Future<MessageResponse> deleteMember({
    required String healthCenterId,
    required String memberId,
    required MemberType type,
  }) async {
    var endpoint = '';

    switch (type) {
      case MemberType.APS:
        endpoint = "/health-centers/$healthCenterId/aps/$memberId";
        break;
      case MemberType.COMPANION:
        endpoint = "/health-centers/$healthCenterId/companions/$memberId";
        break;
    }

    final response = await _httpClient.delete(endpoint);

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<MemberModel> updateMember(
    String healthCenterId, {
    required String memberId,
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required MemberResponsibility responsibility,
    required String? description,
    required MemberCompanionRole? companionRole,
  }) async {
    var endpoint = '';

    switch (role) {
      case UserRole.APS:
        endpoint = "/health-centers/$healthCenterId/aps/$memberId";
        break;
      case UserRole.COMPANION:
        endpoint = "/health-centers/$healthCenterId/companions/$memberId";
        break;
      default:
        throw UnimplementedError("User role cannot be $role");
    }

    final data = {
      'name': name,
      'phone': phone,
      'email': email,
      'role': role.name,
      'responsibility': responsibility.name,
      'description': description,
      'companion_role': companionRole?.name,
    };

    final response = await _httpClient.put(endpoint, data: data);

    return MemberModel.fromJson(response.data['data']);
  }

  @override
  Future<MessageResponse> attachMember({
    required String healthCenterId,
    required String userId,
    required MemberType type,
    required MemberResponsibility responsibility,
  }) async {
    var endpoint = '';

    switch (type) {
      case MemberType.APS:
        endpoint = "/health-centers/$healthCenterId/aps/attach";
        break;
      case MemberType.COMPANION:
        endpoint = "/health-centers/$healthCenterId/companions/attach";
        break;
    }

    final data = {
      'user_id': userId,
      'responsibility': responsibility.name,
    };

    final response = await _httpClient.post(endpoint, data: data);

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<List<UserModel>> searchAttachMember({
    required String healthCenterId,
    required MemberType type,
    required String search,
  }) async {
    var endpoint = '';

    switch (type) {
      case MemberType.APS:
        endpoint = "/health-centers/$healthCenterId/aps/attach?search=$search";
        break;
      case MemberType.COMPANION:
        endpoint =
            "/health-centers/$healthCenterId/companions/attach?search=$search";
        break;
    }

    final response = await _httpClient.get(endpoint);

    return (response.data['data'] as List)
        .map<UserModel>((e) => UserModel.fromJson(e))
        .toList();
  }
}
