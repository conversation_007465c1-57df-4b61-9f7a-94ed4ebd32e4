import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';

import 'package:s3g/core/repository/repository.dart';

import 'package:s3g/src/authentication/domain/entities/user.dart';
import 'package:s3g/src/manager/member/data/remote/member_remote_datasource.dart';

import 'package:s3g/src/manager/member/domain/entity/member.dart';

import '../../domain/repository/member_repository.dart';

@Injectable(as: MemberRepository)
class MemberRepositoryImpl extends MemberRepository {
  final MemberRemoteDataSource _remoteDataSource;

  MemberRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<List<Member>> getMembers({
    required String healthCenterId,
    required MemberType type,
  }) {
    return requestHelper(() => _remoteDataSource.getMembers(
          healthCenterId: healthCenterId,
          type: type,
        ));
  }

  @override
  RepositoryResponse<Member> createMember(
    String healthCenterId, {
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required String? description,
    required MemberResponsibility responsibility,
    required MemberCompanionRole? companionRole,
  }) {
    return requestHelper(() {
      return _remoteDataSource.createMember(
        healthCenterId,
        name: name,
        phone: phone,
        email: email,
        role: role,
        responsibility: responsibility,
        description: description,
        companionRole: companionRole,
      );
    });
  }

  @override
  RepositoryResponse<MessageResponse> deleteMember({
    required String healthCenterId,
    required String memberId,
    required MemberType type,
  }) {
    return requestHelper(() => _remoteDataSource.deleteMember(
          healthCenterId: healthCenterId,
          memberId: memberId,
          type: type,
        ));
  }

  @override
  RepositoryResponse<Member> updateMember(
    String healthCenterId, {
    required String memberId,
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required String? description,
    required MemberResponsibility responsibility,
    required MemberCompanionRole? companionRole,
  }) {
    return requestHelper(() {
      return _remoteDataSource.updateMember(
        healthCenterId,
        memberId: memberId,
        name: name,
        phone: phone,
        email: email,
        role: role,
        responsibility: responsibility,
        description: description,
        companionRole: companionRole,
      );
    });
  }

  @override
  RepositoryResponse<MessageResponse> attachMember({
    required String healthCenterId,
    required String userId,
    required MemberType type,
    required MemberResponsibility responsibility,
  }) {
    return requestHelper(() => _remoteDataSource.attachMember(
          healthCenterId: healthCenterId,
          userId: userId,
          type: type,
          responsibility: responsibility,
        ));
  }

  @override
  RepositoryResponse<List<User>> searchAttachMember({
    required String healthCenterId,
    required MemberType type,
    required String search,
  }) {
    return requestHelper(() => _remoteDataSource.searchAttachMember(
          healthCenterId: healthCenterId,
          type: type,
          search: search,
        ));
  }
}
