// ignore_for_file: constant_identifier_names

import 'package:s3g/src/authentication/authentication.dart';

class Member extends User {
  final MemberResponsibility responsibility;
  final MemberCompanionRole? companionRole;

  const Member({
    required super.id,
    required super.name,
    required super.email,
    required super.role,
    required this.responsibility,
    required super.phone,
    required super.description,
    required super.createdAt,
    required super.updatedAt,
    required this.companionRole,
  });
}

enum MemberType {
  APS,
  COMPANION;

  String getLabel() {
    switch (this) {
      case APS:
        return 'APS';
      case COMPANION:
        return 'Accompagnant';
    }
  }
}

enum MemberCompanionRole {
  CODESA,
  AVEC,
  RELIGIOUS_LEADER;

  getLabel() {
    return switch (this) {
      CODESA => "Codesa",
      AVEC => "AVEC",
      RELIGIOUS_LEADER => "Leader religieux"
    };
  }
}

enum MemberResponsibility {
  ADMINISTRATOR,
  OPERATOR,
  COMPANION;

  String getLabel() {
    switch (this) {
      case OPERATOR:
        return 'Operateur';
      case ADMINISTRATOR:
        return 'Administateur';
      case COMPANION:
        return 'Accompagnant';
    }
  }

  static fromString(String role) {
    switch (role) {
      case 'ADMINISTRATOR':
        return ADMINISTRATOR;
      case 'OPERATOR':
        return OPERATOR;
      case 'COMPANION':
        return COMPANION;
    }
  }
}
