import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/authentication/authentication.dart';

import '../../member.dart';

abstract class MemberRepository {
  RepositoryResponse<List<Member>> getMembers({
    required String healthCenterId,
    required MemberType type,
  });

  RepositoryResponse<Member> createMember(
    String healthCenterId, {
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required String? description,
    required MemberResponsibility responsibility,
    required MemberCompanionRole? companionRole,
  });

  RepositoryResponse<Member> updateMember(
    String healthCenterId, {
    required String memberId,
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required String? description,
    required MemberResponsibility responsibility,
    required MemberCompanionRole? companionRole,
  });

  RepositoryResponse<List<User>> searchAttachMember({
    required String healthCenterId,
    required MemberType type,
    required String search,
  });

  RepositoryResponse<MessageResponse> attachMember({
    required String healthCenterId,
    required String userId,
    required MemberType type,
    required MemberResponsibility responsibility,
  });

  RepositoryResponse<MessageResponse> deleteMember({
    required String healthCenterId,
    required String memberId,
    required MemberType type,
  });
}
