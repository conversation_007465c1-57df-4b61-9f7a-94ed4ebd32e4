import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/member/domain/entity/member.dart';

import '../repository/member_repository.dart';

@injectable
class SearchAttachMember extends UseCase<List<User>, SearchAttachMemberParams> {
  final MemberRepository _repository;

  SearchAttachMember(this._repository);

  @override
  call(SearchAttachMemberParams params) async {
    return _repository.searchAttachMember(
      healthCenterId: params.healthCenterId,
      type: params.type,
      search: params.search,
    );
  }
}

class SearchAttachMemberParams {
  final String healthCenterId;
  final MemberType type;
  final String search;

  SearchAttachMemberParams({
    required this.healthCenterId,
    required this.type,
    required this.search,
  });
}
