import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../../member.dart';
import '../repository/member_repository.dart';

@injectable
class DeleteMember extends UseCase<MessageResponse, DeleteMemberParams> {
  final MemberRepository memberRepository;

  DeleteMember({required this.memberRepository});

  @override
  call(DeleteMemberParams params) {
    return memberRepository.deleteMember(
      healthCenterId: params.healthCenterId,
      memberId: params.memberId,
      type: params.type,
    );
  }
}

class DeleteMemberParams {
  final String memberId;
  final String healthCenterId;
  final MemberType type;

  DeleteMemberParams({
    required this.memberId,
    required this.healthCenterId,
    required this.type,
  });
}
