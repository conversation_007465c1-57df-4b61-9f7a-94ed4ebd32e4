import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../../member.dart';
import '../repository/member_repository.dart';

@injectable
class GetMembers extends UseCase<List<Member>, GetMembersParams> {
  final MemberRepository repository;

  GetMembers(this.repository);

  @override
  call(GetMembersParams params) {
    return repository.getMembers(
      healthCenterId: params.healthCenterId,
      type: params.type,
    );
  }
}

class GetMembersParams {
  final String healthCenterId;
  final MemberType type;

  GetMembersParams({required this.healthCenterId, required this.type});
}
