import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../repository/member_repository.dart';

@injectable
class CreateMember extends UseCase<Member, CreateMemberParams> {
  final MemberRepository repository;

  CreateMember(this.repository);

  @override
  call(CreateMemberParams params) {
    return repository.createMember(
      params.healthCenterId,
      name: params.name,
      phone: params.phone,
      email: params.email,
      role: params.role,
      responsibility: params.responsibility,
      description: params.description,
      companionRole: params.companionRole,
    );
  }
}

class CreateMemberParams {
  final String healthCenterId;
  final String name;
  final String phone;
  final String? email;
  final UserRole role;
  final String? description;
  final MemberResponsibility responsibility;
  final MemberCompanionRole? companionRole;

  CreateMemberParams({
    required this.healthCenterId,
    required this.name,
    required this.phone,
    required this.email,
    required this.role,
    required this.responsibility,
    required this.description,
    required this.companionRole,
  });
}
