import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/member/domain/entity/member.dart';

import '../repository/member_repository.dart';

@injectable
class AttachMember extends UseCase<MessageResponse, AttachMemberParams> {
  final MemberRepository _repository;

  AttachMember(this._repository);

  @override
  call(AttachMemberParams params) async {
    return _repository.attachMember(
      healthCenterId: params.healthCenterId,
      userId: params.userId,
      type: params.type,
      responsibility: params.responsibility,
    );
  }
}

class AttachMemberParams {
  final String healthCenterId;
  final String userId;
  final MemberType type;
  final MemberResponsibility responsibility;

  AttachMemberParams({
    required this.healthCenterId,
    required this.userId,
    required this.type,
    required this.responsibility,
  });
}
