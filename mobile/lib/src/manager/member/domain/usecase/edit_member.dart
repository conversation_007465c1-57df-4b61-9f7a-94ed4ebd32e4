import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/member/domain/entity/member.dart';
import 'package:s3g/src/manager/member/domain/repository/member_repository.dart';

@injectable
class EditMember extends UseCase<Member, EditMemberParams> {
  final MemberRepository _repository;

  EditMember(this._repository);

  @override
  call(EditMemberParams params) {
    return _repository.updateMember(
      params.healthCenterId,
      name: params.name,
      phone: params.phone,
      memberId: params.memberId,
      email: params.email,
      role: params.role,
      responsibility: params.responsibility,
      description: params.description,
      companionRole: params.companionRole,
    );
  }
}

class EditMemberParams {
  final String healthCenterId;
  final String name;
  final String memberId;
  final String phone;
  final String? email;
  final String? description;
  final UserRole role;
  final MemberResponsibility responsibility;
  final MemberCompanionRole? companionRole;

  EditMemberParams({
    required this.healthCenterId,
    required this.name,
    required this.memberId,
    required this.phone,
    required this.email,
    required this.role,
    required this.responsibility,
    required this.description,
    required this.companionRole,
  });
}
