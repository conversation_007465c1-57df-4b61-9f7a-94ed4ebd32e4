import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/member/domain/usecase/create_member.dart';

import '../../../member.dart';

part 'create_member_state.dart';

@injectable
class CreateMemberCubit extends Cubit<CreateMemberState> {
  final CreateMember _createMember;

  CreateMemberCubit(this._createMember) : super(CreateMemberInitial());

  void createMember(CreateMemberParams params) async {
    emit(CreateMemberLoading());

    final result = await _createMember(params);

    result.fold(
      (l) => emit(CreateMemberFailure(l.message)),
      (r) => emit(CreateMemberSuccess(r)),
    );
  }
}
