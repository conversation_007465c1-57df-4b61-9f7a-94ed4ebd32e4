part of 'create_member_cubit.dart';

@immutable
sealed class CreateMemberState {}

final class CreateMemberInitial extends CreateMemberState {}

final class CreateMemberLoading extends CreateMemberState {}

final class CreateMemberSuccess extends CreateMemberState {
  final Member member;

  CreateMemberSuccess(this.member);
}

final class CreateMemberFailure extends CreateMemberState {
  final String message;

  CreateMemberFailure(this.message);
}
