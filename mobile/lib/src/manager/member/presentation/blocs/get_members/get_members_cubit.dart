import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/member/domain/entity/member.dart';
import 'package:s3g/src/manager/member/domain/usecase/get_members.dart';

part 'get_members_state.dart';

class GetMembersCubit extends Cubit<GetMembersState> {
  final GetMembers _getMembers;
  final String healthCenterId;
  final MemberType memberType;

  GetMembersCubit({
    required GetMembers getMembers,
    required this.healthCenterId,
    required this.memberType,
  })  : _getMembers = getMembers,
        super(GetMembersInitial());

  Future<void> getMembers() async {
    emit(GetMembersLoading());

    final response = await _getMembers(
      GetMembersParams(healthCenterId: healthCenterId, type: memberType),
    );

    response.fold(
      (failure) => emit(GetMembersError(message: failure.message)),
      (members) => emit(GetMembersLoaded(members: members)),
    );
  }
}
