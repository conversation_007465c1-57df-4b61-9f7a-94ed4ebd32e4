part of 'get_members_cubit.dart';

@immutable
sealed class GetMembersState {}

final class GetMembersInitial extends GetMembersState {}

final class GetMembersLoading extends GetMembersState {}

final class GetMembersLoaded extends GetMembersState {
  final List<Member> members;

  GetMembersLoaded({required this.members});
}

final class GetMembersError extends GetMembersState {
  final String message;

  GetMembersError({required this.message});
}
