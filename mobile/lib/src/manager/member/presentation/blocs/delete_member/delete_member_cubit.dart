import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/member/domain/usecase/delete_member.dart';
import 'package:s3g/src/manager/member/member.dart';
part 'delete_member_state.dart';

class DeleteMemberCubit extends Cubit<DeleteMemberState> {
  final DeleteMember _deleteMember;
  final String healthCenterId;
  final MemberType memberType;

  DeleteMemberCubit(
    this._deleteMember, {
    required this.healthCenterId,
    required this.memberType,
  }) : super(DeleteMemberInitial());

  void deleteMember({required String memberId}) async {
    emit(DeleteMemberLoading());

    final result = await _deleteMember(
      DeleteMemberParams(
        memberId: memberId,
        healthCenterId: healthCenterId,
        type: memberType,
      ),
    );

    result.fold(
      (l) => emit(DeleteMemberError(l.message)),
      (r) => emit(DeleteMemberSuccess(r.message)),
    );
  }
}
