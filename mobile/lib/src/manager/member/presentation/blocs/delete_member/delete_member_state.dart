part of 'delete_member_cubit.dart';

@immutable
sealed class DeleteMemberState {}

final class DeleteMemberInitial extends DeleteMemberState {}

final class DeleteMemberLoading extends DeleteMemberState {}

final class DeleteMemberSuccess extends DeleteMemberState {
  final String message;

  DeleteMemberSuccess(this.message);
}

final class DeleteMemberError extends DeleteMemberState {
  final String message;

  DeleteMemberError(this.message);
}
