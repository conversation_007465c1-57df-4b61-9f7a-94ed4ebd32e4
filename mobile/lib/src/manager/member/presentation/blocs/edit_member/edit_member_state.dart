part of 'edit_member_cubit.dart';

@immutable
sealed class EditMemberState {}

final class EditMemberInitial extends EditMemberState {}

final class EditMemberLoading extends EditMemberState {}

final class EditMemberSuccess extends EditMemberState {
  final Member member;

  EditMemberSuccess(this.member);
}

final class EditMemberError extends EditMemberState {
  final String message;

  EditMemberError(this.message);
}
