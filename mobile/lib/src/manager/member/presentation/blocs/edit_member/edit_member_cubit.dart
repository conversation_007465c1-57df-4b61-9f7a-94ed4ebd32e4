import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/member/domain/usecase/edit_member.dart';

import '../../../member.dart';

part 'edit_member_state.dart';

@injectable
class EditMemberCubit extends Cubit<EditMemberState> {
  final EditMember _editMember;

  EditMemberCubit(this._editMember) : super(EditMemberInitial());

  Future<void> editMember(EditMemberParams params) async {
    emit(EditMemberLoading());

    final result = await _editMember(params);

    result.fold(
      (l) => emit(EditMemberError(l.message)),
      (r) => emit(EditMemberSuccess(r)),
    );
  }
}
