part of 'search_attach_member_bloc.dart';

@immutable
sealed class SearchAttachMemberState {}

final class SearchAttachMemberInitial extends SearchAttachMemberState {}

final class SearchAttachMemberLoading extends SearchAttachMemberState {}

final class SearchAttachMemberLoaded extends SearchAttachMemberState {
  final List<User> users;

  SearchAttachMemberLoaded(this.users);
}

final class SearchAttachMemberError extends SearchAttachMemberState {
  final String message;

  SearchAttachMemberError(this.message);
}
