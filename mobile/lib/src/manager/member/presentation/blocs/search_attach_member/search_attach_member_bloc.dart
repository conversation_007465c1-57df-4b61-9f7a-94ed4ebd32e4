import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/member/domain/usecase/search_attach_member.dart';
import 'package:stream_transform/stream_transform.dart';

import '../../../member.dart';

part 'search_attach_member_event.dart';
part 'search_attach_member_state.dart';

const _duration = Duration(milliseconds: 300);

EventTransformer<Event> debounce<Event>(Duration duration) {
  return (events, mapper) => events.debounce(duration).switchMap(mapper);
}

class SearchAttachMemberBloc
    extends Bloc<SearchAttachMemberEvent, SearchAttachMemberState> {
  final SearchAttachMember _searchAttachMember;
  final String healthCenterId;
  final MemberType memberType;

  SearchAttachMemberBloc(
    this._searchAttachMember, {
    required this.healthCenterId,
    required this.memberType,
  }) : super(SearchAttachMemberInitial()) {
    on<SearchAttachMemberEvent>(
      _searchAttachMemberCb,
      transformer: debounce(_duration),
    );
  }

  void _searchAttachMemberCb(
    SearchAttachMemberEvent event,
    Emitter<SearchAttachMemberState> emit,
  ) async {
    emit(SearchAttachMemberLoading());

    final result = await _searchAttachMember(
      SearchAttachMemberParams(
        healthCenterId: healthCenterId,
        type: memberType,
        search: event.query,
      ),
    );

    result.fold(
      (l) => emit(SearchAttachMemberError(l.message)),
      (r) => emit(SearchAttachMemberLoaded(r)),
    );
  }
}
