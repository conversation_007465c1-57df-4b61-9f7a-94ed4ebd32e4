import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/member/domain/usecase/attach_member.dart';

part 'attach_member_state.dart';

@injectable
class AttachMemberCubit extends Cubit<AttachMemberState> {
  final AttachMember _attachMember;

  AttachMemberCubit(this._attachMember) : super(AttachMemberInitial());

  Future<void> attachMember(AttachMemberParams params) async {
    emit(AttachMemberLoading());

    final result = await _attachMember(params);

    result.fold(
      (l) => emit(AttachMemberError(message: l.message)),
      (r) => emit(AttachMemberSuccess(message: r.message)),
    );
  }
}
