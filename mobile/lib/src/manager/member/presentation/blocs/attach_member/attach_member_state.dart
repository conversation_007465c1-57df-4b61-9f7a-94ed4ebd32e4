part of 'attach_member_cubit.dart';

@immutable
sealed class AttachMemberState {}

final class AttachMemberInitial extends AttachMemberState {}

final class AttachMemberLoading extends AttachMemberState {}

final class AttachMemberError extends AttachMemberState {
  final String message;

  AttachMemberError({required this.message});
}

final class AttachMemberSuccess extends AttachMemberState {
  final String message;

  AttachMemberSuccess({required this.message});
}
