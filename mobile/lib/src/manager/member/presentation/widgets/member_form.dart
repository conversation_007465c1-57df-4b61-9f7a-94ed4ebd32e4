import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/member/member.dart';
import 'package:s3g/src/manager/member/presentation/blocs/attach_member/attach_member_cubit.dart';
import 'package:s3g/src/manager/member/presentation/blocs/create_member/create_member_cubit.dart';
import 'package:s3g/src/manager/member/presentation/blocs/edit_member/edit_member_cubit.dart';
import 'package:s3g/src/manager/member/presentation/blocs/search_attach_member/search_attach_member_bloc.dart';

import 'member_attache_form.dart';
import 'member_editable_form.dart';

class MemberForm extends StatelessWidget {
  final MemberType memberType;
  final VoidCallback closeEditMode;
  final HealthCenter healthCenter;
  final Member? editable;

  const MemberForm({
    super.key,
    required this.closeEditMode,
    required this.healthCenter,
    required this.editable,
    required this.memberType,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => getIt<CreateMemberCubit>(),
        ),
        BlocProvider(
          create: (_) => getIt<EditMemberCubit>(),
        ),
        BlocProvider(
          create: (_) => getIt<AttachMemberCubit>(),
        ),
        BlocProvider(
          create: (_) => SearchAttachMemberBloc(
            getIt(),
            healthCenterId: healthCenter.id,
            memberType: memberType,
          ),
        ),
      ],
      child: FormContent(
        memberType: memberType,
        closeEditMode: closeEditMode,
        healthCenter: healthCenter,
        editable: editable,
      ),
    );
  }
}

class FormContent extends StatefulWidget {
  final MemberType memberType;
  final VoidCallback closeEditMode;
  final HealthCenter healthCenter;
  final Member? editable;

  const FormContent({
    super.key,
    required this.memberType,
    required this.closeEditMode,
    required this.healthCenter,
    this.editable,
  });

  @override
  State<FormContent> createState() => _FormContentState();
}

class _FormContentState extends State<FormContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    _tabController = TabController(vsync: this, length: 2);
    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.editable != null) {
      return MemberEditableForm(
        memberType: widget.memberType,
        closeEditMode: widget.closeEditMode,
        healthCenter: widget.healthCenter,
        editable: widget.editable,
      );
    }

    return GFTabs(
      isScrollable: false,
      length: _tabController.length,
      controller: _tabController,
      tabBarColor: Colors.white,
      tabBarHeight: 50.0,
      indicatorSize: TabBarIndicatorSize.tab,
      shape: InputBorder.none,
      height: MediaQuery.of(context).size.height,
      tabs: const <Widget>[
        Tab(child: Text("Créer")),
        Tab(child: Text("Attacher")),
      ],
      tabBarView: GFTabBarView(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(),
        children: <Widget>[
          MemberEditableForm(
            memberType: widget.memberType,
            closeEditMode: widget.closeEditMode,
            healthCenter: widget.healthCenter,
            editable: widget.editable,
          ),
          MemberAttacheForm(
            memberType: widget.memberType,
            closeEditMode: widget.closeEditMode,
            healthCenter: widget.healthCenter,
          )
        ],
      ),
    );
  }
}
