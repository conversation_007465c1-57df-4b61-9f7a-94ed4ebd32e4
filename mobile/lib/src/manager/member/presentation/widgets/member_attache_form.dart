import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/member/domain/usecase/attach_member.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../blocs/attach_member/attach_member_cubit.dart';
import '../blocs/search_attach_member/search_attach_member_bloc.dart';

class MemberAttacheForm extends StatefulWidget {
  final MemberType memberType;
  final VoidCallback closeEditMode;
  final HealthCenter healthCenter;

  const MemberAttacheForm({
    super.key,
    required this.memberType,
    required this.closeEditMode,
    required this.healthCenter,
  });

  @override
  State<MemberAttacheForm> createState() => _MemberAttacheFormState();
}

class _MemberAttacheFormState extends State<MemberAttacheForm> {
  final SearchController searchController = SearchController();

  var _users = <User>[];

  bool skipSearch = false;

  User? selectedUser;
  MemberResponsibility? selectedResponsibility;

  @override
  void initState() {
    searchController.addListener(_onTextInputEdit);

    super.initState();
  }

  @override
  void dispose() {
    searchController.removeListener(_onTextInputEdit);
    searchController.dispose();

    super.dispose();
  }

  void _onTextInputEdit() {
    if (!skipSearch) {
      context
          .read<SearchAttachMemberBloc>()
          .add(SearchAttachMemberEvent(searchController.text));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        BlocBuilder<SearchAttachMemberBloc, SearchAttachMemberState>(
          builder: (_, state) {
            if (state is SearchAttachMemberLoaded) {
              _users = state.users;

              Timer? timer;

              timer = Timer(const Duration(milliseconds: 50), () {
                if (timer != null) {
                  timer.cancel();
                }

                skipSearch = true;

                final oldValue = searchController.value.text.trim();
                searchController.value = const TextEditingValue();
                searchController.value = TextEditingValue(text: oldValue);

                skipSearch = false;
              });
            }

            return SearchAnchor(
              searchController: searchController,
              isFullScreen: true,
              builder: (_, SearchController controller) {
                // if actor view is opened the disable the drawer
                context
                    .read<HealthCenterDrawerCubit>()
                    .setDrawerState(!controller.isOpen);

                return SearchBar(
                  controller: controller,
                  padding: const WidgetStatePropertyAll<EdgeInsets>(
                    EdgeInsets.symmetric(horizontal: 16.0),
                  ),
                  enabled: true,
                  onTap: () {
                    controller.openView();
                  },
                  onChanged: (_) {
                    controller.openView();
                  },
                  leading: const Icon(Icons.search),
                );
              },
              suggestionsBuilder: (
                BuildContext context,
                SearchController controller,
              ) {
                return _users.map((User user) {
                  return ListTile(
                    title: Text(user.name),
                    subtitle: Text(user.phone,
                        style: const TextStyle(
                          color: Colors.grey,
                        )),
                    onTap: () {
                      setState(() {
                        selectedUser = user;
                        controller.closeView(user.name);
                      });
                    },
                  );
                });
              },
            );
          },
        ),

        // Member Responsibility
        if (widget.memberType == MemberType.APS) ...[
          const SizedBox(height: 40),
          FormBuilderChoiceChips<MemberResponsibility>(
            name: "responsibility",
            spacing: 15,
            selectedColor: Theme.of(context).colorScheme.primary.withOpacity(
                  0.8,
                ),
            onChanged: (value) {
              setState(() {
                selectedResponsibility = value;
              });
            },
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Responsabilité',
            ),
            options: MemberResponsibility.values
                .where((v) => v != MemberResponsibility.COMPANION)
                .map<FormBuilderChipOption<MemberResponsibility>>(
              (MemberResponsibility v) {
                return FormBuilderChipOption(
                  value: v,
                  child: Text(v.getLabel()),
                );
              },
            ).toList(),
          ),
        ],

        const SizedBox(height: 30),

        BlocBuilder<AttachMemberCubit, AttachMemberState>(
          builder: (_, state) {
            return FormButtons(
              loading: state is AttachMemberLoading,
              onSubmit: _save,
              onCancel: widget.closeEditMode,
            );
          },
        ),

        // Show error message
        BlocListener<AttachMemberCubit, AttachMemberState>(
          listener: (_, state) {
            switch (state) {
              case AttachMemberSuccess():
                widget.closeEditMode();
                break;

              case AttachMemberError():
                showToast(
                  context,
                  message: state.message,
                  type: ToastType.error,
                );
                break;

              default:
            }
          },
          child: const SizedBox.shrink(),
        )
        // Name
      ],
    );
  }

  void _save() {
    if (selectedUser == null || selectedUser?.name != searchController.text) {
      return;
    }

    if (selectedResponsibility == null && widget.memberType == MemberType.APS) {
      return;
    }

    final responsibility = widget.memberType == MemberType.COMPANION
        ? MemberResponsibility.COMPANION
        : selectedResponsibility;

    context.read<AttachMemberCubit>().attachMember(
          AttachMemberParams(
            healthCenterId: widget.healthCenter.id,
            userId: selectedUser!.id,
            type: widget.memberType,
            responsibility: responsibility!,
          ),
        );
  }
}
