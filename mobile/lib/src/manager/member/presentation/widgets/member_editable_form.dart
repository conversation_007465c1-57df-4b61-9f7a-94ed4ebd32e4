import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/member/domain/usecase/create_member.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../../domain/usecase/edit_member.dart';
import '../blocs/create_member/create_member_cubit.dart';
import '../blocs/edit_member/edit_member_cubit.dart';

class MemberEditableForm extends StatefulWidget {
  final MemberType memberType;
  final VoidCallback closeEditMode;
  final HealthCenter healthCenter;
  final Member? editable;

  const MemberEditableForm({
    super.key,
    required this.memberType,
    required this.closeEditMode,
    required this.healthCenter,
    this.editable,
  });

  @override
  State<MemberEditableForm> createState() => _MemberEditableFormState();
}

class _MemberEditableFormState extends State<MemberEditableForm> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10.0),
            child: Text(
              widget.editable != null
                  ? "Modifier ${widget.memberType.getLabel()} ${widget.editable!.name}:"
                  : "Créer ${widget.memberType.getLabel()}:",
              style: const TextStyle(fontSize: 18),
            ),
          ),

          // Some space
          columnSizedBox,

          // name field
          FormBuilderTextField(
            name: 'name',
            keyboardType: TextInputType.name,
            initialValue: widget.editable?.name,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
              FormBuilderValidators.min(2),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Nom',
            ),
          ),

          // Some space
          columnSizedBox,

          // Phone field
          FormBuilderTextField(
            name: 'phone',
            keyboardType: TextInputType.phone,
            initialValue: widget.editable?.phone,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
              FormBuilderValidators.min(2),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Numéro de téléphone',
            ),
          ),

          // Some space
          columnSizedBox,

          // Email field
          FormBuilderTextField(
            name: 'email',
            keyboardType: TextInputType.emailAddress,
            initialValue: widget.editable?.email,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.email(),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Adresse Email (facultative)',
            ),
          ),

          // Type
          if (widget.memberType == MemberType.APS) ...[
            columnSizedBox,
            FormBuilderChoiceChips<MemberResponsibility>(
              name: "responsibility",
              spacing: 15,
              selectedColor: Theme.of(context).colorScheme.primary.withOpacity(
                    0.8,
                  ),
              initialValue: widget.editable?.responsibility,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: 'Responsabilité',
              ),
              options: MemberResponsibility.values
                  .where((v) => v != MemberResponsibility.COMPANION)
                  .map<FormBuilderChipOption<MemberResponsibility>>(
                (MemberResponsibility v) {
                  return FormBuilderChipOption(
                    value: v,
                    child: Text(v.getLabel()),
                  );
                },
              ).toList(),
            ),
          ],

          if (widget.memberType == MemberType.COMPANION) ...[
            columnSizedBox,
            FormBuilderChoiceChips<MemberCompanionRole>(
              name: "companion_role",
              spacing: 15,
              initialValue: widget.editable?.companionRole,
              selectedColor:
                  Theme.of(context).colorScheme.primary.withOpacity(0.8),
              validator: FormBuilderValidators.required(),
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: 'Type Accompagnant',
              ),
              options: MemberCompanionRole.values.map((type) {
                return FormBuilderChipOption<MemberCompanionRole>(
                  value: type,
                  child: Text(
                    type.getLabel(),
                    style: const TextStyle(fontSize: 12),
                  ),
                );
              }).toList(),
            ),
          ],

          columnSizedBox,

          // Description
          FormBuilderTextField(
            name: 'description',
            minLines: 3,
            maxLines: 10,
            initialValue: widget.editable?.description,
            keyboardType: TextInputType.multiline,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: "Plus d'information (facultative)",
            ),
          ),

          columnSizedBox,

          // Create or update buttons
          if (widget.editable != null)
            BlocBuilder<EditMemberCubit, EditMemberState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is EditMemberLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          if (widget.editable == null)
            BlocBuilder<CreateMemberCubit, CreateMemberState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is CreateMemberLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          // Show error message on create
          BlocListener<CreateMemberCubit, CreateMemberState>(
            listener: (_, state) {
              switch (state) {
                case CreateMemberSuccess():
                  widget.closeEditMode();
                  _formKey.currentState?.reset();

                  break;

                case CreateMemberFailure():
                  showToast(
                    context,
                    message: state.message,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          ),

          // Show error message on edit
          BlocListener<EditMemberCubit, EditMemberState>(
            listener: (_, state) {
              switch (state) {
                case EditMemberSuccess():
                  widget.closeEditMode();
                  _formKey.currentState?.reset();
                  break;

                case EditMemberError():
                  showToast(
                    context,
                    message: state.message,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          )
        ],
      ),
    );
  }

  void _save() {
    final isValid = _formKey.currentState?.saveAndValidate();
    final value = _formKey.currentState?.value;

    if (isValid != null && isValid && value != null) {
      final name = value['name'] as String;
      final phone = value['phone'] as String;
      final email = value['email'] as String?;
      final companionRole = value['companion_role'] as MemberCompanionRole?;

      final role = widget.memberType == MemberType.COMPANION
          ? UserRole.COMPANION
          : UserRole.APS;

      final responsibility = widget.memberType == MemberType.COMPANION
          ? MemberResponsibility.COMPANION
          : value['responsibility'] as MemberResponsibility;

      final description = value['description'] as String?;

      if (widget.editable != null) {
        // EditMemberCubit, EditMemberState
        context.read<EditMemberCubit>().editMember(EditMemberParams(
              healthCenterId: widget.healthCenter.id,
              memberId: widget.editable!.id,
              name: name,
              phone: phone,
              email: email,
              role: role,
              responsibility: responsibility,
              description: description,
              companionRole: companionRole,
            ));
      } else {
        context.read<CreateMemberCubit>().createMember(CreateMemberParams(
              healthCenterId: widget.healthCenter.id,
              name: name,
              phone: phone,
              email: email,
              role: role,
              responsibility: responsibility,
              description: description,
              companionRole: companionRole,
            ));
      }
    }
  }
}
