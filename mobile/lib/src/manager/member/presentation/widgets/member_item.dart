import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../blocs/delete_member/delete_member_cubit.dart';

class MemberItem extends StatelessWidget {
  final bool canEdit;
  final Member member;

  const MemberItem({
    super.key,
    required this.member,
    required this.canEdit,
  });

  @override
  Widget build(BuildContext context) {
    final user = context.read<AuthenticatedCubit>().state.user!;

    return Column(
      children: [
        const SizedBox(height: 10),
        Slidable(
          enabled: user.id == member.id ? false : canEdit,
          endActionPane: ActionPane(
            motion: const ScrollMotion(),
            children: [
              SlidableAction(
                onPressed: (_) {
                  context
                      .read<InlineFormCubit<Member>>()
                      .enableForm(editable: member);
                },
                backgroundColor: GFColors.INFO,
                foregroundColor: Colors.white,
                icon: Icons.edit,
                label: 'Modifier',
              ),
              SlidableAction(
                onPressed: (_) => _onDelete(context, member),
                backgroundColor: GFColors.DANGER,
                foregroundColor: Colors.white,
                icon: Icons.delete,
                label: 'Supprimer',
              ),
            ],
          ),
          child: DetailTitle(
            leading: InitialIcon(text: member.name),
            titleText: member.name,
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Member phone number
                Text(
                  member.phone,
                  style: const TextStyle(
                    fontSize: 14.0,
                    color: Colors.grey,
                  ),
                ),

                // Member email
                if (member.email != null)
                  Text(
                    member.email!,
                    style: const TextStyle(
                      fontSize: 14.0,
                      color: Colors.grey,
                    ),
                  ),

                const SizedBox(height: 5),

                Text(
                  "Role: ${member.role.getLabel()}",
                  style: const TextStyle(
                    fontSize: 14.0,
                    color: Colors.grey,
                  ),
                ),

                // Member responsibility
                Text(
                  "Responsabilité: ${member.responsibility.getLabel()}",
                  style: const TextStyle(
                    fontSize: 14.0,
                    color: Colors.grey,
                  ),
                ),

                // Member
              ],
            ),
          ),
        ),
        const SizedBox(height: 10),
        const GreyDivider()
      ],
    );
  }

  void _onDelete(BuildContext context, Member member) async {
    final confirmed = await pressConfirm(context);

    if (confirmed) {
      // ignore: use_build_context_synchronously
      context.read<DeleteMemberCubit>().deleteMember(memberId: member.id);
    }
  }
}
