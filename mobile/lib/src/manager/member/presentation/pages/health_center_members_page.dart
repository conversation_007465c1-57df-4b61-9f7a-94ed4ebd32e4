import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/manager.dart';
import 'package:s3g/src/manager/member/domain/entity/member.dart';
import 'package:s3g/src/manager/member/presentation/widgets/member_form.dart';

import '../blocs/delete_member/delete_member_cubit.dart';
import '../blocs/get_members/get_members_cubit.dart';
import '../widgets/member_item.dart';

class HealthCenterMembersPage extends StatelessWidget {
  final MemberType memberType;

  const HealthCenterMembersPage({
    super.key,
    required this.memberType,
  });

  @override
  Widget build(BuildContext context) {
    final healthCenter =
        context.watch<HealthCenterCubit>().state.selectedHealthCenter!;

    return MultiBlocProvider(
      key: ValueKey(healthCenter.id),
      providers: [
        BlocProvider(
          create: (context) => GetMembersCubit(
            getMembers: getIt(),
            memberType: memberType,
            healthCenterId: healthCenter.id,
          )..getMembers(),
        ),

        // Inline form
        BlocProvider(
          create: (context) => InlineFormCubit<Member>(),
        ),

        // Delete member
        BlocProvider(
          create: (context) => DeleteMemberCubit(
            getIt(),
            memberType: memberType,
            healthCenterId: healthCenter.id,
          ),
        ),
      ],
      child: _HealthCenterMembersPage(
        memberType: memberType,
        healthCenter: healthCenter,
      ),
    );
  }
}

class _HealthCenterMembersPage extends StatelessWidget {
  final HealthCenter healthCenter;
  final MemberType memberType;

  const _HealthCenterMembersPage({
    required this.memberType,
    required this.healthCenter,
  });

  @override
  Widget build(BuildContext context) {
    final user = context.read<AuthenticatedCubit>().state.user!;
    final inlineForm = context.watch<InlineFormCubit<Member>>();

    final canEdit = memberType == MemberType.APS
        ? (user.role == UserRole.ROOT ||
            healthCenter.responsibility == MemberResponsibility.ADMINISTRATOR)
        : true;

    if (inlineForm.state.enabled) {
      return SingleChildScrollView(
        padding: bodyPadding,
        physics: const ScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PageTitle(
              title: memberType == MemberType.APS ? "APS" : "Accompagnants",
            ),
            MemberForm(
              closeEditMode: () {
                inlineForm.disableForm();

                context.read<GetMembersCubit>().getMembers();
              },
              healthCenter: healthCenter,
              editable: inlineForm.state.editable,
              memberType: memberType,
            ),
          ],
        ),
      );
    }

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          context.read<GetMembersCubit>().getMembers();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: bodyPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PageTitle(
                title: memberType == MemberType.APS ? "APS" : "Accompagnants",
              ),
              BlocBuilder<GetMembersCubit, GetMembersState>(
                builder: (context, state) {
                  switch (state) {
                    case GetMembersInitial() || GetMembersLoading():
                      return const Center(child: GFLoader());

                    case GetMembersLoaded(members: List<Member> members):
                      if (members.isEmpty) {
                        return const EmptyList(
                          message: "Aucune donnée disponible.",
                        );
                      }

                      return MemberList(
                        members: members,
                        canEdit: canEdit,
                      );

                    case GetMembersError(message: String message):
                      return RetryWidget(
                        message: message,
                        onPressed: () {
                          context.read<GetMembersCubit>().getMembers();
                        },
                      );
                    // ignore: unreachable_switch_default
                    default:
                  }

                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: canEdit
          ? FloatingActionButton(
              heroTag: memberType.name,
              onPressed: () {
                inlineForm.enableForm();
              },
              child: const Icon(Icons.add),
            )
          : null,
    );
  }
}

class MemberList extends StatelessWidget {
  final bool canEdit;
  final List<Member> members;

  const MemberList({
    super.key,
    required this.members,
    required this.canEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: members.length,
          itemBuilder: (context, index) {
            final member = members[index];

            return MemberItem(
              member: member,
              canEdit: canEdit,
            );
          },
        ),

        // Show delete message
        BlocListener<DeleteMemberCubit, DeleteMemberState>(
          listener: (_, state) {
            switch (state) {
              case DeleteMemberLoading():
                showToast(
                  context,
                  type: ToastType.info,
                  message: "Suppression..",
                );
                break;

              case DeleteMemberError():
                showToast(
                  context,
                  type: ToastType.error,
                  message: state.message,
                );
                break;

              case DeleteMemberSuccess(message: String message):
                showToast(
                  context,
                  type: ToastType.success,
                  message: message,
                );

                // Refresh the list after deletion
                context.read<GetMembersCubit>().getMembers();
                break;
              default:
            }
          },
          child: const SizedBox.shrink(),
        ),
      ],
    );
  }
}
