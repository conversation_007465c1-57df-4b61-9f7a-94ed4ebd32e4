// ignore_for_file: constant_identifier_names

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class Treatment extends Equatable {
  final String id;

  final String instanceId;

  final TreatmentType type;

  final DateTime createdAt;

  final DateTime updatedAt;

  const Treatment({
    required this.id,
    required this.instanceId,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [id];
}

class TreatmentShow extends Treatment {
  final String observation;
  final String? attachment;

  const TreatmentShow({
    required super.id,
    required super.instanceId,
    required super.type,
    required this.observation,
    required this.attachment,
    required super.createdAt,
    required super.updatedAt,
  });

  @override
  List<Object?> get props => [super.id];
}

enum TreatmentType {
  MEDICAL_CARE,

  LEGAL_AND_JUDICIAL_CARE,

  PSYCHOLOGICAL_CARE,

  SAFEHOUSE_CARE;

  getLabel() {
    return switch (this) {
      MEDICAL_CARE => "Médicale",
      LEGAL_AND_JUDICIAL_CARE => "Juridique et judiciaire",
      PSYCHOLOGICAL_CARE => "Psychologique",
      SAFEHOUSE_CARE => "Safehouse",
    };
  }

  IconData getIcon() {
    return switch (this) {
      MEDICAL_CARE => Icons.local_hospital,
      LEGAL_AND_JUDICIAL_CARE => Icons.gavel,
      PSYCHOLOGICAL_CARE => Icons.psychology,
      SAFEHOUSE_CARE => Icons.home,
    };
  }
}
