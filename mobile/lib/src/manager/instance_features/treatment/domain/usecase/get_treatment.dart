import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/treatment.dart';
import '../repository/treatment_repository.dart';

@injectable
class GetTreatment extends UseCase<TreatmentShow, GetTreatmentParams> {
  final TreatmentRepository repository;

  GetTreatment(this.repository);

  @override
  call(params) {
    return repository.getTreatment(
      instanceId: params.instanceId,
      treatmentId: params.treatmentId,
    );
  }
}

class GetTreatmentParams {
  final String instanceId;
  final String treatmentId;

  GetTreatmentParams({required this.instanceId, required this.treatmentId});
}
