import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/treatment_repository.dart';

@injectable
class DeleteTreatment extends UseCase<MessageResponse, DeleteTreatmentParams> {
  final TreatmentRepository repository;

  DeleteTreatment(this.repository);

  @override
  call(DeleteTreatmentParams params) async {
    return repository.deleteTreatment(
      instanceId: params.instanceId,
      treatmentId: params.treatmentId,
    );
  }
}

class DeleteTreatmentParams {
  final String instanceId;
  final String treatmentId;

  DeleteTreatmentParams({required this.instanceId, required this.treatmentId});
}
