import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/treatment.dart';
import '../repository/treatment_repository.dart';

@injectable
class GetTreatmentList
    extends UseCase<List<Treatment>, GetTreatmentListParams> {
  final TreatmentRepository repository;

  GetTreatmentList(this.repository);

  @override
  call(params) {
    return repository.getTreatmentList(instanceId: params.instanceId);
  }
}

class GetTreatmentListParams {
  final String instanceId;

  GetTreatmentListParams(this.instanceId);
}
