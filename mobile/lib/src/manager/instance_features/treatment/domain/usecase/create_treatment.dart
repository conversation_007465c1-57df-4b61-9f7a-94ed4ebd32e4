import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/treatment.dart';
import '../repository/treatment_repository.dart';

@injectable
class CreateTreatment extends UseCase<TreatmentShow, CreateTreatmentParams> {
  final TreatmentRepository _repository;

  CreateTreatment(this._repository);

  @override
  call(CreateTreatmentParams params) {
    return _repository.createTreatment(
      instanceId: params.instanceId,
      type: params.type,
      observation: params.observation,
      attachment: params.attachment,
    );
  }
}

class CreateTreatmentParams {
  CreateTreatmentParams({
    required this.instanceId,
    required this.type,
    required this.observation,
    this.attachment,
  });

  final String instanceId;
  final TreatmentType type;
  final String observation;
  final String? attachment;
}
