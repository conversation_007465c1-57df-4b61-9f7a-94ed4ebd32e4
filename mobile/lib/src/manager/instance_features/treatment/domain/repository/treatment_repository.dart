import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';

import '../entity/treatment.dart';

abstract class TreatmentRepository {
  RepositoryResponse<List<Treatment>> getTreatmentList({
    required String instanceId,
  });

  RepositoryResponse<TreatmentShow> getTreatment({
    required String instanceId,
    required String treatmentId,
  });

  RepositoryResponse<TreatmentShow> createTreatment({
    required String instanceId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  });

  RepositoryResponse<TreatmentShow> editTreatment({
    required String instanceId,
    required String treatmentId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  });

  RepositoryResponse<MessageResponse> deleteTreatment({
    required String instanceId,
    required String treatmentId,
  });
}
