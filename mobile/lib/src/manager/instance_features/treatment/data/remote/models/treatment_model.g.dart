// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'treatment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TreatmentModel _$TreatmentModelFromJson(Map<String, dynamic> json) =>
    TreatmentModel(
      id: json['id'] as String,
      type: $enumDecode(_$TreatmentTypeEnumMap, json['type']),
      instanceId: json['instance_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$TreatmentModelToJson(TreatmentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$TreatmentTypeEnumMap[instance.type]!,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'instance_id': instance.instanceId,
    };

const _$TreatmentTypeEnumMap = {
  TreatmentType.MEDICAL_CARE: 'MEDICAL_CARE',
  TreatmentType.LEGAL_AND_JUDICIAL_CARE: 'LEGAL_AND_JUDICIAL_CARE',
  TreatmentType.PSYCHOLOGICAL_CARE: 'PSYCHOLOGICAL_CARE',
  TreatmentType.SAFEHOUSE_CARE: 'SAFEHOUSE_CARE',
};

TreatmentShowModel _$TreatmentShowModelFromJson(Map<String, dynamic> json) =>
    TreatmentShowModel(
      id: json['id'] as String,
      type: $enumDecode(_$TreatmentTypeEnumMap, json['type']),
      observation: json['observation'] as String,
      attachment: json['attachment'] as String?,
      instanceId: json['instance_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$TreatmentShowModelToJson(TreatmentShowModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$TreatmentTypeEnumMap[instance.type]!,
      'observation': instance.observation,
      'attachment': instance.attachment,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'instance_id': instance.instanceId,
    };
