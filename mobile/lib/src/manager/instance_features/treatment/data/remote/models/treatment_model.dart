// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/entity/treatment.dart';

part 'treatment_model.g.dart';

@JsonSerializable()
class TreatmentModel extends Treatment {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  @JsonKey(name: 'instance_id')
  final String instanceId;

  const TreatmentModel({
    required super.id,
    required super.type,
    required this.instanceId,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
          instanceId: instanceId,
        );

  factory TreatmentModel.fromJson(Map<String, dynamic> json) =>
      _$TreatmentModelFromJson(json);

  Map<String, dynamic> toJson() => _$TreatmentModelToJson(this);
}

@JsonSerializable()
class TreatmentShowModel extends TreatmentShow {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  @JsonKey(name: 'instance_id')
  final String instanceId;

  const TreatmentShowModel({
    required super.id,
    required super.type,
    required super.observation,
    required super.attachment,
    required this.instanceId,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
          instanceId: instanceId,
        );

  factory TreatmentShowModel.fromJson(Map<String, dynamic> json) =>
      _$TreatmentShowModelFromJson(json);

  Map<String, dynamic> toJson() => _$TreatmentShowModelToJson(this);
}
