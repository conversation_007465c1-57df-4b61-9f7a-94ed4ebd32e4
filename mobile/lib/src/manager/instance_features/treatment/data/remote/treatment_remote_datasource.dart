import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';

import '../../domain/entity/treatment.dart';
import 'models/treatment_model.dart';

abstract class TreatmentRemoteDataSource {
  Future<List<TreatmentModel>> getTreatmentList({
    required String instanceId,
  });

  Future<TreatmentShowModel> getTreatment({
    required String instanceId,
    required String treatmentId,
  });

  Future<TreatmentShowModel> createTreatment({
    required String instanceId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  });

  Future<TreatmentShowModel> editTreatment({
    required String instanceId,
    required String treatmentId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  });

  Future<MessageResponse> deleteTreatment({
    required String instanceId,
    required String treatmentId,
  });
}

@Injectable(as: TreatmentRemoteDataSource)
class TreatmentRemoteDataSourceImpl implements TreatmentRemoteDataSource {
  final Dio _httpClient;

  TreatmentRemoteDataSourceImpl(this._httpClient);

  @override
  Future<TreatmentShowModel> createTreatment({
    required String instanceId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  }) async {
    final hasAttachment = attachment != null;

    dynamic data = {
      'type': type.name,
      'observation': observation,
    };

    if (hasAttachment) {
      data = FormData.fromMap({
        ...data,
        'attachment': await MultipartFile.fromFile(attachment),
      });
    }

    final reqResponse = await _httpClient.post(
      '/instances/$instanceId/treatments',
      data: data,
    );

    return TreatmentShowModel.fromJson(reqResponse.data['data']);
  }

  @override
  Future<MessageResponse> deleteTreatment({
    required String instanceId,
    required String treatmentId,
  }) async {
    final reqResponse = await _httpClient.delete(
      '/instances/$instanceId/treatments/$treatmentId',
    );

    return MessageResponse.fromJson(reqResponse.data);
  }

  @override
  Future<TreatmentShowModel> editTreatment({
    required String instanceId,
    required String treatmentId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  }) async {
    final hasAttachment = attachment != null;

    dynamic data = {
      'type': type.name,
      'observation': observation,
      '_method': 'PUT'
    };

    if (hasAttachment) {
      data = FormData.fromMap({
        ...data,
        'attachment': await MultipartFile.fromFile(attachment),
      });
    }

    final reqResponse = await _httpClient.post(
      '/instances/$instanceId/treatments/$treatmentId',
      data: data,
    );

    return TreatmentShowModel.fromJson(reqResponse.data['data']);
  }

  @override
  Future<TreatmentShowModel> getTreatment({
    required String instanceId,
    required String treatmentId,
  }) async {
    final response =
        await _httpClient.get('/instances/$instanceId/treatments/$treatmentId');

    return TreatmentShowModel.fromJson(response.data['data']);
  }

  @override
  Future<List<TreatmentModel>> getTreatmentList({
    required String instanceId,
  }) async {
    final response = await _httpClient.get(
      '/instances/$instanceId/treatments',
    );

    return (response.data['data'] as List)
        .map<TreatmentModel>((e) => TreatmentModel.fromJson(e))
        .toList();
  }
}
