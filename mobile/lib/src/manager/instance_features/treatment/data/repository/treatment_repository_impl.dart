import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/entity/treatment.dart';

import '../../domain/repository/treatment_repository.dart';
import '../remote/treatment_remote_datasource.dart';

@Injectable(as: TreatmentRepository)
class TreatmentRepositoryImpl extends TreatmentRepository {
  final TreatmentRemoteDataSource _remoteDataSource;

  TreatmentRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<TreatmentShow> createTreatment({
    required String instanceId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  }) {
    return requestHelper(() => _remoteDataSource.createTreatment(
          instanceId: instanceId,
          type: type,
          observation: observation,
          attachment: attachment,
        ));
  }

  @override
  RepositoryResponse<MessageResponse> deleteTreatment({
    required String instanceId,
    required String treatmentId,
  }) {
    return requestHelper(() => _remoteDataSource.deleteTreatment(
          instanceId: instanceId,
          treatmentId: treatmentId,
        ));
  }

  @override
  RepositoryResponse<TreatmentShow> editTreatment({
    required String instanceId,
    required String treatmentId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  }) {
    return requestHelper(
      () => _remoteDataSource.editTreatment(
        instanceId: instanceId,
        treatmentId: treatmentId,
        type: type,
        observation: observation,
        attachment: attachment,
      ),
    );
  }

  @override
  RepositoryResponse<TreatmentShow> getTreatment({
    required String instanceId,
    required String treatmentId,
  }) {
    return requestHelper(
      () => _remoteDataSource.getTreatment(
        instanceId: instanceId,
        treatmentId: treatmentId,
      ),
    );
  }

  @override
  RepositoryResponse<List<Treatment>> getTreatmentList({
    required String instanceId,
  }) {
    return requestHelper(
      () => _remoteDataSource.getTreatmentList(
        instanceId: instanceId,
      ),
    );
  }
}
