import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/core/helpers/url.dart';
import 'package:s3g/src/manager/instance_features/treatment/treatment.dart';

import '../blocs/get_treatment/get_treatment_cubit.dart';

class TreatmentDetail extends StatelessWidget {
  final double parentHeight;
  final Treatment treatment;

  const TreatmentDetail({
    super.key,
    required this.treatment,
    required this.parentHeight,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      key: ValueKey(treatment.id),
      create: (_) => GetTreatmentCubit(
        getIt(),
        instanceId: treatment.instanceId,
      )..getTreatment(treatmentId: treatment.id),

      // Child
      child: _TreatmentDetailContent(
        parentHeight: parentHeight,
        treatment: treatment,
      ),
    );
  }
}

class _TreatmentDetailContent extends StatelessWidget {
  final double parentHeight;
  final Treatment treatment;

  const _TreatmentDetailContent({
    required this.parentHeight,
    required this.treatment,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: parentHeight,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 10,
        ),
        child: BlocBuilder<GetTreatmentCubit, GetTreatmentState>(
          builder: (_, state) {
            switch (state) {
              case GetTreatmentInitial() || GetTreatmentLoading():
                return const GFShimmer(child: _ShimmerWidget());

              case GetTreatmentFailure(message: String message):
                return RetryWidget(
                  message: message,
                  onPressed: () {
                    context
                        .read<GetTreatmentCubit>()
                        .getTreatment(treatmentId: treatment.id);
                  },
                );

              case GetTreatmentSuccess(treatment: TreatmentShow treatmentShow):
                return _TreatmentDetail(treatment: treatmentShow);
              // ignore: unreachable_switch_default
              default:
            }

            return Container();
          },
        ),
      ),
    );
  }
}

class _TreatmentDetail extends StatelessWidget {
  final TreatmentShow treatment;
  const _TreatmentDetail({required this.treatment});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Prise en charge: \n${treatment.type.getLabel()}",
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.apply(fontWeightDelta: 1),
            ),

            // File viewer
            if (treatment.attachment != null)
              FileViewer(
                url: remoteStorageFile(treatment.attachment!),
                loaderWidget: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GFLoader(),
                    Text('Pièce jointe'),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(40),
                      ),
                      child: const Icon(Icons.attach_file),
                    ),
                    const Text('Pièce jointe'),
                  ],
                ),
              ),
          ],
        ),

        // Some spaces
        columnSizedBox,
        columnSizedBox,

        // Observation
        Expanded(
          child: SingleChildScrollView(
            child: Text(
              treatment.observation,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
        )
      ],
    );
  }
}

class _ShimmerWidget extends StatelessWidget {
  const _ShimmerWidget();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              width: 104,
              height: 30,
              color: Colors.white,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: const Icon(Icons.attach_file),
                ),
                const Text('Pièce jointe'),
              ],
            ),
          ],
        ),
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.02,
        ),
        Container(
          width: double.infinity,
          height: MediaQuery.of(context).size.height * 0.2,
          color: Colors.white,
        ),
      ],
    );
  }
}
