import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/src/manager/instance_features/treatment/treatment.dart';

class TreatmentItem extends StatelessWidget {
  final void Function()? onTap;
  final Treatment treatment;

  const TreatmentItem({
    super.key,
    required this.treatment,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      ListTile(
        onTap: onTap,

        // Remove vertical space
        contentPadding: const EdgeInsets.symmetric(vertical: 0),

        // title
        title: Text("Prise en charge: ${treatment.type.getLabel()}"),

        leading: G<PERSON>vatar(
          backgroundColor: Colors.grey[300],
          size: 27.0,
          child: Icon(
            treatment.type.getIcon(),
            color: Colors.grey[800],
          ),
        ),

        // subtitle
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 10),
            LocalTimeago(
              date: treatment.createdAt,
              builder: (_, value) => Text(
                value,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
              ),
            )
          ],
        ),
      )
    ]);
  }
}
