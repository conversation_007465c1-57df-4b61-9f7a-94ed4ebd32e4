part of 'delete_treatment_cubit.dart';

@immutable
sealed class DeleteTreatmentState {}

final class DeleteTreatmentInitial extends DeleteTreatmentState {}

final class DeleteTreatmentLoading extends DeleteTreatmentState {}

final class DeleteTreatmentSuccess extends DeleteTreatmentState {
  final String message;

  DeleteTreatmentSuccess(this.message);
}

final class DeleteTreatmentFailure extends DeleteTreatmentState {
  final String message;

  DeleteTreatmentFailure(this.message);
}
