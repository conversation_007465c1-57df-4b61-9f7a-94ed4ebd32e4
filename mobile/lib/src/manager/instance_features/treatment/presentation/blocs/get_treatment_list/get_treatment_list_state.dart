part of 'get_treatment_list_cubit.dart';

@immutable
sealed class GetTreatmentListState {}

final class GetTreatmentListInitial extends GetTreatmentListState {}

final class GetTreatmentListLoading extends GetTreatmentListState {}

final class GetTreatmentListError extends GetTreatmentListState {
  final String message;

  GetTreatmentListError({required this.message});
}

final class GetTreatmentListLoaded extends GetTreatmentListState {
  final List<Treatment> treatments;

  GetTreatmentListLoaded({required this.treatments});
}
