part of 'get_treatment_cubit.dart';

@immutable
sealed class GetTreatmentState {}

final class GetTreatmentInitial extends GetTreatmentState {}

final class GetTreatmentLoading extends GetTreatmentState {}

final class GetTreatmentSuccess extends GetTreatmentState {
  final TreatmentShow treatment;

  GetTreatmentSuccess(this.treatment);
}

final class GetTreatmentFailure extends GetTreatmentState {
  final String message;

  GetTreatmentFailure(this.message);
}
