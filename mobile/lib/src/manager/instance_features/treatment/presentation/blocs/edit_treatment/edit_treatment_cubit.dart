import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/entity/treatment.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/usecase/edit_treatment.dart';

part 'edit_treatment_state.dart';

class EditTreatmentCubit extends Cubit<EditTreatmentState> {
  final EditTreatment _editTreatment;
  final String instanceId;

  EditTreatmentCubit(this._editTreatment, {required this.instanceId})
      : super(EditTreatmentInitial());

  Future<void> editTreatment({
    required String treatmentId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  }) async {
    emit(EditTreatmentLoading());

    final result = await _editTreatment(
      EditTreatmentParams(
        instanceId: instanceId,
        treatmentId: treatmentId,
        type: type,
        observation: observation,
        attachment: attachment,
      ),
    );

    result.fold(
      (l) => emit(EditTreatmentFailure(l.message)),
      (r) => emit(EditTreatmentSuccess(r)),
    );
  }
}
