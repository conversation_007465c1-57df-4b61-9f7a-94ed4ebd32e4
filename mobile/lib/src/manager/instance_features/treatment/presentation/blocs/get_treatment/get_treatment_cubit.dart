import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/entity/treatment.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/usecase/get_treatment.dart';

part 'get_treatment_state.dart';

class GetTreatmentCubit extends Cubit<GetTreatmentState> {
  final GetTreatment _getTreatment;
  final String instanceId;

  GetTreatmentCubit(this._getTreatment, {required this.instanceId})
      : super(GetTreatmentInitial());

  Future<void> getTreatment({required String treatmentId}) async {
    emit(GetTreatmentLoading());

    final result = await _getTreatment(
      GetTreatmentParams(instanceId: instanceId, treatmentId: treatmentId),
    );

    result.fold(
      (failure) => emit(GetTreatmentFailure(failure.message)),
      (treatment) => emit(GetTreatmentSuccess(treatment)),
    );
  }
}
