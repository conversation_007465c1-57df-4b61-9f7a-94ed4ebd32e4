import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/usecase/delete_treatment.dart';

part 'delete_treatment_state.dart';

class DeleteTreatmentCubit extends Cubit<DeleteTreatmentState> {
  final DeleteTreatment _deleteTreatment;
  final String instanceId;

  DeleteTreatmentCubit(this._deleteTreatment, {required this.instanceId})
      : super(DeleteTreatmentInitial());

  void deleteTreatment({required String treatmentId}) async {
    emit(DeleteTreatmentLoading());

    final result = await _deleteTreatment(
      DeleteTreatmentParams(
        instanceId: instanceId,
        treatmentId: treatmentId,
      ),
    );

    result.fold(
      (l) => emit(DeleteTreatmentFailure(l.message)),
      (r) => emit(DeleteTreatmentSuccess(r.message)),
    );
  }
}
