part of 'create_treatment_cubit.dart';

@immutable
sealed class CreateTreatmentState {}

final class CreateTreatmentInitial extends CreateTreatmentState {}

final class CreateTreatmentLoading extends CreateTreatmentState {}

final class CreateTreatmentSuccess extends CreateTreatmentState {
  final TreatmentShow treatment;

  CreateTreatmentSuccess(this.treatment);
}

final class CreateTreatmentFailure extends CreateTreatmentState {
  final String message;

  CreateTreatmentFailure(this.message);
}
