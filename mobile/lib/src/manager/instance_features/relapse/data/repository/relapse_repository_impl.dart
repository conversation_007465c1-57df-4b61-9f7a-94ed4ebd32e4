import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';

import 'package:s3g/core/repository/repository.dart';

import 'package:s3g/src/manager/instance_features/relapse/domain/entity/relapse.dart';

import '../../domain/repository/relapse_repository.dart';
import '../remote/relapse_remote_datasource.dart';

@Injectable(as: RelapseRepository)
class RelapseRepositoryImpl implements RelapseRepository {
  final RelapseRemoteDataSource _relapseRemoteDataSource;

  RelapseRepositoryImpl(this._relapseRemoteDataSource);

  @override
  RepositoryResponse<Relapse> createRelapse({
    required String instanceId,
    required String description,
  }) {
    return requestHelper(() => _relapseRemoteDataSource.createRelapse(
          instanceId: instanceId,
          description: description,
        ));
  }

  @override
  RepositoryResponse<MessageResponse> deleteRelapse({
    required String instanceId,
  }) {
    return requestHelper(() => _relapseRemoteDataSource.deleteRelapse(
          instanceId: instanceId,
        ));
  }

  @override
  RepositoryResponse<Relapse> getRelapse({required String instanceId}) {
    return requestHelper(() => _relapseRemoteDataSource.getRelapse(
          instanceId: instanceId,
        ));
  }

  @override
  RepositoryResponse<Relapse> updateRelapse({
    required String instanceId,
    required String description,
  }) {
    return requestHelper(() => _relapseRemoteDataSource.updateRelapse(
          instanceId: instanceId,
          description: description,
        ));
  }
}
