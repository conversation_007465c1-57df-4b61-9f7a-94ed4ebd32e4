// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/manager/instance_features/relapse/domain/entity/relapse.dart';
part 'relapse_model.g.dart';

@JsonSerializable()
class RelapseModel extends Relapse {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const RelapseModel({
    required super.id,
    required super.description,
    required this.createdAt,
    required this.updatedAt,
  }) : super(createdAt: createdAt, updatedAt: updatedAt);

  factory RelapseModel.fromJson(Map<String, dynamic> json) =>
      _$RelapseModelFromJson(json);

  Map<String, dynamic> toJson() => _$RelapseModelToJson(this);
}
