import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';

import 'models/relapse_model.dart';

abstract class RelapseRemoteDataSource {
  Future<RelapseModel> createRelapse({
    required String instanceId,
    required String description,
  });

  Future<MessageResponse> deleteRelapse({required String instanceId});

  Future<RelapseModel> getRelapse({
    required String instanceId,
  });

  Future<RelapseModel> updateRelapse({
    required String instanceId,
    required String description,
  });
}

@Injectable(as: RelapseRemoteDataSource)
class RelapseRemoteDataSourceImpl implements RelapseRemoteDataSource {
  final Dio _httpClient;

  RelapseRemoteDataSourceImpl(this._httpClient);

  @override
  Future<RelapseModel> createRelapse({
    required String instanceId,
    required String description,
  }) async {
    final response = await _httpClient.post(
      "/instances/$instanceId/relapse",
      data: {"description": description},
    );

    return RelapseModel.fromJson(response.data["data"]);
  }

  @override
  Future<MessageResponse> deleteRelapse({required String instanceId}) async {
    final response = await _httpClient.delete("/instances/$instanceId/relapse");

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<RelapseModel> getRelapse({required String instanceId}) async {
    final response = await _httpClient.get("/instances/$instanceId/relapse");

    return RelapseModel.fromJson(response.data["data"]);
  }

  @override
  Future<RelapseModel> updateRelapse({
    required String instanceId,
    required String description,
  }) async {
    final response = await _httpClient.put(
      "/instances/$instanceId/relapse",
      data: {"description": description},
    );

    return RelapseModel.fromJson(response.data["data"]);
  }
}
