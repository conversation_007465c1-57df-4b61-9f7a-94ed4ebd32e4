part of 'relapse_cubit.dart';

@immutable
sealed class RelapseState {}

final class RelapseInitial extends RelapseState {}

final class RelapseLoading extends RelapseState {}

final class RelapseSuccess extends RelapseState {
  final Relapse? relapse;

  RelapseSuccess({this.relapse});
}

final class RelapseError extends RelapseState {
  final String message;

  RelapseError({required this.message});
}
