import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';

import '../entity/relapse.dart';

abstract class RelapseRepository {
  RepositoryResponse<Relapse> getRelapse({
    required String instanceId,
  });

  RepositoryResponse<Relapse> updateRelapse({
    required String instanceId,
    required String description,
  });

  RepositoryResponse<MessageResponse> deleteRelapse({
    required String instanceId,
  });

  RepositoryResponse<Relapse> createRelapse({
    required String instanceId,
    required String description,
  });
}
