import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

import '../repository/relapse_repository.dart';

@injectable
class CreateRelapse extends UseCase<Relapse, CreateRelapseParams> {
  final RelapseRepository repository;

  CreateRelapse({required this.repository});

  @override
  call(CreateRelapseParams params) async {
    return repository.createRelapse(
      instanceId: params.instanceId,
      description: params.description,
    );
  }
}

class CreateRelapseParams {
  final String instanceId;
  final String description;

  CreateRelapseParams({required this.instanceId, required this.description});
}
