import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/relapse_repository.dart';

@injectable
class DeleteRelapse extends UseCase<MessageResponse, DeleteRelapseParams> {
  final RelapseRepository repository;

  DeleteRelapse({required this.repository});

  @override
  call(DeleteRelapseParams params) {
    return repository.deleteRelapse(instanceId: params.instanceId);
  }
}

class DeleteRelapseParams {
  final String instanceId;

  DeleteRelapseParams({required this.instanceId});
}
