import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

import '../entity/diagnostic.dart';
import '../repository/diagnostic_repository.dart';

@injectable
class CreateDiagnostic extends UseCase<Diagnostic, CreateDiagnosticParams> {
  final DiagnosticRepository repository;

  CreateDiagnostic(this.repository);

  @override
  call(CreateDiagnosticParams params) {
    return repository.createDiagnostic(
      instanceId: params.instanceId,
      questionnaire: params.questionnaire,
      response: params.response,
    );
  }
}

class CreateDiagnosticParams {
  final String instanceId;
  final Questionnaire questionnaire;
  final String response;

  CreateDiagnosticParams({
    required this.instanceId,
    required this.questionnaire,
    required this.response,
  });
}
