import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/diagnostic.dart';
import '../repository/diagnostic_repository.dart';

@injectable
class GetDiagnostics extends UseCase<List<Diagnostic>, GetDiagnosticsParams> {
  final DiagnosticRepository repository;

  GetDiagnostics({required this.repository});

  @override
  call(GetDiagnosticsParams params) {
    return repository.getDiagnostics(instanceId: params.instanceId);
  }
}

class GetDiagnosticsParams {
  final String instanceId;

  GetDiagnosticsParams({required this.instanceId});
}
