import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/diagnostic_repository.dart';

@injectable
class DeleteDiagnostic
    extends UseCase<MessageResponse, DeleteDiagnosticParams> {
  final DiagnosticRepository _repository;

  DeleteDiagnostic(this._repository);

  @override
  call(DeleteDiagnosticParams params) {
    return _repository.deleteDiagnostic(
      instanceId: params.instanceId,
      diagnosticId: params.diagnosticId,
    );
  }
}

class DeleteDiagnosticParams {
  final String instanceId;
  final String diagnosticId;

  DeleteDiagnosticParams({
    required this.instanceId,
    required this.diagnosticId,
  });
}
