import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

import '../entity/diagnostic.dart';
import '../repository/diagnostic_repository.dart';

@injectable
class EditDiagnostic extends UseCase<Diagnostic, EditDiagnosticParams> {
  final DiagnosticRepository repository;

  EditDiagnostic({required this.repository});

  @override
  call(EditDiagnosticParams params) {
    return repository.editDiagnostic(
      instanceId: params.instanceId,
      questionnaire: params.questionnaire,
      diagnosticId: params.diagnosticId,
      response: params.response,
    );
  }
}

class EditDiagnosticParams {
  final String instanceId;
  final Questionnaire questionnaire;
  final String diagnosticId;
  final String response;

  EditDiagnosticParams({
    required this.instanceId,
    required this.questionnaire,
    required this.diagnosticId,
    required this.response,
  });
}
