import 'package:equatable/equatable.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

class Diagnostic extends Equatable {
  final String id;
  final Questionnaire questionnaire;
  final String instanceId;
  final String response;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Diagnostic({
    required this.id,
    required this.questionnaire,
    required this.instanceId,
    required this.response,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [id];
}
