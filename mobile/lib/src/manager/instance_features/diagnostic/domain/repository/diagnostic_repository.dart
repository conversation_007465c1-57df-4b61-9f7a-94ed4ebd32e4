import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

import '../entity/diagnostic.dart';

abstract class DiagnosticRepository {
  RepositoryResponse<List<Diagnostic>> getDiagnostics({
    required String instanceId,
  });

  RepositoryResponse<Diagnostic> createDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String response,
  });

  RepositoryResponse<Diagnostic> editDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String diagnosticId,
    required String response,
  });

  RepositoryResponse<MessageResponse> deleteDiagnostic({
    required String instanceId,
    required String diagnosticId,
  });
}
