// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/entity/diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

part 'diagnostic_model.g.dart';

@JsonSerializable()
class DiagnosticModel extends Diagnostic {
  @override
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  @JsonKey(name: 'instance_id')
  final String instanceId;

  @override
  @JsonKey(name: 'questionnaire')
  final QuestionnaireModel questionnaire;

  const DiagnosticModel({
    required super.id,
    required super.response,
    required this.questionnaire,
    required this.instanceId,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          questionnaire: questionnaire,
          instanceId: instanceId,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  factory DiagnosticModel.fromJson(Map<String, dynamic> json) =>
      _$DiagnosticModelFromJson(json);

  Map<String, dynamic> toJson() => _$DiagnosticModelToJson(this);
}
