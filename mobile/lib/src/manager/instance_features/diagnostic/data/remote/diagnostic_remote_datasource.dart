import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

import 'models/diagnostic_model.dart';

abstract class DiagnosticRemoteDataSource {
  Future<List<DiagnosticModel>> getDiagnostics({
    required String instanceId,
  });

  Future<DiagnosticModel> createDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String response,
  });

  Future<DiagnosticModel> editDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String diagnosticId,
    required String response,
  });

  Future<MessageResponse> deleteDiagnostic({
    required String instanceId,
    required String diagnosticId,
  });
}

@Injectable(as: DiagnosticRemoteDataSource)
class DiagnosticRemoteDataSourceImpl implements DiagnosticRemoteDataSource {
  final Dio _httpClient;

  DiagnosticRemoteDataSourceImpl(this._httpClient);

  @override
  Future<DiagnosticModel> createDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String response,
  }) async {
    final isAttachment =
        questionnaire.type == QuestionnaireResponseType.ATTACHMENT;

    dynamic data = {
      'questionnaire_id': questionnaire.id,
      'response': response,
    };

    if (isAttachment) {
      data = FormData.fromMap({
        ...data,
        'response': await MultipartFile.fromFile(response),
      });
    }

    final reqResponse = await _httpClient.post(
      '/instances/$instanceId/diagnostics',
      data: data,
    );

    return DiagnosticModel.fromJson(reqResponse.data['data']);
  }

  @override
  Future<MessageResponse> deleteDiagnostic({
    required String instanceId,
    required String diagnosticId,
  }) async {
    final reqResponse = await _httpClient
        .delete('/instances/$instanceId/diagnostics/$diagnosticId');

    return MessageResponse.fromJson(reqResponse.data);
  }

  @override
  Future<DiagnosticModel> editDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String diagnosticId,
    required String response,
  }) async {
    final isAttachment =
        questionnaire.type == QuestionnaireResponseType.ATTACHMENT;

    dynamic data = {
      'questionnaire_id': questionnaire.id,
      'response': response,
      '_method': 'PUT'
    };

    if (isAttachment) {
      data = FormData.fromMap({
        ...data,
        'response': await MultipartFile.fromFile(response),
      });
    }

    final reqResponse = await _httpClient.post(
      '/instances/$instanceId/diagnostics/$diagnosticId',
      data: data,
    );

    return DiagnosticModel.fromJson(reqResponse.data['data']);
  }

  @override
  Future<List<DiagnosticModel>> getDiagnostics({
    required String instanceId,
  }) async {
    final reqResponse =
        await _httpClient.get('/instances/$instanceId/diagnostics');

    return (reqResponse.data['data'] as List)
        .map((e) => DiagnosticModel.fromJson(e))
        .toList();
  }
}
