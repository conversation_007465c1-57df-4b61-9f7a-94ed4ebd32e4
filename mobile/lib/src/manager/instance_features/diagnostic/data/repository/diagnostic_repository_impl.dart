import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/entity/diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/domain/entity/questionnaire.dart';

import '../../domain/repository/diagnostic_repository.dart';
import '../remote/diagnostic_remote_datasource.dart';

@Injectable(as: DiagnosticRepository)
class DiagnosticRepositoryImpl extends DiagnosticRepository {
  final DiagnosticRemoteDataSource _remoteDataSource;

  DiagnosticRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<Diagnostic> createDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String response,
  }) {
    return requestHelper(() {
      return _remoteDataSource.createDiagnostic(
        instanceId: instanceId,
        questionnaire: questionnaire,
        response: response,
      );
    });
  }

  @override
  RepositoryResponse<MessageResponse> deleteDiagnostic({
    required String instanceId,
    required String diagnosticId,
  }) {
    return requestHelper(() {
      return _remoteDataSource.deleteDiagnostic(
        instanceId: instanceId,
        diagnosticId: diagnosticId,
      );
    });
  }

  @override
  RepositoryResponse<Diagnostic> editDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String diagnosticId,
    required String response,
  }) {
    return requestHelper(() {
      return _remoteDataSource.editDiagnostic(
        instanceId: instanceId,
        questionnaire: questionnaire,
        diagnosticId: diagnosticId,
        response: response,
      );
    });
  }

  @override
  RepositoryResponse<List<Diagnostic>> getDiagnostics({
    required String instanceId,
  }) {
    return requestHelper(() {
      return _remoteDataSource.getDiagnostics(instanceId: instanceId);
    });
  }
}
