import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:getwidget/getwidget.dart';
import 'package:image_picker/image_picker.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

import '../blocs/create_diagnostic/create_diagnostic_cubit.dart';
import '../blocs/edit_diagnostic/edit_diagnostic_cubit.dart';
import '../blocs/get_diagnostics/get_diagnostics_cubit.dart';
import 'questionnaire_select.dart';
import 'response_dield.dart';

class DiagnosticForm extends StatelessWidget {
  final VoidCallback closeEditMode;
  final Diagnostic? editable;
  final InstanceShow instance;

  const DiagnosticForm({
    super.key,
    this.editable,
    required this.closeEditMode,
    required this.instance,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => CreateDiagnosticCubit(
            getIt(),
            instanceId: instance.id,
          ),
        ),
        BlocProvider(
          create: (_) => EditDiagnosticCubit(
            getIt(),
            instanceId: instance.id,
          ),
        ),
      ],
      child: _FormContent(
        closeEditMode: closeEditMode,
        editable: editable,
      ),
    );
  }
}

class _FormContent extends StatefulWidget {
  final VoidCallback closeEditMode;
  final Diagnostic? editable;

  const _FormContent({required this.closeEditMode, this.editable});

  @override
  State<_FormContent> createState() => _FormContentState();
}

class _FormContentState extends State<_FormContent> {
  List<Questionnaire> _questionnaires = [];
  List<Diagnostic> _diagnostics = [];

  XFile? _fileResponse;
  final _formKey = GlobalKey<FormBuilderState>();

  Questionnaire? _selectedQuestionnaire;

  @override
  void initState() {
    _selectedQuestionnaire = widget.editable?.questionnaire;

    final questionnaireBloc = context.read<GetQuestionnairesCubit>();
    final diagnosticsBloc = context.read<GetDiagnosticsCubit>();

    if (questionnaireBloc.state is GetQuestionnairesLoaded) {
      _questionnaires =
          (questionnaireBloc.state as GetQuestionnairesLoaded).questionnaires;
    }

    if (diagnosticsBloc.state is GetDiagnosticsLoaded) {
      _diagnostics =
          (diagnosticsBloc.state as GetDiagnosticsLoaded).diagnostics;
    }

    if (widget.editable == null) {
      questionnaireBloc.getQuestionnaires();
      diagnosticsBloc.getDiagnostics();
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final questionnaireState = context.watch<GetQuestionnairesCubit>().state;
    final diagnosticsState = context.watch<GetDiagnosticsCubit>().state;

    return FormBuilder(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.editable != null
                ? "Modifier le Diagnostique:"
                : "Ajouter un Diagnostique:",
            style: const TextStyle(fontSize: 18),
          ),
          columnSizedBox,

          // Questionnaire Selection
          if (widget.editable == null) ...[
            ElevatedButton(
              onPressed: () {
                final parentHeight = MediaQuery.of(context).size.height * 0.7;

                if (diagnosticsState is GetDiagnosticsLoaded &&
                    questionnaireState is GetQuestionnairesLoaded) {
                  _questionnaires = questionnaireState.questionnaires;
                  _diagnostics = diagnosticsState.diagnostics;
                }

                if (_questionnaires.isEmpty) {
                  return;
                }

                showMaterialModalBottomSheet(
                  context: context,
                  shape: bottomSheetRadius,
                  builder: (context) => QuestionnaireSelect(
                    questionnaires: _questionnaires,
                    diagnostics: _diagnostics,
                    parentHeight: parentHeight,
                    onSelect: (questionnaire) {
                      setState(() {
                        _fileResponse = null;
                        _selectedQuestionnaire = questionnaire;
                      });

                      _formKey.currentState?.reset();
                    },
                  ),
                );
              },
              child: _questionnaires.isEmpty &&
                      questionnaireState is GetQuestionnairesLoading
                  ? const GFLoader()
                  : const Text('Sélectionner un Questionnaire'),
            ),
            columnSizedBox,

            // Questionnaire
            if (_selectedQuestionnaire != null) ...[
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(Icons.input, size: 20),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      _selectedQuestionnaire!.question,
                      style: const TextStyle(fontSize: 18),
                    ),
                  ),
                ],
              ),
              columnSizedBox,
            ]
          ],

          // Edit Questionnaire
          if (widget.editable != null) ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(Icons.input, size: 20),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    widget.editable!.questionnaire.question,
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
              ],
            ),
            columnSizedBox,
          ],

          // Some Space
          columnSizedBox,

          // Response field
          if (_selectedQuestionnaire != null) ...[
            Text(
              _selectedQuestionnaire!.type.getLabel(),
              style: const TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 9),
            ResponseField(
              key: ValueKey(_selectedQuestionnaire!.id),
              questionnaire: _selectedQuestionnaire!,
              editable: widget.editable,
              onFileChange: (file) {
                setState(() {
                  _fileResponse = file;
                });
              },
            )
          ],

          columnSizedBox,

          // Create or update buttons
          if (widget.editable != null)
            BlocBuilder<EditDiagnosticCubit, EditDiagnosticState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is EditDiagnosticLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          if (widget.editable == null)
            BlocBuilder<CreateDiagnosticCubit, CreateDiagnosticState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is CreateDiagnosticLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          // Show error message on create
          BlocListener<CreateDiagnosticCubit, CreateDiagnosticState>(
            listener: (_, state) {
              switch (state) {
                case CreateDiagnosticSuccess():
                  widget.closeEditMode();
                  _formKey.currentState?.reset();

                  // Refresh the list
                  context.read<GetDiagnosticsCubit>().getDiagnostics();
                  break;

                case CreateDiagnosticError():
                  showToast(
                    context,
                    message: state.error,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          ),

          // Show error message on edit
          BlocListener<EditDiagnosticCubit, EditDiagnosticState>(
            listener: (_, state) {
              switch (state) {
                case EditDiagnosticSuccess():
                  widget.closeEditMode();
                  _formKey.currentState?.reset();

                  // Refresh the list
                  context.read<GetDiagnosticsCubit>().getDiagnostics();
                  break;

                case EditDiagnosticError():
                  showToast(
                    context,
                    message: state.error,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          )
        ],
      ),
    );
  }

  void _save() {
    final isValid = _formKey.currentState?.saveAndValidate();
    final value = _formKey.currentState?.value;

    //  Questionnaire is required
    if (_selectedQuestionnaire == null) {
      showToast(
        context,
        message: "Veuillez sélectionner un Questionnaire",
        type: ToastType.error,
      );
      return;
    }

    // File response is required
    if (_selectedQuestionnaire!.type == QuestionnaireResponseType.ATTACHMENT &&
        _fileResponse == null &&
        widget.editable == null) {
      showToast(
        context,
        message: "Veuillez choisir un fichier",
        type: ToastType.error,
      );
      return;
    }

    if (isValid != null && isValid && value != null) {
      final response = value['response'];

      // Handle edit type no attachment
      if (widget.editable != null &&
          _selectedQuestionnaire!.type !=
              QuestionnaireResponseType.ATTACHMENT) {
        context.read<EditDiagnosticCubit>().editDiagnostic(
              questionnaire: _selectedQuestionnaire!,
              diagnosticId: widget.editable!.id,
              response: response as String,
            );
      }

      // Handle edit type attachment
      if (widget.editable != null &&
          _selectedQuestionnaire!.type ==
              QuestionnaireResponseType.ATTACHMENT) {
        // File response is required
        if (_fileResponse == null) {
          widget.closeEditMode();
          return;
        }

        context.read<EditDiagnosticCubit>().editDiagnostic(
              questionnaire: _selectedQuestionnaire!,
              diagnosticId: widget.editable!.id,
              response: _fileResponse!.path,
            );
      }

      // Handle create type
      if (widget.editable == null) {
        context.read<CreateDiagnosticCubit>().createDiagnostic(
              questionnaire: _selectedQuestionnaire!,
              response: _selectedQuestionnaire!.type ==
                      QuestionnaireResponseType.ATTACHMENT
                  ? _fileResponse!.path
                  : response as String,
            );
      }
    }
  }
}
