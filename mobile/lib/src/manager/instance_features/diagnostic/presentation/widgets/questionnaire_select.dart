import 'package:flutter/material.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

class QuestionnaireSelect extends StatefulWidget {
  final void Function(Questionnaire questionnaire) onSelect;
  final List<Questionnaire> questionnaires;
  final List<Diagnostic> diagnostics;
  final double parentHeight;

  const QuestionnaireSelect({
    super.key,
    required this.onSelect,
    required this.questionnaires,
    required this.diagnostics,
    required this.parentHeight,
  });

  @override
  State<QuestionnaireSelect> createState() => _QuestionnaireSelectState();
}

class _QuestionnaireSelectState extends State<QuestionnaireSelect> {
  List<Questionnaire> _questionnaires = [];

  @override
  void initState() {
    _questionnaires = widget.questionnaires.where((questionnaire) {
      return widget.diagnostics
          .where(
              (diagnostic) => diagnostic.questionnaire.id == questionnaire.id)
          .isEmpty;
    }).toList();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (_questionnaires.isEmpty) {
      return SafeArea(
        child: SizedBox(
          height: widget.parentHeight,
          child: Center(
            child: Text(
              'Aucun questionnaire disponible.',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
        ),
      );
    }

    return SafeArea(
      child: SizedBox(
        height: widget.parentHeight,
        child: ListView.builder(
          itemCount: _questionnaires.length,
          itemBuilder: (context, index) {
            final item = _questionnaires[index];

            return Column(
              children: [
                ListTile(
                  title: Text(item.question),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    widget.onSelect(item);
                    Navigator.of(context).pop();
                  },
                ),
                const GreyDivider()
              ],
            );
          },
        ),
      ),
    );
  }
}
