import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:getwidget/getwidget.dart';
import 'package:intl/intl.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/helpers/url.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

import '../blocs/delete_diagnostic/delete_diagnostic_cubit.dart';
import '../blocs/get_diagnostics/get_diagnostics_cubit.dart';
import 'response_dield.dart';

class DiagnosticItem extends StatelessWidget {
  final Diagnostic diagnostic;

  const DiagnosticItem({super.key, required this.diagnostic});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: LocalTimeago(
            builder: (_, value) => Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            date: diagnostic.createdAt,
          ),
        ),

        // Some space
        const SizedBox(height: 5),

        // Show title
        AccordionItem(diagnostic: diagnostic),

        // Show divider
        const GreyDivider(),

        // Show space
        columnSizedBox,

        // Show delete message
        BlocListener<DeleteDiagnosticCubit, DeleteDiagnosticState>(
          listener: (_, state) {
            switch (state) {
              case DeleteDiagnosticLoading():
                showToast(
                  context,
                  type: ToastType.info,
                  message: "Suppression..",
                );
                break;

              case DeleteDiagnosticError(message: String message):
                showToast(
                  context,
                  type: ToastType.error,
                  message: message,
                );
                break;

              case DeleteDiagnosticSuccess(message: String message):
                showToast(
                  context,
                  type: ToastType.success,
                  message: message,
                );

                // Refresh the list after deletion
                context.read<GetDiagnosticsCubit>().getDiagnostics();
                break;
              default:
            }
          },
          child: const SizedBox.shrink(),
        ),
      ],
    );
  }
}

class AccordionItem extends StatefulWidget {
  final Diagnostic diagnostic;
  const AccordionItem({super.key, required this.diagnostic});

  @override
  State<AccordionItem> createState() => AccordionStateItem();
}

class AccordionStateItem extends State<AccordionItem> {
  bool _collapsed = false;

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(widget.diagnostic.id),
      enabled: !_collapsed,
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (_) {
              context
                  .read<InlineFormCubit<Diagnostic>>()
                  .enableForm(editable: widget.diagnostic);
            },
            backgroundColor: GFColors.INFO,
            foregroundColor: Colors.white,
            icon: Icons.edit,
            label: 'Modifier',
          ),
          SlidableAction(
            onPressed: (_) => _onDelete(context),
            backgroundColor: GFColors.DANGER,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Supprimer',
          ),
        ],
      ),
      child: GFAccordion(
        margin: const EdgeInsets.all(0),
        titleBorderRadius: BorderRadius.circular(10),
        titleChild: Text(
          widget.diagnostic.questionnaire.question,
          style: const TextStyle(fontSize: 18),
        ),
        contentChild: AccordionContent(diagnostic: widget.diagnostic),
        onToggleCollapsed: (value) => {
          setState(() {
            _collapsed = value;
          }),
        },
      ),
    );
  }

  void _onDelete(BuildContext context) async {
    final confirmed = await pressConfirm(context);

    if (confirmed && mounted) {
      // ignore: use_build_context_synchronously
      context
          .read<DeleteDiagnosticCubit>()
          .deleteDiagnostic(diagnosticId: widget.diagnostic.id);
    }
  }
}

class AccordionContent extends StatelessWidget {
  final Diagnostic diagnostic;

  const AccordionContent({
    super.key,
    required this.diagnostic,
  });

  @override
  Widget build(BuildContext context) {
    var response = diagnostic.response;

    if (diagnostic.questionnaire.type == QuestionnaireResponseType.ATTACHMENT) {
      return FileViewer(url: remoteStorageFile(diagnostic.response));
    }

    if (diagnostic.questionnaire.type == QuestionnaireResponseType.DATE) {
      final parsedDate = dateResponseTypeFormat.tryParse(diagnostic.response);

      if (parsedDate != null) {
        response = toBeginningOfSentenceCase(
          DateFormat.yMMMMEEEEd('fr').format(parsedDate),
        );
      }
    }

    return Text(
      response,
      style: const TextStyle(fontSize: 16),
    );
  }
}
