part of 'create_diagnostic_cubit.dart';

@immutable
sealed class CreateDiagnosticState {}

final class CreateDiagnosticInitial extends CreateDiagnosticState {}

final class CreateDiagnosticLoading extends CreateDiagnosticState {}

final class CreateDiagnosticSuccess extends CreateDiagnosticState {
  final Diagnostic diagnostic;

  CreateDiagnosticSuccess(this.diagnostic);
}

final class CreateDiagnosticError extends CreateDiagnosticState {
  final String error;

  CreateDiagnosticError(this.error);
}
