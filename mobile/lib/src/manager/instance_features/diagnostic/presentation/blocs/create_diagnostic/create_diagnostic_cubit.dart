import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/entity/diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

import '../../../domain/usecase/create_diagnostic.dart';

part 'create_diagnostic_state.dart';

class CreateDiagnosticCubit extends Cubit<CreateDiagnosticState> {
  final CreateDiagnostic _createDiagnostic;
  final String instanceId;

  CreateDiagnosticCubit(this._createDiagnostic, {required this.instanceId})
      : super(CreateDiagnosticInitial());

  Future<void> createDiagnostic({
    required Questionnaire questionnaire,
    required String response,
  }) async {
    emit(CreateDiagnosticLoading());

    final result = await _createDiagnostic(CreateDiagnosticParams(
      instanceId: instanceId,
      questionnaire: questionnaire,
      response: response,
    ));

    result.fold(
      (failure) => emit(CreateDiagnosticError(failure.message)),
      (diagnostic) => emit(CreateDiagnosticSuccess(diagnostic)),
    );
  }
}
