import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/entity/diagnostic.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/usecase/edit_diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

part 'edit_diagnostic_state.dart';

class EditDiagnosticCubit extends Cubit<EditDiagnosticState> {
  final EditDiagnostic _editDiagnostic;
  final String instanceId;

  EditDiagnosticCubit(this._editDiagnostic, {required this.instanceId})
      : super(EditDiagnosticInitial());

  Future<void> editDiagnostic({
    required Questionnaire questionnaire,
    required String diagnosticId,
    required String response,
  }) async {
    emit(EditDiagnosticLoading());

    final result = await _editDiagnostic(
      EditDiagnosticParams(
        instanceId: instanceId,
        questionnaire: questionnaire,
        diagnosticId: diagnosticId,
        response: response,
      ),
    );

    result.fold(
      (l) => emit(EditDiagnosticError(l.message)),
      (r) => emit(EditDiagnosticSuccess(r)),
    );
  }
}
