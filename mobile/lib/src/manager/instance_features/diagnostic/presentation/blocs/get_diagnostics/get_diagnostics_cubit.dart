import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/entity/diagnostic.dart';

import '../../../domain/usecase/get_diagnostics.dart';

part 'get_diagnostics_state.dart';

class GetDiagnosticsCubit extends Cubit<GetDiagnosticsState> {
  final String instanceId;
  final GetDiagnostics _diagnostics;

  GetDiagnosticsCubit(this._diagnostics, {required this.instanceId})
      : super(GetDiagnosticsInitial());

  Future<void> getDiagnostics() async {
    emit(GetDiagnosticsLoading());

    final result = await _diagnostics(
      GetDiagnosticsParams(instanceId: instanceId),
    );

    result.fold(
      (failure) => emit(GetDiagnosticsError(failure.message)),
      (diagnostics) => emit(GetDiagnosticsLoaded(diagnostics)),
    );
  }
}
