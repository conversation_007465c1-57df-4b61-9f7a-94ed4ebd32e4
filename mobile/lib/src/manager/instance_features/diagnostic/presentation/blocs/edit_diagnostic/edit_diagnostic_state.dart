part of 'edit_diagnostic_cubit.dart';

@immutable
sealed class EditDiagnosticState {}

final class EditDiagnosticInitial extends EditDiagnosticState {}

final class EditDiagnosticLoading extends EditDiagnosticState {}

final class EditDiagnosticSuccess extends EditDiagnosticState {
  final Diagnostic diagnostic;

  EditDiagnosticSuccess(this.diagnostic);
}

final class EditDiagnosticError extends EditDiagnosticState {
  final String error;

  EditDiagnosticError(this.error);
}
