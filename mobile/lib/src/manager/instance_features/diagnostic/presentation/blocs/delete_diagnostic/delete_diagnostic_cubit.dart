import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/usecase/delete_diagnostic.dart';

part 'delete_diagnostic_state.dart';

class DeleteDiagnosticCubit extends Cubit<DeleteDiagnosticState> {
  final DeleteDiagnostic _deleteDiagnostic;
  final String instanceId;

  DeleteDiagnosticCubit(this._deleteDiagnostic, {required this.instanceId})
      : super(DeleteDiagnosticInitial());

  Future<void> deleteDiagnostic({required String diagnosticId}) async {
    emit(DeleteDiagnosticLoading());

    final result = await _deleteDiagnostic(DeleteDiagnosticParams(
      instanceId: instanceId,
      diagnosticId: diagnosticId,
    ));

    result.fold(
      (l) => emit(DeleteDiagnosticError(l.message)),
      (r) => emit(DeleteDiagnosticSuccess(r.message)),
    );
  }
}
