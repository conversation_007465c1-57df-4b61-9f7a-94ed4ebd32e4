import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:getwidget/getwidget.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';

import '../blocs/delete_companion/delete_companion_cubit.dart';
import '../blocs/get_companion_list/get_companion_list_cubit.dart';
import '../widgets/add_companion_form.dart';
import '../widgets/companion_item.dart';

class CompanionListPage extends StatelessWidget {
  const CompanionListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final instanceState =
        context.read<InstanceDetailCubit>().state as InstanceDetailLoaded;

    return MultiBlocProvider(
      key: ObjectKey(instanceState.instance.id),
      providers: [
        // Get Diagnostic
        BlocProvider(
          create: (_) => GetCompanionListCubit(
            getIt(),
            instanceId: instanceState.instance.id,
          )..getCompanionList(),
        ),

        // Delete Diagnostic
        BlocProvider(
          create: (_) => DeleteCompanionCubit(
            getIt(),
            instanceId: instanceState.instance.id,
          ),
        ),
      ],
      child: _CompanionListContent(
        instance: instanceState.instance,
      ),
    );
  }
}

class _CompanionListContent extends StatelessWidget {
  final InstanceShow instance;
  const _CompanionListContent({required this.instance});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton:
          BlocBuilder<GetCompanionListCubit, GetCompanionListState>(
        builder: (context, state) {
          List<Companion> companions = [];

          if (state is GetCompanionListLoaded) {
            companions = state.companions;
          }

          if (instance.status == InstanceStatus.CLOSED) {
            return const SizedBox.shrink();
          }

          return FloatingActionButton(
            heroTag: "CompanionListPage",
            onPressed: () {
              final parentHeight = MediaQuery.of(context).size.height * 0.7;

              showMaterialModalBottomSheet(
                context: context,
                shape: bottomSheetRadius,
                builder: (_) => AddCompanionForm(
                  companions: companions,
                  parentHeight: parentHeight,
                  instance: instance,
                  onCompanionAdded: () {
                    context.read<GetCompanionListCubit>().getCompanionList();
                  },
                ),
              );
            },
            child: const Icon(Icons.add),
          );
        },
      ),

      // Content
      body: RefreshIndicator(
        onRefresh: () async {
          context.read<GetCompanionListCubit>().getCompanionList();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: bodyPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const PageTitle(title: "Accompagnants"),

              //  List
              BlocBuilder<GetCompanionListCubit, GetCompanionListState>(
                builder: (_, state) {
                  switch (state) {
                    case GetCompanionListInitial() || GetCompanionListLoading():
                      return const Center(child: GFLoader());

                    case GetCompanionListError(message: String message):
                      return RetryWidget(
                        message: message,
                        onPressed: () {
                          context
                              .read<GetCompanionListCubit>()
                              .getCompanionList();
                        },
                      );

                    case GetCompanionListLoaded(
                        companions: List<Companion> companions
                      ):
                      if (companions.isEmpty) {
                        return const EmptyList(
                          message: "Aucun accompagnant\ntrouvé pour ce cas.",
                        );
                      }

                      return ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: companions.length,
                        itemBuilder: (context, index) {
                          final companion = companions[index];

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // List title
                              Slidable(
                                endActionPane: ActionPane(
                                  motion: const ScrollMotion(),
                                  children: [
                                    SlidableAction(
                                      onPressed: (_) {
                                        _onDelete(context, companion);
                                      },
                                      backgroundColor: GFColors.DANGER,
                                      foregroundColor: Colors.white,
                                      icon: Icons.delete,
                                      label: 'Supprimer',
                                    ),
                                  ],
                                ),

                                // Companion item
                                child: CompanionItem(companion: companion),
                              ),

                              // Separator
                              const SizedBox(height: 5),
                              const GreyDivider(),
                            ],
                          );
                        },
                      );

                    // ignore: unreachable_switch_default
                    default:
                  }

                  return Container();
                },
              ),

              // Show delete message
              BlocListener<DeleteCompanionCubit, DeleteCompanionState>(
                listener: (_, state) {
                  switch (state) {
                    case DeleteCompanionLoading():
                      showToast(
                        context,
                        type: ToastType.info,
                        message: "Suppression..",
                      );
                      break;

                    case DeleteCompanionError():
                      showToast(
                        context,
                        type: ToastType.error,
                        message: state.message,
                      );
                      break;

                    case DeleteCompanionSuccess(message: String message):
                      showToast(
                        context,
                        type: ToastType.success,
                        message: message,
                      );

                      // Refresh the list after deletion
                      context.read<GetCompanionListCubit>().getCompanionList();
                      break;
                    default:
                  }
                },
                child: const SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onDelete(BuildContext context, Companion companion) async {
    final confirmed = await pressConfirm(context);

    if (confirmed) {
      // ignore: use_build_context_synchronously
      context
          .read<DeleteCompanionCubit>()
          .deleteCompanion(companionId: companion.id);
    }
  }
}
