import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';

import '../../../domain/usecase/get_companion_list.dart';

part 'get_companion_list_state.dart';

class GetCompanionListCubit extends Cubit<GetCompanionListState> {
  final GetCompanionList _getCompanionList;
  final String instanceId;

  GetCompanionListCubit(
    this._getCompanionList, {
    required this.instanceId,
  }) : super(GetCompanionListInitial());

  Future<void> getCompanionList() async {
    emit(GetCompanionListLoading());

    final result = await _getCompanionList(
      GetCompanionListParams(instanceId: instanceId),
    );

    result.fold(
      (l) => emit(GetCompanionListError(l.message)),
      (r) => emit(GetCompanionListLoaded(r)),
    );
  }
}
