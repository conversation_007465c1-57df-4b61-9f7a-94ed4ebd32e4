part of 'create_companion_cubit.dart';

@immutable
sealed class CreateCompanionState {}

final class CreateCompanionInitial extends CreateCompanionState {}

final class CreateCompanionLoading extends CreateCompanionState {}

final class CreateCompanionSuccess extends CreateCompanionState {
  final Companion companion;

  CreateCompanionSuccess(this.companion);
}

final class CreateCompanionError extends CreateCompanionState {
  final String message;

  CreateCompanionError(this.message);
}
