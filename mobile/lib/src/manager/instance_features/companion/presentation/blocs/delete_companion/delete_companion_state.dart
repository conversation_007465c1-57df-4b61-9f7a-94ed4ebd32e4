part of 'delete_companion_cubit.dart';

@immutable
sealed class DeleteCompanionState {}

final class DeleteCompanionInitial extends DeleteCompanionState {}

final class DeleteCompanionLoading extends DeleteCompanionState {}

final class DeleteCompanionError extends DeleteCompanionState {
  final String message;

  DeleteCompanionError(this.message);
}

final class DeleteCompanionSuccess extends DeleteCompanionState {
  final String message;

  DeleteCompanionSuccess(this.message);
}
