import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../domain/usecase/delete_companion.dart';

part 'delete_companion_state.dart';

class DeleteCompanionCubit extends Cubit<DeleteCompanionState> {
  final DeleteCompanion _deleteCompanion;
  final String instanceId;

  DeleteCompanionCubit(
    this._deleteCompanion, {
    required this.instanceId,
  }) : super(DeleteCompanionInitial());

  void deleteCompanion({required String companionId}) async {
    emit(DeleteCompanionLoading());

    final result = await _deleteCompanion(
      DeleteCompanionParams(
        instanceId: instanceId,
        companionId: companionId,
      ),
    );

    result.fold(
      (failure) => emit(DeleteCompanionError(failure.message)),
      (success) => emit(DeleteCompanionSuccess(success.message)),
    );
  }
}
