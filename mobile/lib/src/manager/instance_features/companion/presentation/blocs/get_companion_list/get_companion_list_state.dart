part of 'get_companion_list_cubit.dart';

@immutable
sealed class GetCompanionListState {}

final class GetCompanionListInitial extends GetCompanionListState {}

final class GetCompanionListLoading extends GetCompanionListState {}

final class GetCompanionListLoaded extends GetCompanionListState {
  final List<Companion> companions;

  GetCompanionListLoaded(this.companions);
}

final class GetCompanionListError extends GetCompanionListState {
  final String message;

  GetCompanionListError(this.message);
}
