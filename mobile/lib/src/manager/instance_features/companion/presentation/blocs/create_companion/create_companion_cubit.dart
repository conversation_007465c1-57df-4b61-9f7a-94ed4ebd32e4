import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../../../domain/usecase/create_companion.dart';

part 'create_companion_state.dart';

class CreateCompanionCubit extends Cubit<CreateCompanionState> {
  final CreateCompanion _createCompanion;
  final String instanceId;

  CreateCompanionCubit(this._createCompanion, {required this.instanceId})
      : super(CreateCompanionInitial());

  Future<void> create({
    required MemberCompanionRole type,
    required String userId,
  }) async {
    emit(CreateCompanionLoading());

    final result = await _createCompanion(
      CreateCompanionParams(instanceId: instanceId, type: type, userId: userId),
    );

    result.fold(
      (l) => emit(CreateCompanionError(l.message)),
      (r) => emit(CreateCompanionSuccess(r)),
    );
  }
}
