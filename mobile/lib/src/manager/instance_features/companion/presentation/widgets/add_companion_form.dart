import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';
import 'package:s3g/src/manager/member/member.dart';
import 'package:s3g/src/manager/member/presentation/blocs/get_members/get_members_cubit.dart';

import '../blocs/create_companion/create_companion_cubit.dart';

class AddCompanionForm extends StatelessWidget {
  final double parentHeight;
  final List<Companion> companions;
  final InstanceShow instance;
  final VoidCallback? onCompanionAdded;

  const AddCompanionForm({
    super.key,
    required this.parentHeight,
    required this.companions,
    required this.instance,
    required this.onCompanionAdded,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => CreateCompanionCubit(
            getIt(),
            instanceId: instance.id,
          ),
        ),
        BlocProvider(
          create: (context) => GetMembersCubit(
            getMembers: getIt(),
            memberType: MemberType.COMPANION,
            healthCenterId: instance.healthCenter.id,
          )..getMembers(),
        ),
      ],
      child: _AddCompanionFormContent(
        parentHeight: parentHeight,
        instance: instance,
        companions: companions,
        onCompanionAdded: onCompanionAdded,
      ),
    );
  }
}

class _AddCompanionFormContent extends StatelessWidget {
  final double parentHeight;
  final List<Companion> companions;
  final InstanceShow instance;
  final VoidCallback? onCompanionAdded;

  const _AddCompanionFormContent({
    required this.parentHeight,
    required this.companions,
    required this.instance,
    required this.onCompanionAdded,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: parentHeight,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 10,
        ),
        child: BlocBuilder<GetMembersCubit, GetMembersState>(
          builder: (_, state) {
            switch (state) {
              case GetMembersInitial() || GetMembersLoading():
                return const GFShimmer(child: _ShimmerWidget());

              case GetMembersError(message: String message):
                return RetryWidget(
                  message: message,
                  onPressed: () {
                    context.read<GetMembersCubit>().getMembers();
                  },
                );

              case GetMembersLoaded(members: List<Member> members):
                return _AddCompanionFormList(
                  instance: instance,
                  companions: companions,
                  members: members,
                  onCompanionAdded: onCompanionAdded,
                );
              // ignore: unreachable_switch_default
              default:
            }

            return Container();
          },
        ),
      ),
    );
  }
}

class _AddCompanionFormList extends StatelessWidget {
  final InstanceShow instance;
  final List<Companion> companions;
  final List<Member> members;
  final VoidCallback? onCompanionAdded;

  const _AddCompanionFormList({
    required this.instance,
    required this.companions,
    required this.members,
    required this.onCompanionAdded,
  });

  @override
  Widget build(BuildContext context) {
    final filteredMembers = members.where((member) {
      return companions.where((companion) {
        return companion.user.id == member.id;
      }).isEmpty;
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Accompagnants",
          style:
              Theme.of(context).textTheme.bodyLarge?.apply(fontWeightDelta: 1),
        ),
        Text(
          "Centre de santé: ${instance.healthCenter.name}",
          style:
              Theme.of(context).textTheme.bodySmall?.apply(fontWeightDelta: 1),
        ),

        if (filteredMembers.isEmpty)
          Expanded(
            child: Center(
              child: Text(
                'Aucun accompagnant disponible.',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),

        // Observation
        if (filteredMembers.isNotEmpty)
          Expanded(
            child: SingleChildScrollView(
              child: ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: filteredMembers.length,
                itemBuilder: (context, index) {
                  final member = filteredMembers[index];

                  return BlocProvider(
                    key: ValueKey(member.id),
                    create: (_) => CreateCompanionCubit(
                      getIt(),
                      instanceId: instance.id,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CompanionFormItem(
                          instance: instance,
                          member: member,
                          onCompanionAdded: onCompanionAdded,
                        ),
                        columnSizedBox,
                        const GreyDivider()
                      ],
                    ),
                  );
                },
              ),
            ),
          )
      ],
    );
  }
}

class CompanionFormItem extends StatefulWidget {
  final InstanceShow instance;
  final Member member;
  final VoidCallback? onCompanionAdded;

  const CompanionFormItem({
    super.key,
    required this.instance,
    required this.member,
    this.onCompanionAdded,
  });

  @override
  State<CompanionFormItem> createState() => _CompanionFormItemState();
}

class _CompanionFormItemState extends State<CompanionFormItem> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: GFAccordion(
        margin: const EdgeInsets.all(0),
        titleBorderRadius: BorderRadius.circular(10),
        titleChild: Text(
          widget.member.name,
          style: const TextStyle(fontSize: 18),
        ),
        contentChild: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: IgnorePointer(
                    ignoring: widget.member.companionRole != null,
                    child: FormBuilderChoiceChips<MemberCompanionRole>(
                      name: "type",
                      spacing: 15,
                      initialValue: widget.member.companionRole,
                      selectedColor: Theme.of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.8),
                      validator: FormBuilderValidators.required(),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                      ),
                      options: MemberCompanionRole.values.where((type) {
                        if (widget.member.companionRole != null) {
                          return type == widget.member.companionRole;
                        }

                        return true;
                      }).map((type) {
                        return FormBuilderChipOption<MemberCompanionRole>(
                          value: type,
                          child: Text(
                            type.getLabel(),
                            style: const TextStyle(fontSize: 12),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),

                // BlocBuilder
                BlocBuilder<CreateCompanionCubit, CreateCompanionState>(
                  builder: (_, state) {
                    final loading = state is CreateCompanionLoading;

                    if (loading) {
                      return const Padding(
                        padding: EdgeInsets.only(top: 7),
                        child: SizedBox(
                          height: 25,
                          width: 25,
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    return Padding(
                      padding: const EdgeInsets.only(top: 7),
                      child: GFButton(
                        size: GFSize.MEDIUM,
                        color: Colors.grey[800] ?? Colors.grey,
                        type: GFButtonType.outline,
                        onPressed: _onSubmit,
                        child: const Text("Ajouter"),
                      ),
                    );
                  },
                ),
              ],
            ),
            BlocListener<CreateCompanionCubit, CreateCompanionState>(
              listener: (_, state) {
                switch (state) {
                  case CreateCompanionError():
                    showToast(
                      context,
                      type: ToastType.error,
                      message: state.message,
                    );
                    break;

                  case CreateCompanionSuccess():
                    widget.onCompanionAdded?.call();
                    Navigator.of(context).pop();
                    break;
                  default:
                }
              },
              child: const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  void _onSubmit() {
    final isValid = _formKey.currentState?.saveAndValidate();
    final value = _formKey.currentState?.value;

    if (isValid != null && isValid && value != null) {
      final type = value['type'] as MemberCompanionRole;

      context.read<CreateCompanionCubit>().create(
            type: type,
            userId: widget.member.id,
          );
    }
  }
}

class _ShimmerWidget extends StatelessWidget {
  const _ShimmerWidget();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 104,
          height: 30,
          color: Colors.white,
        ),
        columnSizedBox,
        columnSizedBox,
        Container(
          width: double.infinity,
          height: 60,
          color: Colors.white,
        ),
        columnSizedBox,
        Container(
          width: double.infinity,
          height: 60,
          color: Colors.white,
        ),
        columnSizedBox,
        Container(
          width: double.infinity,
          height: 60,
          color: Colors.white,
        ),
      ],
    );
  }
}
