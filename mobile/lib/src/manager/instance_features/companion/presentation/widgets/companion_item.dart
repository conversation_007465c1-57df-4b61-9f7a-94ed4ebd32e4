import 'package:flutter/material.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';

class CompanionItem extends StatelessWidget {
  final Companion companion;

  const CompanionItem({
    super.key,
    required this.companion,
  });

  @override
  Widget build(BuildContext context) {
    return DetailTitle(
      leading: InitialIcon(text: companion.user.name),
      titleText: companion.user.name,
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Member phone number
          Text(
            companion.user.phone,
            style: TextStyle(
              fontSize: 15.0,
              color: Colors.grey[600],
            ),
          ),

          Text(
            "Role: ${companion.type.getLabel()}",
            style: TextStyle(
              fontSize: 15.0,
              color: Colors.grey[600],
            ),
          ),

          const SizedBox(height: 5),

          // Member responsibility
          LocalTimeago(
            date: companion.createdAt,
            builder: (_, value) => Text(
              value,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[600],
              ),
            ),
          ),

          // Member
        ],
      ),
    );
  }
}
