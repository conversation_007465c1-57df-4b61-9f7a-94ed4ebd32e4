// ignore_for_file: constant_identifier_names

import 'package:equatable/equatable.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/member/member.dart';

class Companion extends Equatable {
  final String id;
  final MemberCompanionRole type;
  final User user;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Companion({
    required this.id,
    required this.user,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [id];
}
