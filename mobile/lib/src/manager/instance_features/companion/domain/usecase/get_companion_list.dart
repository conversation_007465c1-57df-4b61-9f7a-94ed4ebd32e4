import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/companion.dart';
import '../repository/companion_repository.dart';

@injectable
class GetCompanionList
    extends UseCase<List<Companion>, GetCompanionListParams> {
  final CompanionRepository _repository;

  GetCompanionList(this._repository);

  @override
  call(GetCompanionListParams params) {
    return _repository.getCompanionList(instanceId: params.instanceId);
  }
}

class GetCompanionListParams {
  final String instanceId;

  GetCompanionListParams({required this.instanceId});
}
