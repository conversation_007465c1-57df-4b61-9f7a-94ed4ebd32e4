import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../entity/companion.dart';
import '../repository/companion_repository.dart';

@injectable
class CreateCompanion extends UseCase<Companion, CreateCompanionParams> {
  final CompanionRepository _repository;

  CreateCompanion(this._repository);

  @override
  call(CreateCompanionParams params) {
    return _repository.createCompanion(
      instanceId: params.instanceId,
      type: params.type,
      userId: params.userId,
    );
  }
}

class CreateCompanionParams {
  final String instanceId;
  final MemberCompanionRole type;
  final String userId;

  CreateCompanionParams({
    required this.instanceId,
    required this.type,
    required this.userId,
  });
}
