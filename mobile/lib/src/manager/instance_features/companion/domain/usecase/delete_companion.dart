import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/companion_repository.dart';

@injectable
class DeleteCompanion extends UseCase<MessageResponse, DeleteCompanionParams> {
  final CompanionRepository _repository;

  DeleteCompanion(this._repository);

  @override
  call(DeleteCompanionParams params) {
    return _repository.deleteCompanion(
      instanceId: params.instanceId,
      companionId: params.companionId,
    );
  }
}

class DeleteCompanionParams {
  final String instanceId;
  final String companionId;

  DeleteCompanionParams({
    required this.instanceId,
    required this.companionId,
  });
}
