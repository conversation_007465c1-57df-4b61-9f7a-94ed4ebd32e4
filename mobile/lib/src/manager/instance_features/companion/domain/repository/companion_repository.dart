import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../entity/companion.dart';

abstract class CompanionRepository {
  RepositoryResponse<List<Companion>> getCompanionList({
    required String instanceId,
  });

  RepositoryResponse<Companion> createCompanion({
    required String instanceId,
    required MemberCompanionRole type,
    required String userId,
  });

  RepositoryResponse<MessageResponse> deleteCompanion({
    required String instanceId,
    required String companionId,
  });
}
