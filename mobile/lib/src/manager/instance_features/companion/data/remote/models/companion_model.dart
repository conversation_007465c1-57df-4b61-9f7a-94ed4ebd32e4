// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';
import 'package:s3g/src/manager/member/member.dart';

part 'companion_model.g.dart';

@JsonSerializable()
class CompanionModel extends Companion {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  final UserModel user;

  const CompanionModel({
    required super.id,
    required super.type,
    required this.user,
    required this.createdAt,
    required this.updatedAt,
  }) : super(createdAt: createdAt, updatedAt: updatedAt, user: user);

  factory CompanionModel.fromJson(Map<String, dynamic> json) =>
      _$CompanionModelFromJson(json);

  Map<String, dynamic> toJson() => _$CompanionModelToJson(this);
}
