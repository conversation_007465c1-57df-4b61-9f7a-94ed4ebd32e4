import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/src/manager/member/member.dart';

import 'models/companion_model.dart';

abstract class CompanionRemoteDataSource {
  Future<List<CompanionModel>> getCompanionList({
    required String instanceId,
  });

  Future<CompanionModel> createCompanion({
    required String instanceId,
    required MemberCompanionRole type,
    required String userId,
  });

  Future<MessageResponse> deleteCompanion({
    required String instanceId,
    required String companionId,
  });
}

@Injectable(as: CompanionRemoteDataSource)
class CompanionRemoteDataSourceImpl implements CompanionRemoteDataSource {
  final Dio _httpClient;

  CompanionRemoteDataSourceImpl(this._httpClient);

  @override
  Future<CompanionModel> createCompanion({
    required String instanceId,
    required MemberCompanionRole type,
    required String userId,
  }) async {
    final data = {
      'type': type.name,
      'user_id': userId,
    };

    final response = await _httpClient.post(
      '/instances/$instanceId/companions',
      data: data,
    );

    return CompanionModel.fromJson(response.data['data']);
  }

  @override
  Future<MessageResponse> deleteCompanion({
    required String instanceId,
    required String companionId,
  }) async {
    final response = await _httpClient
        .delete('/instances/$instanceId/companions/$companionId');

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<List<CompanionModel>> getCompanionList({
    required String instanceId,
  }) async {
    final response = await _httpClient.get(
      '/instances/$instanceId/companions',
    );

    return (response.data['data'] as List)
        .map((e) => CompanionModel.fromJson(e))
        .toList();
  }
}
