// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'companion_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CompanionModel _$CompanionModelFromJson(Map<String, dynamic> json) =>
    CompanionModel(
      id: json['id'] as String,
      type: $enumDecode(_$MemberCompanionRoleEnumMap, json['type']),
      user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$CompanionModelToJson(CompanionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$MemberCompanionRoleEnumMap[instance.type]!,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'user': instance.user,
    };

const _$MemberCompanionRoleEnumMap = {
  MemberCompanionRole.CODESA: 'CODESA',
  MemberCompanionRole.AVEC: 'AVEC',
  MemberCompanionRole.RELIGIOUS_LEADER: 'RELIGIOUS_LEADER',
};
