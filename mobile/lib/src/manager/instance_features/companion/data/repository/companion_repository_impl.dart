import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/instance_features/companion/domain/entity/companion.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../../domain/repository/companion_repository.dart';
import '../remote/companion_remote_datasource.dart';

@Injectable(as: CompanionRepository)
class CompanionRepositoryImpl extends CompanionRepository {
  final CompanionRemoteDataSource _remoteDataSource;

  CompanionRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<Companion> createCompanion({
    required String instanceId,
    required MemberCompanionRole type,
    required String userId,
  }) {
    return requestHelper(() => _remoteDataSource.createCompanion(
          instanceId: instanceId,
          type: type,
          userId: userId,
        ));
  }

  @override
  RepositoryResponse<MessageResponse> deleteCompanion({
    required String instanceId,
    required String companionId,
  }) {
    return requestHelper(() => _remoteDataSource.deleteCompanion(
          instanceId: instanceId,
          companionId: companionId,
        ));
  }

  @override
  RepositoryResponse<List<Companion>> getCompanionList({
    required String instanceId,
  }) {
    return requestHelper(() => _remoteDataSource.getCompanionList(
          instanceId: instanceId,
        ));
  }
}
