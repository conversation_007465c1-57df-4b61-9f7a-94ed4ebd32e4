import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../domain/usecase/delete_followup.dart';

part 'delete_followup_state.dart';

class DeleteFollowupCubit extends Cubit<DeleteFollowupState> {
  final DeleteFollowup _deleteFollowupUseCase;
  final String instanceId;

  DeleteFollowupCubit(
    this._deleteFollowupUseCase, {
    required this.instanceId,
  }) : super(DeleteFollowupInitial());

  Future<void> deleteFollowup(String followupId) async {
    emit(DeleteFollowupLoading());

    final result = await _deleteFollowupUseCase(
      DeleteFollowupParams(
        instanceId: instanceId,
        followupId: followupId,
      ),
    );

    result.fold(
      (failure) => emit(DeleteFollowupFailure(message: failure.message)),
      (success) => emit(DeleteFollowupSuccess(message: success.message)),
    );
  }
}
