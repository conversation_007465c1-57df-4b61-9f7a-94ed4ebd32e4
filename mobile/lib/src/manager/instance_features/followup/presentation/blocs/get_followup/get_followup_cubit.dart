import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance_features/followup/followup.dart';

import '../../../domain/usecase/get_followup.dart';

part 'get_followup_state.dart';

class GetFollowupCubit extends Cubit<GetFollowupState> {
  final GetFollowup getFollowupUseCase;
  final String instanceId;

  GetFollowupCubit(
    this.instanceId, {
    required this.getFollowupUseCase,
  }) : super(GetFollowupInitial());

  void getFollowup({required String followupId}) async {
    emit(GetFollowupLoading());

    final result = await getFollowupUseCase(
      GetFollowupParams(instanceId: instanceId, followupId: followupId),
    );

    result.fold(
      (l) => emit(GetFollowupFailure(message: l.message)),
      (r) => emit(GetFollowupSuccess(r)),
    );
  }
}
