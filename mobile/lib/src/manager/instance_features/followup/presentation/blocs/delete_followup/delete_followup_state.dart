part of 'delete_followup_cubit.dart';

@immutable
sealed class DeleteFollowupState {}

final class DeleteFollowupInitial extends DeleteFollowupState {}

final class DeleteFollowupLoading extends DeleteFollowupState {}

final class DeleteFollowupSuccess extends Delete<PERSON><PERSON>owupState {
  final String message;

  DeleteFollowupSuccess({required this.message});
}

final class DeleteFollowupFailure extends DeleteFollowupState {
  final String message;

  DeleteFollowupFailure({required this.message});
}
