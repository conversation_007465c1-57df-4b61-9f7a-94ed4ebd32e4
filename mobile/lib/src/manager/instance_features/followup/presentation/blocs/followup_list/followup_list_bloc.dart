import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/instance_features/followup/followup.dart';

import '../../../domain/usecase/get_followup_list.dart';

class FollowupListBloc extends PaginatedItemBloc<Followup> {
  final GetFollowupList getFollowupListUseCase;
  final String instanceId;

  FollowupListBloc(
    this.instanceId, {
    required this.getFollowupListUseCase,
  }) : super();

  @override
  callUseCase(PaginationParams params) {
    return getFollowupListUseCase(
      GetFollowupListParams(
        instanceId: instanceId,
        page: params.page,
      ),
    );
  }
}
