import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/followup/followup.dart';

import '../blocs/delete_followup/delete_followup_cubit.dart';
import '../blocs/followup_list/followup_list_bloc.dart';
import '../blocs/get_followup/get_followup_cubit.dart';

class PaginatedItems extends StatelessWidget {
  final ScrollController scrollController;

  const PaginatedItems({super.key, required this.scrollController});

  @override
  Widget build(BuildContext context) {
    return PaginatedWidget<FollowupListBloc, Followup>(
      scrollController: scrollController,
      widgetEmpty: const EmptyList(
        message: "Aucun suivi enregistré.",
      ),
      render: (_, __, state) {
        return _ListItem(state: state);
      } as WidgetRender<Followup>,
    );
  }
}

class _ListItem extends StatelessWidget {
  final PaginatedItemState<Followup> state;

  const _ListItem({required this.state});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: state.itemCountWithLoader,
      itemBuilder: (context, index) {
        if (index >= state.items.length) {
          const Center(child: GFLoader());
        }

        final followup = state.items[index];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: LocalTimeago(
                builder: (_, value) => Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                date: followup.createdAt,
              ),
            ),

            // Some space
            const SizedBox(height: 5),

            // Show title
            _AccordionItem(followup: followup),

            // Show divider
            const GreyDivider(),

            // Show space
            columnSizedBox,

            // Show delete message
            BlocListener<DeleteFollowupCubit, DeleteFollowupState>(
              listener: (_, state) {
                switch (state) {
                  case DeleteFollowupLoading():
                    showToast(
                      context,
                      type: ToastType.info,
                      message: "Suppression..",
                    );
                    break;

                  case DeleteFollowupFailure(message: String message):
                    showToast(
                      context,
                      type: ToastType.error,
                      message: message,
                    );
                    break;

                  case DeleteFollowupSuccess(message: String message):
                    showToast(
                      context,
                      type: ToastType.success,
                      message: message,
                    );

                    // Refresh the list after deletion
                    context
                        .read<FollowupListBloc>()
                        .add(PaginatedItemFetched(refresh: true));
                    break;
                  default:
                }
              },
              child: const SizedBox.shrink(),
            ),
          ],
        );
      },
    );
  }
}

class _AccordionItem extends StatefulWidget {
  final Followup followup;
  const _AccordionItem({required this.followup});

  @override
  State<_AccordionItem> createState() => AccordionStateItem();
}

class AccordionStateItem extends State<_AccordionItem> {
  bool _collapsed = false;

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(widget.followup.id),
      enabled: !_collapsed,
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (_) => _onDelete(context),
            backgroundColor: GFColors.DANGER,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Supprimer',
          ),
        ],
      ),
      child: GFAccordion(
        margin: const EdgeInsets.all(0),
        titleBorderRadius: BorderRadius.circular(10),
        titleChild: Text(
          widget.followup.title,
          style: const TextStyle(fontSize: 18),
        ),
        contentChild: BlocBuilder<InstanceDetailCubit, InstanceDetailState>(
          builder: (_, state) {
            if (state is! InstanceDetailLoaded) {
              return const SizedBox.shrink();
            }

            return _collapsed
                ? _AccordionContent(
                    instance: state.instance,
                    followup: widget.followup,
                  )
                : const SizedBox.shrink();
          },
        ),
        onToggleCollapsed: (value) => {
          setState(() {
            _collapsed = value;
          }),
        },
      ),
    );
  }

  void _onDelete(BuildContext context) async {
    final confirmed = await pressConfirm(context);

    if (confirmed && mounted) {
      // ignore: use_build_context_synchronously
      context.read<DeleteFollowupCubit>().deleteFollowup(widget.followup.id);
    }
  }
}

class _AccordionContent extends StatelessWidget {
  final Followup followup;
  final InstanceShow instance;

  const _AccordionContent({
    required this.followup,
    required this.instance,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      key: ValueKey(followup.id),

      // Provide
      create: (_) {
        return GetFollowupCubit(
          instance.id,
          getFollowupUseCase: getIt(),
        )..getFollowup(followupId: followup.id);
      },

      // Display
      child: BlocBuilder<GetFollowupCubit, GetFollowupState>(
        builder: (_, state) {
          switch (state) {
            case GetFollowupSuccess(followup: FollowupShow followup):
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (followup.companion != null) ...[
                    Text(
                      "Accompagnant: ${followup.companion!.user.name}",
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      followup.companion!.type.getLabel(),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    const SizedBox(height: 10),
                  ],
                  Text(
                    followup.description,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              );

            case GetFollowupLoading():
              return GFShimmer(
                child: Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  margin: const EdgeInsets.all(20),
                  color: Colors.white,
                ),
              );

            case GetFollowupFailure(message: String message):
              return RetryWidget(
                message: message,
                onPressed: () {
                  context
                      .read<GetFollowupCubit>()
                      .getFollowup(followupId: followup.id);
                },
              );

            default:
              return const SizedBox.shrink();
          }
        },
      ),
    );
  }
}
