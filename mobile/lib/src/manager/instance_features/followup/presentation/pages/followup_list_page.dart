import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';

import '../blocs/delete_followup/delete_followup_cubit.dart';
import '../blocs/followup_list/followup_list_bloc.dart';
import '../widgets/paginated_items.dart';

class FollowupListPage extends StatelessWidget {
  const FollowupListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final instanceState =
        context.read<InstanceDetailCubit>().state as InstanceDetailLoaded;

    return MultiBlocProvider(
      key: ValueKey(instanceState.instance.id),
      providers: [
        // Followup List Cubit
        BlocProvider(
          create: (_) => FollowupListBloc(
            instanceState.instance.id,
            getFollowupListUseCase: getIt(),
          )..add(PaginatedItemFetched()),
        ),

        // Followup Form Cubit
        BlocProvider(
          create: (_) => DeleteFollowupCubit(
            getIt(),
            instanceId: instanceState.instance.id,
          ),
        ),
      ], // Content
      child: _FollowupListPageContent(instanceState.instance),
    );
  }
}

class _FollowupListPageContent extends StatefulWidget {
  final InstanceShow instance;

  const _FollowupListPageContent(this.instance);

  @override
  State<_FollowupListPageContent> createState() =>
      _FollowupListPageContentState();
}

class _FollowupListPageContentState extends State<_FollowupListPageContent> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        context
            .read<FollowupListBloc>()
            .add(PaginatedItemFetched(refresh: true));
      },
      // Content
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        controller: _scrollController,
        padding: bodyPadding,

        // Content
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const PageTitle(title: "Suivis"),
            PaginatedItems(
              scrollController: _scrollController,
            ),
          ],
        ),
      ),
    );
  }
}
