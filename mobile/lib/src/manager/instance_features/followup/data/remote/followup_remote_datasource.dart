import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';

import 'models/followup_model.dart';

abstract class FollowupRemoteDataSource {
  Future<MessageResponse> deleteFollowup({
    required String instanceId,
    required String followupId,
  });

  Future<FollowupShowModel> getFollowup({
    required String instanceId,
    required String followupId,
  });

  Future<Paginated<FollowupModel>> getFollowupList({
    required String instanceId,
    int? page,
  });
}

@Injectable(as: FollowupRemoteDataSource)
class FollowupRemoteDataSourceImpl implements FollowupRemoteDataSource {
  final Dio _httpClient;

  FollowupRemoteDataSourceImpl(this._httpClient);

  @override
  Future<MessageResponse> deleteFollowup({
    required String instanceId,
    required String followupId,
  }) async {
    final response = await _httpClient.delete(
      '/instances/$instanceId/followups/$followupId',
    );

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<FollowupShowModel> getFollowup({
    required String instanceId,
    required String followupId,
  }) async {
    final response = await _httpClient.get(
      '/instances/$instanceId/followups/$followupId',
    );

    return FollowupShowModel.fromJson(response.data['data']);
  }

  @override
  Future<Paginated<FollowupModel>> getFollowupList({
    required String instanceId,
    int? page,
  }) async {
    final response = await _httpClient.get(
      '/instances/$instanceId/followups',
      queryParameters: {'page': page},
    );

    return Paginated.fromJson(
      response.data,
      (data) => FollowupModel.fromJson(data as Map<String, dynamic>),
    );
  }
}
