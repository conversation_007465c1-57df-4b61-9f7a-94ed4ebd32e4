// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';

import '../../../domain/entity/followup.dart';

part 'followup_model.g.dart';

@JsonSerializable()
class FollowupModel extends Followup {
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  final CompanionModel? companion;

  const FollowupModel({
    required super.id,
    required super.title,
    required this.companion,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
          companion: companion,
        );

  factory FollowupModel.fromJson(Map<String, dynamic> json) =>
      _$FollowupModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowupModelToJson(this);
}

@JsonSerializable()
class FollowupShowModel extends FollowupShow {
  @override
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  final CompanionModel? companion;

  const FollowupShowModel({
    required super.id,
    required super.title,
    required super.description,
    required this.companion,
    required this.createdAt,
    required this.updatedAt,
  }) : super(createdAt: createdAt, updatedAt: updatedAt, companion: companion);

  factory FollowupShowModel.fromJson(Map<String, dynamic> json) =>
      _$FollowupShowModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowupShowModelToJson(this);
}
