// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'followup_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FollowupModel _$FollowupModelFromJson(Map<String, dynamic> json) =>
    FollowupModel(
      id: json['id'] as String,
      title: json['title'] as String,
      companion: json['companion'] == null
          ? null
          : CompanionModel.fromJson(json['companion'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$FollowupModelToJson(FollowupModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'companion': instance.companion,
    };

FollowupShowModel _$FollowupShowModelFromJson(Map<String, dynamic> json) =>
    FollowupShowModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      companion: json['companion'] == null
          ? null
          : CompanionModel.fromJson(json['companion'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$FollowupShowModelToJson(FollowupShowModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'companion': instance.companion,
    };
