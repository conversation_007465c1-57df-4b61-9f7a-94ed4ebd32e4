import 'package:equatable/equatable.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';

class Followup extends Equatable {
  final String id;
  final String title;
  final Companion? companion;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Followup({
    required this.id,
    required this.title,
    required this.companion,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [id];
}

class FollowupShow extends Followup {
  final String description;

  const FollowupShow({
    required super.id,
    required super.title,
    required super.companion,
    required super.createdAt,
    required super.updatedAt,
    required this.description,
  });
}
