import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/followup_repository.dart';

@injectable
class DeleteFollowup extends UseCase<MessageResponse, DeleteFollowupParams> {
  final FollowupRepository _repository;

  DeleteFollowup(this._repository);

  @override
  call(DeleteFollowupParams params) {
    return _repository.deleteFollowup(
      instanceId: params.instanceId,
      followupId: params.followupId,
    );
  }
}

class DeleteFollowupParams {
  final String instanceId;
  final String followupId;

  DeleteFollowupParams({required this.instanceId, required this.followupId});
}
