import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/followup.dart';
import '../repository/followup_repository.dart';

@injectable
class GetFollowup extends UseCase<FollowupShow, GetFollowupParams> {
  final FollowupRepository repository;

  GetFollowup({required this.repository});

  @override
  call(GetFollowupParams params) {
    return repository.getFollowup(
      instanceId: params.instanceId,
      followupId: params.followupId,
    );
  }
}

class GetFollowupParams {
  final String instanceId;
  final String followupId;

  GetFollowupParams({
    required this.instanceId,
    required this.followupId,
  });
}
