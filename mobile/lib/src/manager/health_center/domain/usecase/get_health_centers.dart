import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/health_center_repository.dart';
import '../entity/health_center.dart';

@injectable
class GetHealthCenters extends UseCase<List<HealthCenter>, NoParams> {
  final HealthCenterRepository repository;

  GetHealthCenters(this.repository);

  @override
  call(NoParams params) async {
    return repository.getHealthCenters();
  }
}
