import 'package:equatable/equatable.dart';

class HealthZone extends Equatable {
  final String id;
  final String name;
  final int populationServed;
  final DateTime createdAt;
  final DateTime updatedAt;

  const HealthZone({
    required this.id,
    required this.name,
    required this.populationServed,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [id, name, populationServed, createdAt, updatedAt];
}
