import 'package:equatable/equatable.dart';
import 'package:s3g/src/manager/member/member.dart';

import 'health_zone.dart';

class HealthCenter extends Equatable {
  final String id;
  final String name;
  final String address;
  final String phone;
  final String? servicesOffered;
  final int aps;
  final int companions;
  final int instances;
  final int relapses;
  final MemberResponsibility? responsibility;
  final HealthZone healthZone;
  final DateTime createdAt;
  final DateTime updatedAt;

  const HealthCenter({
    required this.id,
    required this.name,
    required this.address,
    required this.phone,
    required this.servicesOffered,
    required this.aps,
    required this.companions,
    required this.instances,
    required this.healthZone,
    required this.createdAt,
    required this.updatedAt,
    required this.responsibility,
    required this.relapses,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        address,
        phone,
        servicesOffered,
        aps,
        companions,
        instances,
        healthZone,
        createdAt,
        updatedAt,
        responsibility,
        relapses,
      ];
}
