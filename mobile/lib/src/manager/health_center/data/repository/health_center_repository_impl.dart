import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';

import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/src/manager/health_center/data/remote/health_center_remote_datasource.dart';

import 'package:s3g/src/manager/health_center/domain/entity/health_center.dart';

import '../../domain/repository/health_center_repository.dart';

@Injectable(as: HealthCenterRepository)
class HealthCenterRepositoryImpl extends HealthCenterRepository {
  final HealthCenterRemoteDataSource _remoteDataSource;

  HealthCenterRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, List<HealthCenter>>> getHealthCenters() {
    return requestHelper(() => _remoteDataSource.getHealthCenters());
  }
}
