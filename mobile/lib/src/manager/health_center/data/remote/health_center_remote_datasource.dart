import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/src/manager/health_center/data/remote/models/health_center_model.dart';

import '../../domain/entity/health_center.dart';

abstract class HealthCenterRemoteDataSource {
  Future<List<HealthCenter>> getHealthCenters();
}

@Injectable(as: HealthCenterRemoteDataSource)
class HealthCenterRemoteDataSourceImpl implements HealthCenterRemoteDataSource {
  final Dio _httpClient;

  HealthCenterRemoteDataSourceImpl(this._httpClient);

  @override
  Future<List<HealthCenter>> getHealthCenters() async {
    final response = await _httpClient.get('/health-centers');

    return (response.data['data'] as List<dynamic>)
        .map((item) => HealthCenterModel.fromJson(item))
        .toList();
  }
}
