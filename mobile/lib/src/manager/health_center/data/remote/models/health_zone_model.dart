// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/manager/health_center/domain/entity/health_zone.dart';

part 'health_zone_model.g.dart';

@JsonSerializable()
class HealthZoneModel extends HealthZone {
  @override
  @Json<PERSON>ey(name: "created_at")
  final DateTime createdAt;

  @override
  @JsonKey(name: "updated_at")
  final DateTime updatedAt;

  @override
  @JsonKey(name: "population_served")
  final int populationServed;

  const HealthZoneModel({
    required super.id,
    required super.name,
    required this.populationServed,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
          populationServed: populationServed,
        );

  factory HealthZoneModel.fromJson(Map<String, dynamic> json) =>
      _$HealthZoneModelFromJson(json);

  Map<String, dynamic> toJson() => _$HealthZoneModelToJson(this);
}
