// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_center_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HealthCenterModel _$HealthCenterModelFromJson(Map<String, dynamic> json) =>
    HealthCenterModel(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      phone: json['phone'] as String,
      aps: (json['aps'] as num).toInt(),
      companions: (json['companions'] as num).toInt(),
      instances: (json['instances'] as num).toInt(),
      responsibility: $enumDecodeNullable(
          _$MemberResponsibilityEnumMap, json['responsibility']),
      relapses: (json['relapses'] as num).toInt(),
      servicesOffered: json['services_offered'] as String?,
      healthZone:
          HealthZoneModel.fromJson(json['health_zone'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$HealthCenterModelToJson(HealthCenterModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'address': instance.address,
      'phone': instance.phone,
      'aps': instance.aps,
      'companions': instance.companions,
      'instances': instance.instances,
      'relapses': instance.relapses,
      'responsibility': _$MemberResponsibilityEnumMap[instance.responsibility],
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'health_zone': instance.healthZone.toJson(),
      'services_offered': instance.servicesOffered,
    };

const _$MemberResponsibilityEnumMap = {
  MemberResponsibility.ADMINISTRATOR: 'ADMINISTRATOR',
  MemberResponsibility.OPERATOR: 'OPERATOR',
  MemberResponsibility.COMPANION: 'COMPANION',
};
