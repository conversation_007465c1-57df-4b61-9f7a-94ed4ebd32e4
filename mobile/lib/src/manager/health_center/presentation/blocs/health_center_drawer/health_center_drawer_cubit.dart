import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';

part 'health_center_drawer_state.dart';

@injectable
class HealthCenterDrawerCubit extends Cubit<HealthCenterDrawerState> {
  HealthCenterDrawerCubit() : super(HealthCenterDrawerState(enabled: true));

  void enableDrawer() {
    emit(HealthCenterDrawerState(enabled: true));
  }

  void disableDrawer() {
    emit(HealthCenterDrawerState(enabled: false));
  }

  void setDrawerState(bool enabled) {
    emit(HealthCenterDrawerState(enabled: enabled));
  }
}
