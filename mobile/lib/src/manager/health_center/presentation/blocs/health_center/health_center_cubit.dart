import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../../../domain/entity/health_center.dart';
import '../../../domain/usecase/get_health_centers.dart';

part 'health_center_state.dart';

@injectable
class HealthCenterCubit extends Cubit<HealthCenterState> {
  final GetHealthCenters _getHealthCenters;

  HealthCenterCubit(this._getHealthCenters)
      : super(HealthCenterState.initial());

  Future<void> loadHealthCenter() async {
    emit(state.copyWith(status: HealthCenterStatus.loading));

    final healthCenters = await _getHealthCenters(NoParams());

    healthCenters.fold(
      (failure) => emit(state.copyWith(
        status: HealthCenterStatus.error,
        errorMessage: failure.message,
      )),
      (healthCenters) => emit(state.copyWith(
        status: HealthCenterStatus.loaded,
        healthCenters: healthCenters,
        selectedHealthCenter: state.selectedHealthCenter != null
            ? healthCenters
                    .where(
                      (healthCenter) =>
                          healthCenter.id == state.selectedHealthCenter!.id,
                    )
                    .firstOrNull ??
                state.selectedHealthCenter!
            : null,
      )),
    );
  }

  void selectHealthCenter(HealthCenter healthCenter) {
    emit(state.copyWith(selectedHealthCenter: healthCenter));
  }
}
