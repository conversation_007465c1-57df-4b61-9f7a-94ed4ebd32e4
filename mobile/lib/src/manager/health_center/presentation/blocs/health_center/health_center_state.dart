part of 'health_center_cubit.dart';

enum HealthCenterStatus { initial, loading, loaded, error }

@immutable
final class HealthCenterState {
  final List<HealthCenter> healthCenters;

  final HealthCenter? selectedHealthCenter;

  final HealthCenterStatus status;

  final String? errorMessage;

  const HealthCenterState({
    required this.healthCenters,
    required this.status,
    required this.errorMessage,
    this.selectedHealthCenter,
  });

  factory HealthCenterState.initial() {
    return const HealthCenterState(
      healthCenters: [],
      status: HealthCenterStatus.initial,
      errorMessage: null,
    );
  }

  HealthCenterState copyWith({
    List<HealthCenter>? healthCenters,
    HealthCenterStatus? status,
    String? errorMessage,
    HealthCenter? selectedHealthCenter,
  }) {
    return HealthCenterState(
      healthCenters: healthCenters ?? this.healthCenters,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      selectedHealthCenter: selectedHealthCenter ?? this.selectedHealthCenter,
    );
  }
}
