import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/manager.dart';

import '../../domain/entity/health_center.dart';
import '../widgets/card_stats.dart';

class HealthCenterDetailPage extends StatelessWidget {
  const HealthCenterDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final healthCenter =
        context.watch<HealthCenterCubit>().state.selectedHealthCenter!;

    return _HealthCenterDetailContent(
      key: ValueKey(healthCenter.id),
      healthCenter: healthCenter,
    );
  }
}

class _HealthCenterDetailContent extends StatefulWidget {
  final HealthCenter healthCenter;
  const _HealthCenterDetailContent({super.key, required this.healthCenter});

  @override
  State<_HealthCenterDetailContent> createState() =>
      _HealthCenterDetailContentState();
}

class _HealthCenterDetailContentState
    extends State<_HealthCenterDetailContent> {
  bool _loading = true;

  @override
  void initState() {
    Timer(const Duration(seconds: 1), () {
      setState(() {
        _loading = false;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final user = context.read<AuthenticatedCubit>().state.user!;
    final healthCenter = widget.healthCenter;

    final headerWidget = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Centre de santé",
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.normal,
          ),
        ),
        Text(
          healthCenter.name,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );

    return RefreshIndicator(
      onRefresh: () async {
        context.read<HealthCenterCubit>().loadHealthCenter();
      },
      child: SingleChildScrollView(
        padding: bodyPadding,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Health center name
                if (_loading) GFShimmer(child: headerWidget),

                if (!_loading) headerWidget,

                // Responsibility
                if (healthCenter.responsibility != null)
                  Text(
                    "${user.role.getLabel()} ${healthCenter.responsibility?.getLabel()}",
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                else
                  Text(
                    user.role.getLabel(),
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
              ],
            ),

            // Some space
            const SizedBox(height: 10),

            // Show shimmer
            if (_loading)
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                mainAxisSpacing: 15.0,
                crossAxisSpacing: 15.0,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  GFShimmer(
                    mainColor: Colors.grey[100] ?? Colors.grey,
                    child: const ShimmerItem(),
                  ),
                  GFShimmer(
                    mainColor: Colors.grey[100] ?? Colors.grey,
                    child: const ShimmerItem(),
                  ),
                  GFShimmer(
                    mainColor: Colors.grey[100] ?? Colors.grey,
                    child: const ShimmerItem(),
                  ),
                  GFShimmer(
                    mainColor: Colors.grey[100] ?? Colors.grey,
                    child: const ShimmerItem(),
                  ),
                ],
              ),

            // Show summary
            if (!_loading)
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                mainAxisSpacing: 15.0,
                crossAxisSpacing: 15.0,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  CardStats(
                    title: "Cas",
                    subtitle: healthCenter.instances.toString(),
                  ),
                  CardStats(
                    title: "Rechutes",
                    subtitle: healthCenter.relapses.toString(),
                  ),
                  CardStats(
                    title: "APS",
                    subtitle: healthCenter.aps.toString(),
                  ),
                  CardStats(
                    title: "Accompagnants",
                    subtitle: healthCenter.companions.toString(),
                  ),
                ],
              ),

            // Some space
            const SizedBox(height: 10),

            // Some divider
            const GreyDivider(),

            // Health zone
            TitleItem(
              title: "Zone de santé",
              subtitle: healthCenter.healthZone.name,
            ),

            const GreyDivider(),

            TitleItem(
              title: "Téléphone",
              subtitle: healthCenter.phone,
            ),

            const GreyDivider(),

            TitleItem(
              title: "Adresse",
              subtitle: healthCenter.address,
            ),
          ],
        ),
      ),
    );
  }
}

class TitleItem extends StatelessWidget {
  final String title;
  final String subtitle;

  const TitleItem({super.key, required this.title, required this.subtitle});

  @override
  Widget build(BuildContext context) {
    return GFListTile(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.all(0),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 18.0),
      ),
      subTitle: Text(
        subtitle,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: Colors.grey[700],
          fontSize: 14.0,
        ),
      ),
    );
  }
}
