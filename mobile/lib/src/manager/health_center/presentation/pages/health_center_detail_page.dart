import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/manager.dart';

import '../../domain/entity/health_center.dart';
import '../widgets/card_stats.dart';

class HealthCenterDetailPage extends StatelessWidget {
  const HealthCenterDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final healthCenter =
        context.watch<HealthCenterCubit>().state.selectedHealthCenter!;

    return _HealthCenterDetailContent(
      key: ValueKey(healthCenter.id),
      healthCenter: healthCenter,
    );
  }
}

class _HealthCenterDetailContent extends StatefulWidget {
  final HealthCenter healthCenter;
  const _HealthCenterDetailContent({super.key, required this.healthCenter});

  @override
  State<_HealthCenterDetailContent> createState() =>
      _HealthCenterDetailContentState();
}

class _HealthCenterDetailContentState
    extends State<_HealthCenterDetailContent> {
  bool _loading = true;

  @override
  void initState() {
    Timer(const Duration(seconds: 1), () {
      setState(() {
        _loading = false;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final user = context.read<AuthenticatedCubit>().state.user!;
    final healthCenter = widget.healthCenter;

    final headerWidget = Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF3B82F6),
            Color(0xFF1D4ED8),
          ],
        ),
        borderRadius: BorderRadius.circular(cardRadius),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF3B82F6).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.local_hospital,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  "Centre de santé",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            healthCenter.name,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              healthCenter.healthZone.name,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );

    return RefreshIndicator(
      onRefresh: () async {
        context.read<HealthCenterCubit>().loadHealthCenter();
      },
      child: SingleChildScrollView(
        padding: bodyPadding,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Health center name
                if (_loading)
                  Container(
                    height: 140,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF3B82F6).withValues(alpha: 0.3),
                          const Color(0xFF1D4ED8).withValues(alpha: 0.3),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(cardRadius),
                    ),
                  ),

                if (!_loading) headerWidget,

                const SizedBox(height: 16),

                // Responsibility Badge
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.person_outline,
                        size: 18,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        healthCenter.responsibility != null
                            ? "${user.role.getLabel()} ${healthCenter.responsibility?.getLabel()}"
                            : user.role.getLabel(),
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Statistics Section
            const SizedBox(height: 24),

            const Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  color: AppTheme.primary,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  "Statistiques",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Show shimmer
            if (_loading)
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                mainAxisSpacing: 16.0,
                crossAxisSpacing: 16.0,
                childAspectRatio: 0.95,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  GFShimmer(
                    mainColor: Colors.grey[100] ?? Colors.grey,
                    child: const ShimmerItem(),
                  ),
                  GFShimmer(
                    mainColor: Colors.grey[100] ?? Colors.grey,
                    child: const ShimmerItem(),
                  ),
                  GFShimmer(
                    mainColor: Colors.grey[100] ?? Colors.grey,
                    child: const ShimmerItem(),
                  ),
                  GFShimmer(
                    mainColor: Colors.grey[100] ?? Colors.grey,
                    child: const ShimmerItem(),
                  ),
                ],
              ),

            // Show summary
            if (!_loading)
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                mainAxisSpacing: 16.0,
                crossAxisSpacing: 16.0,
                childAspectRatio: 0.95,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  CardStats(
                    title: "Cas",
                    subtitle: healthCenter.instances.toString(),
                  ),
                  CardStats(
                    title: "Rechutes",
                    subtitle: healthCenter.relapses.toString(),
                  ),
                  CardStats(
                    title: "APS",
                    subtitle: healthCenter.aps.toString(),
                  ),
                  CardStats(
                    title: "Accompagnants",
                    subtitle: healthCenter.companions.toString(),
                  ),
                ],
              ),

            // Some space
            const SizedBox(height: 24),

            // Contact Information Section
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(cardRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.info_outline,
                          color: Color(0xFF3B82F6),
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        "Informations de contact",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // Phone
                  Container(
                    padding: cardSpacing,
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF8FAFC),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFFE2E8F0),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                const Color(0xFF10B981).withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.phone_outlined,
                            color: Color(0xFF10B981),
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "Téléphone",
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF6B7280),
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                healthCenter.phone,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF1F2937),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Address
                  Container(
                    padding: cardSpacing,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF8FAFC),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFFE2E8F0),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.location_on_outlined,
                            color: Color(0xFF8B5CF6),
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "Adresse",
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF6B7280),
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                healthCenter.address,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF1F2937),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Bottom spacing
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

class TitleItem extends StatelessWidget {
  final String title;
  final String subtitle;

  const TitleItem({super.key, required this.title, required this.subtitle});

  @override
  Widget build(BuildContext context) {
    return GFListTile(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.all(0),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 18.0),
      ),
      subTitle: Text(
        subtitle,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: Colors.grey[700],
          fontSize: 14.0,
        ),
      ),
    );
  }
}
