import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/user_notification/domain/repository/user_notification_repository.dart';

@injectable
class MarkNotificationAsRead
    extends UseCase<MessageResponse, MarkNotificationAsReadParams> {
  final UserNotificationRepository repository;

  MarkNotificationAsRead(this.repository);

  @override
  call(MarkNotificationAsReadParams params) {
    return repository.markNotificationAsRead(params.notificationId);
  }
}

class MarkNotificationAsReadParams {
  final String notificationId;

  MarkNotificationAsReadParams({required this.notificationId});
}
