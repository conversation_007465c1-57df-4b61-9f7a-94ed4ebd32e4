import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/user_notification.dart';
import '../repository/user_notification_repository.dart';

@injectable
class GetAllNotification
    extends UseCase<Paginated<UserNotification>, PaginationParams> {
  final UserNotificationRepository repository;

  GetAllNotification(this.repository);

  @override
  call(PaginationParams params) {
    return repository.getAllNotification(page: params.page);
  }
}
