import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/user_notification.dart';
import '../repository/user_notification_repository.dart';

@injectable
class GetUnreadNotifications
    extends UseCase<Paginated<UserNotification>, PaginationParams> {
  final UserNotificationRepository repository;

  GetUnreadNotifications(this.repository);

  @override
  call(PaginationParams params) {
    return repository.getUnreadNotifications(page: params.page);
  }
}
