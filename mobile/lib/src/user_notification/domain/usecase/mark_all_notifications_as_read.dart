import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/user_notification_repository.dart';

@injectable
class MarkAllNotificationsAsRead extends UseCase<MessageResponse, NoParams> {
  final UserNotificationRepository repository;

  MarkAllNotificationsAsRead(this.repository);

  @override
  call(NoParams params) async {
    return repository.markAllNotificationsAsRead();
  }
}
