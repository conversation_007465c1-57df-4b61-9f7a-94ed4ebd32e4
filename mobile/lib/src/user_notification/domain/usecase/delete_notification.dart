import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../repository/user_notification_repository.dart';

@injectable
class DeleteNotification
    extends UseCase<MessageResponse, DeleteNotificationParams> {
  final UserNotificationRepository repository;

  DeleteNotification(this.repository);

  @override
  call(DeleteNotificationParams params) {
    return repository.deleteNotification(params.notificationId);
  }
}

class DeleteNotificationParams {
  final String notificationId;

  DeleteNotificationParams({required this.notificationId});
}
