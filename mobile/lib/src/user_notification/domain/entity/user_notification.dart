import 'package:equatable/equatable.dart';

class UserNotification extends Equatable {
  final String id;
  final String? type;
  final UserNotificationData data;
  final DateTime? readAt;
  final DateTime createdAt;

  const UserNotification({
    required this.id,
    required this.type,
    required this.createdAt,
    required this.readAt,
    required this.data,
  });

  @override
  List<Object?> get props => [id];
}

class UserNotificationData extends Equatable {
  final String? id;
  final String? title;
  final String? body;
  final String? status;

  const UserNotificationData({
    required this.id,
    required this.title,
    required this.body,
    required this.status,
  });

  @override
  List<Object?> get props => [id];
}
