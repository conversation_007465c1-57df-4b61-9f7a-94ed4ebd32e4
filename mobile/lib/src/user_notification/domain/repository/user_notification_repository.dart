import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';

import '../entity/user_notification.dart';

abstract class UserNotificationRepository {
  RepositoryResponse<Paginated<UserNotification>> getAllNotification({
    required int? page,
  });

  RepositoryResponse<Paginated<UserNotification>> getUnreadNotifications({
    required int? page,
  });

  RepositoryResponse<MessageResponse> markAllNotificationsAsRead();

  RepositoryResponse<MessageResponse> markNotificationAsRead(String id);

  RepositoryResponse<MessageResponse> deleteNotification(String id);

  RepositoryResponse<MessageResponse> deleteAllNotifications();
}
