import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';

import 'models/user_notification_model.dart';

abstract class UserNotificationRemoteDataSource {
  Future<Paginated<UserNotificationModel>> getAllNotification({
    required int? page,
  });

  Future<Paginated<UserNotificationModel>> getUnreadNotifications({
    required int? page,
  });

  Future<MessageResponse> markAllNotificationsAsRead();

  Future<MessageResponse> markNotificationAsRead(String id);

  Future<MessageResponse> deleteNotification(String id);

  Future<MessageResponse> deleteAllNotifications();
}

@Injectable(as: UserNotificationRemoteDataSource)
class UserNotificationRemoteDataSourceImpl
    implements UserNotificationRemoteDataSource {
  final Dio _httpClient;

  UserNotificationRemoteDataSourceImpl(this._httpClient);

  @override
  Future<MessageResponse> deleteAllNotifications() async {
    final response = await _httpClient.delete('/user/notifications');

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<MessageResponse> deleteNotification(String id) async {
    final response = await _httpClient.delete('/user/notifications/$id');

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<Paginated<UserNotificationModel>> getAllNotification({
    required int? page,
  }) async {
    final response = await _httpClient.get(
      '/user/notifications',
      queryParameters: {"page": page},
    );

    return Paginated.fromJson(
      response.data,
      (data) => UserNotificationModel.fromJson(data as Map<String, dynamic>),
    );
  }

  @override
  Future<Paginated<UserNotificationModel>> getUnreadNotifications({
    required int? page,
  }) async {
    final response = await _httpClient.get(
      '/user/notifications/unread',
      queryParameters: {"page": page},
    );

    return Paginated.fromJson(
      response.data,
      (data) => UserNotificationModel.fromJson(data as Map<String, dynamic>),
    );
  }

  @override
  Future<MessageResponse> markAllNotificationsAsRead() async {
    final response =
        await _httpClient.post('/user/notifications/mark-all-as-read');

    return MessageResponse.fromJson(response.data);
  }

  @override
  Future<MessageResponse> markNotificationAsRead(String id) async {
    final response =
        await _httpClient.put('/user/notifications/$id/mark-as-read');

    return MessageResponse.fromJson(response.data);
  }
}
