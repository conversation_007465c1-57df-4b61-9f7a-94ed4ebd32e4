// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserNotificationModel _$UserNotificationModelFromJson(
        Map<String, dynamic> json) =>
    UserNotificationModel(
      id: json['id'] as String,
      type: json['type'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      readAt: json['read_at'] == null
          ? null
          : DateTime.parse(json['read_at'] as String),
      data: UserNotificationDataModel.fromJson(
          json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserNotificationModelToJson(
        UserNotificationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'created_at': instance.createdAt.toIso8601String(),
      'read_at': instance.readAt?.toIso8601String(),
      'data': instance.data,
    };

UserNotificationDataModel _$UserNotificationDataModelFromJson(
        Map<String, dynamic> json) =>
    UserNotificationDataModel(
      id: json['id'] as String?,
      title: json['title'] as String?,
      body: json['body'] as String?,
      status: json['status'] as String?,
    );

Map<String, dynamic> _$UserNotificationDataModelToJson(
        UserNotificationDataModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'body': instance.body,
      'status': instance.status,
    };
