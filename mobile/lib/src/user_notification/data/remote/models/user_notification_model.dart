// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/user_notification/domain/entity/user_notification.dart';

part 'user_notification_model.g.dart';

@JsonSerializable()
class UserNotificationModel extends UserNotification {
  @override
  @Json<PERSON>ey(name: "created_at")
  final DateTime createdAt;

  @override
  @JsonKey(name: "read_at")
  final DateTime? readAt;

  @override
  final UserNotificationDataModel data;

  const UserNotificationModel({
    required super.id,
    required super.type,
    required this.createdAt,
    required this.readAt,
    required this.data,
  }) : super(createdAt: createdAt, readAt: readAt, data: data);

  factory UserNotificationModel.fromJson(Map<String, dynamic> json) =>
      _$UserNotificationModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserNotificationModelToJson(this);
}

@JsonSerializable()
class UserNotificationDataModel extends UserNotificationData {
  const UserNotificationDataModel({
    required super.id,
    required super.title,
    required super.body,
    required super.status,
  });

  factory UserNotificationDataModel.fromJson(Map<String, dynamic> json) =>
      _$UserNotificationDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserNotificationDataModelToJson(this);
}
