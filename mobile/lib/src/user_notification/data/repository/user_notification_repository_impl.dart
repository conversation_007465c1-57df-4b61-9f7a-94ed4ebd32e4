import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/user_notification/domain/entity/user_notification.dart';

import '../../domain/repository/user_notification_repository.dart';
import '../remote/user_notification_remote_datasource.dart';

@Injectable(as: UserNotificationRepository)
class UserNotificationRepositoryImpl extends UserNotificationRepository {
  final UserNotificationRemoteDataSource _remoteDataSource;

  UserNotificationRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<MessageResponse> deleteAllNotifications() {
    return requestHelper(() => _remoteDataSource.deleteAllNotifications());
  }

  @override
  RepositoryResponse<MessageResponse> deleteNotification(String id) {
    return requestHelper(() => _remoteDataSource.deleteNotification(id));
  }

  @override
  RepositoryResponse<Paginated<UserNotification>> getAllNotification({
    required int? page,
  }) {
    return requestHelper(
      () => _remoteDataSource.getAllNotification(page: page),
    );
  }

  @override
  RepositoryResponse<Paginated<UserNotification>> getUnreadNotifications({
    required int? page,
  }) {
    return requestHelper(
      () => _remoteDataSource.getUnreadNotifications(page: page),
    );
  }

  @override
  RepositoryResponse<MessageResponse> markAllNotificationsAsRead() {
    return requestHelper(() => _remoteDataSource.markAllNotificationsAsRead());
  }

  @override
  RepositoryResponse<MessageResponse> markNotificationAsRead(String id) {
    return requestHelper(() => _remoteDataSource.markNotificationAsRead(id));
  }
}
