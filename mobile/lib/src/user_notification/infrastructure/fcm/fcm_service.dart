import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

import '../awasome_notification/notification_service.dart';

Future<void> handleMessage(RemoteMessage message) async {
  if (message.notification != null) {
    NotificationService.show(
      title: message.notification!.title ?? '',
      body: message.notification!.body ?? '',
    );
  }
}

class FirebaseMessagingService {
  final _firebaseMessaging = FirebaseMessaging.instance;

  Future<void> initNotifications() async {
    await _firebaseMessaging.requestPermission();

    final token = await getToken();

    if (kDebugMode) {
      print("FirebaseMessaging token: $token");
    }

    // Background
    FirebaseMessaging.onBackgroundMessage(handleMessage);

    // Foreground
    FirebaseMessaging.onMessage.listen(handleMessage);
  }

  Future<String?> getToken() async {
    return await _firebaseMessaging.getToken();
  }
}
