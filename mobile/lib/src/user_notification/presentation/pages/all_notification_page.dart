import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/user_notification/domain/entity/user_notification.dart';

import '../blocs/delete_all_notifications/delete_all_notifications_cubit.dart';
import '../blocs/delete_notification/delete_notification_cubit.dart';
import '../blocs/get_all_notification/get_all_notification_bloc.dart';
import '../blocs/mark_notification_as_read/mark_notification_as_read_cubit.dart';
import '../widgets/delete_all_notification_popup.dart';
import '../widgets/notification_list_show.dart';

class AllNotificationPage extends StatelessWidget {
  const AllNotificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) =>
              getIt<GetAllNotificationBloc>()..add(PaginatedItemFetched()),
        ),
        BlocProvider(
          create: (_) => getIt<DeleteNotificationCubit>(),
        ),
        BlocProvider(
          create: (_) => getIt<DeleteAllNotificationsCubit>(),
        ),
        BlocProvider(
          create: (_) => getIt<MarkNotificationAsReadCubit>(),
        ),
      ],
      child: const _AllNotificationPageContent(),
    );
  }
}

class _AllNotificationPageContent extends StatefulWidget {
  const _AllNotificationPageContent();

  @override
  State<_AllNotificationPageContent> createState() =>
      _AllNotificationPageContentState();
}

class _AllNotificationPageContentState
    extends State<_AllNotificationPageContent> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Notifications"),
        actions: const [
          DeleteAllNotificationPopup(),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            context.read<GetAllNotificationBloc>().add(
                  PaginatedItemFetched(refresh: true),
                );
          },
          child: SingleChildScrollView(
            controller: _scrollController,
            physics: const ScrollPhysics(),
            padding: bodyPadding,
            child: PaginatedWidget<GetAllNotificationBloc, UserNotification>(
              scrollController: _scrollController,
              widgetEmpty: const EmptyList(
                message: "Pas de notification\npour le moment.",
              ),
              render: (_, __, state) {
                return NotificationListShow(state: state);
              } as WidgetRender<UserNotification>,
            ),
          ),
        ),
      ),
    );
  }
}
