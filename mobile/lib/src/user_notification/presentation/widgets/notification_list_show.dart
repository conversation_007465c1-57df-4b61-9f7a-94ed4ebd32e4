import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/user_notification/domain/entity/user_notification.dart';

import '../blocs/delete_notification/delete_notification_cubit.dart';
import '../blocs/get_all_notification/get_all_notification_bloc.dart';
import '../blocs/mark_notification_as_read/mark_notification_as_read_cubit.dart';

class NotificationListShow extends StatelessWidget {
  final PaginatedItemState<UserNotification> state;

  const NotificationListShow({
    super.key,
    required this.state,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: state.itemCountWithLoader,
      itemBuilder: (context, index) {
        if (index >= state.items.length) {
          const Center(child: GFLoader());
        }

        final notification = state.items[index];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Show date
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  LocalTimeago(
                    builder: (_, value) => Text(
                      value,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    date: notification.createdAt,
                  ),

                  // Show icon if notification is not read
                  if (notification.readAt == null)
                    Icon(
                      Icons.circle_notifications_sharp,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                ],
              ),
            ),

            // Some space
            const SizedBox(height: 5),

            AccordionItem(
              key: ValueKey(notification.id),
              notification: notification,
            ),

            // Show divider
            const GreyDivider(),

            // Show space
            columnSizedBox,

            // Show delete message
            BlocListener<DeleteNotificationCubit, DeleteNotificationState>(
              listener: (_, state) {
                switch (state) {
                  case DeleteNotificationLoading():
                    showToast(
                      context,
                      type: ToastType.info,
                      message: "Suppression..",
                    );
                    break;

                  case DeleteNotificationError(error: String message):
                    showToast(
                      context,
                      type: ToastType.error,
                      message: message,
                    );
                    break;

                  case DeleteNotificationSuccess(message: String message):
                    showToast(
                      context,
                      type: ToastType.success,
                      message: message,
                    );

                    // Refresh the list after deletion
                    context
                        .read<GetAllNotificationBloc>()
                        .add(PaginatedItemFetched(refresh: true));
                    break;
                  default:
                }
              },
              child: const SizedBox.shrink(),
            ),
          ],
        );
      },
    );
  }
}

class AccordionItem extends StatefulWidget {
  final UserNotification notification;
  const AccordionItem({super.key, required this.notification});

  @override
  State<AccordionItem> createState() => AccordionStateItem();
}

class AccordionStateItem extends State<AccordionItem> {
  bool _collapsed = false;

  @override
  void initState() {
    if (widget.notification.readAt == null) {
      context
          .read<MarkNotificationAsReadCubit>()
          .markNotificationAsRead(widget.notification.id);
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(widget.notification.id),
      enabled: !_collapsed,
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (_) => _onDelete(context),
            backgroundColor: GFColors.DANGER,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Supprimer',
          ),
        ],
      ),
      child: GFAccordion(
        margin: const EdgeInsets.all(0),
        titleBorderRadius: BorderRadius.circular(10),
        titleChild: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10.0),
          child: Text(
            widget.notification.data.title ?? '',
            style: const TextStyle(fontSize: 18),
          ),
        ),
        contentChild: Text(widget.notification.data.body ?? ''),
        onToggleCollapsed: (value) => {
          setState(() {
            _collapsed = value;
          }),
        },
      ),
    );
  }

  void _onDelete(BuildContext context) async {
    if (mounted) {
      context
          .read<DeleteNotificationCubit>()
          .deleteNotification(widget.notification.id);
    }
  }
}
