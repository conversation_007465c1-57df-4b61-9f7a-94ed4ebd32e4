import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/user_notification/domain/entity/user_notification.dart';

import '../blocs/delete_notification/delete_notification_cubit.dart';
import '../blocs/get_all_notification/get_all_notification_bloc.dart';
import '../blocs/mark_notification_as_read/mark_notification_as_read_cubit.dart';

class NotificationListShow extends StatelessWidget {
  final PaginatedItemState<UserNotification> state;

  const NotificationListShow({
    super.key,
    required this.state,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<DeleteNotificationCubit, DeleteNotificationState>(
      listener: (_, state) {
        switch (state) {
          case DeleteNotificationLoading():
            showToast(
              context,
              type: ToastType.info,
              message: "Suppression..",
            );
            break;

          case DeleteNotificationError(error: String message):
            showToast(
              context,
              type: ToastType.error,
              message: message,
            );
            break;

          case DeleteNotificationSuccess(message: String message):
            showToast(
              context,
              type: ToastType.success,
              message: message,
            );

            // Refresh the list after deletion
            context
                .read<GetAllNotificationBloc>()
                .add(PaginatedItemFetched(refresh: true));
            break;
          default:
        }
      },
      child: ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: state.itemCountWithLoader,
        itemBuilder: (context, index) {
          if (index >= state.items.length) {
            return const Center(child: GFLoader());
          }

          final notification = state.items[index];

          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: NotificationItem(
              key: ValueKey(notification.id),
              notification: notification,
            ),
          );
        },
      ),
    );
  }
}

class NotificationItem extends StatefulWidget {
  final UserNotification notification;
  const NotificationItem({super.key, required this.notification});

  @override
  State<NotificationItem> createState() => _NotificationItemState();
}

class _NotificationItemState extends State<NotificationItem> {
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    if (widget.notification.readAt == null) {
      context
          .read<MarkNotificationAsReadCubit>()
          .markNotificationAsRead(widget.notification.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isUnread = widget.notification.readAt == null;

    return Slidable(
      key: ValueKey(widget.notification.id),
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        extentRatio: 0.25,
        children: [
          SlidableAction(
            onPressed: (_) => _onDelete(context),
            backgroundColor: AppTheme.primary,
            foregroundColor: Colors.white,
            icon: Icons.delete_outline,
            label: 'Supprimer',
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(16),
              bottomRight: Radius.circular(16),
            ),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _isExpanded = !_isExpanded;
          });
        },
        borderRadius: BorderRadius.circular(cardRadius),
        child: Container(
          padding: cardSpacing,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(cardRadius),
            border: Border.all(
              color: isUnread
                  ? const Color(0xFF3B82F6).withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.08),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Notification Icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: isUnread
                          ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                          : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      isUnread
                          ? Icons.notifications_active
                          : Icons.notifications_outlined,
                      color:
                          isUnread ? const Color(0xFF3B82F6) : Colors.grey[600],
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Title and Time
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.notification.data.title ?? 'Notification',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight:
                                isUnread ? FontWeight.w600 : FontWeight.w500,
                            color: const Color(0xFF1F2937),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        LocalTimeago(
                          date: widget.notification.createdAt,
                          builder: (_, value) => Text(
                            value,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Unread Indicator + Expand Icon
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isUnread)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Color(0xFF3B82F6),
                            shape: BoxShape.circle,
                          ),
                        ),
                      const SizedBox(width: 8),
                      Icon(
                        _isExpanded ? Icons.expand_less : Icons.expand_more,
                        color: Colors.grey[400],
                        size: 20,
                      ),
                    ],
                  ),
                ],
              ),

              // Body (Expandable)
              if (_isExpanded) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    widget.notification.data.body ?? 'Aucun contenu',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _onDelete(BuildContext context) async {
    if (mounted) {
      context
          .read<DeleteNotificationCubit>()
          .deleteNotification(widget.notification.id);
    }
  }
}
