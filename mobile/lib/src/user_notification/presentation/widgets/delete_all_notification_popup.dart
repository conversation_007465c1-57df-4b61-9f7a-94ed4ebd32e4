import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';

import '../blocs/delete_all_notifications/delete_all_notifications_cubit.dart';
import '../blocs/get_all_notification/get_all_notification_bloc.dart';

class DeleteAllNotificationPopup extends StatefulWidget {
  const DeleteAllNotificationPopup({super.key});

  @override
  State<DeleteAllNotificationPopup> createState() =>
      _DeleteAllNotificationPopupState();
}

class _DeleteAllNotificationPopupState
    extends State<DeleteAllNotificationPopup> {
  @override
  Widget build(BuildContext context) {
    return BlocListener<DeleteAllNotificationsCubit,
        DeleteAllNotificationsState>(
      listener: (_, state) {
        switch (state) {
          case DeleteAllNotificationsLoading():
            showToast(
              context,
              type: ToastType.info,
              message: "Suppression..",
            );
            break;

          case DeleteAllNotificationsError(error: String message):
            showToast(
              context,
              type: ToastType.error,
              message: message,
            );
            break;

          case DeleteAllNotificationsSuccess(message: String message):
            showToast(
              context,
              type: ToastType.success,
              message: message,
            );

            // Refresh the list after deletion
            context
                .read<GetAllNotificationBloc>()
                .add(PaginatedItemFetched(refresh: true));
            break;
          default:
        }
      },
      child: IconButton(
        onPressed: () => _showDeleteAllDialog(context),
        icon: const Icon(
          Icons.delete_sweep_outlined,
          color: Colors.white,
        ),
        tooltip: 'Supprimer toutes les notifications',
      ),
    );
  }

  void _showDeleteAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(cardRadius),
          ),
          title: const Row(
            children: [
              Icon(
                Icons.warning_outlined,
                color: Color(0xFFEF4444),
                size: 24,
              ),
              SizedBox(width: 12),
              Text(
                'Confirmer la suppression',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: const Text(
            'Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.',
            style: TextStyle(
              fontSize: 14,
              height: 1.4,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Annuler',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _onDelete(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              child: const Text(
                'Supprimer tout',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _onDelete(BuildContext context) async {
    if (mounted) {
      context.read<DeleteAllNotificationsCubit>().deleteAllNotifications();
    }
  }
}
