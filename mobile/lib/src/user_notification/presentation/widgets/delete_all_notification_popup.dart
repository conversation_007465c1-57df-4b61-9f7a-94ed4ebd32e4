import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';

import '../blocs/delete_all_notifications/delete_all_notifications_cubit.dart';
import '../blocs/get_all_notification/get_all_notification_bloc.dart';

class DeleteAllNotificationPopup extends StatefulWidget {
  const DeleteAllNotificationPopup({super.key});

  @override
  State<DeleteAllNotificationPopup> createState() =>
      _DeleteAllNotificationPopupState();
}

class _DeleteAllNotificationPopupState
    extends State<DeleteAllNotificationPopup> {
  @override
  Widget build(BuildContext context) {
    return BlocListener<DeleteAllNotificationsCubit,
        DeleteAllNotificationsState>(
      listener: (_, state) {
        switch (state) {
          case DeleteAllNotificationsLoading():
            showToast(
              context,
              type: ToastType.info,
              message: "Suppression..",
            );
            break;

          case DeleteAllNotificationsError(error: String message):
            showToast(
              context,
              type: ToastType.error,
              message: message,
            );
            break;

          case DeleteAllNotificationsSuccess(message: String message):
            showToast(
              context,
              type: ToastType.success,
              message: message,
            );

            // Refresh the list after deletion
            context
                .read<GetAllNotificationBloc>()
                .add(PaginatedItemFetched(refresh: true));
            break;
          default:
        }
      },
      child: PopupMenuButton<String>(
        itemBuilder: (BuildContext context) => [
          PopupMenuItem(
            onTap: () => _onDelete(context),
            child: const Text('Tous supprimer'),
          ),
        ],
      ),
    );
  }

  void _onDelete(BuildContext context) async {
    final confirmed = await pressConfirm(context);

    if (confirmed && mounted) {
      // ignore: use_build_context_synchronously
      context.read<DeleteAllNotificationsCubit>().deleteAllNotifications();
    }
  }
}
