import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/user_notification/domain/usecase/mark_notification_as_read.dart';

part 'mark_notification_as_read_state.dart';

@injectable
class MarkNotificationAsReadCubit extends Cubit<MarkNotificationAsReadState> {
  final MarkNotificationAsRead _markNotificationAsRead;

  MarkNotificationAsReadCubit(this._markNotificationAsRead)
      : super(MarkNotificationAsReadInitial());

  void markNotificationAsRead(String notificationId) async {
    await _markNotificationAsRead(
      MarkNotificationAsReadParams(notificationId: notificationId),
    );
  }
}
