import 'package:injectable/injectable.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/user_notification/domain/entity/user_notification.dart';
import 'package:s3g/src/user_notification/domain/usecase/get_unread_notifications.dart';

@injectable
class GetUnreadNotificationsBloc extends PaginatedItemBloc<UserNotification> {
  final GetUnreadNotifications _getUnreadNotifications;

  GetUnreadNotificationsBloc(this._getUnreadNotifications) : super();

  @override
  callUseCase(PaginationParams params) => _getUnreadNotifications(params);
}
