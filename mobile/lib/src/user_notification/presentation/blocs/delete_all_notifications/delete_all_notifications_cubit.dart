import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/user_notification/domain/usecase/delete_all_notifications.dart';

part 'delete_all_notifications_state.dart';

@injectable
class DeleteAllNotificationsCubit extends Cubit<DeleteAllNotificationsState> {
  final DeleteAllNotifications _deleteAllNotifications;

  DeleteAllNotificationsCubit(this._deleteAllNotifications)
      : super(DeleteAllNotificationsInitial());

  void deleteAllNotifications() async {
    emit(DeleteAllNotificationsLoading());

    final result = await _deleteAllNotifications(NoParams());

    result.fold(
      (l) => emit(DeleteAllNotificationsError(error: l.message)),
      (r) => emit(DeleteAllNotificationsSuccess(message: r.message)),
    );
  }
}
