part of 'delete_all_notifications_cubit.dart';

@immutable
sealed class DeleteAllNotificationsState {}

final class DeleteAllNotificationsInitial extends DeleteAllNotificationsState {}

final class DeleteAllNotificationsLoading extends DeleteAllNotificationsState {}

final class Delete<PERSON><PERSON>NotificationsSuc<PERSON> extends DeleteAllNotificationsState {
  final String message;

  DeleteAllNotificationsSuccess({required this.message});
}

final class DeleteAllNotificationsError extends DeleteAllNotificationsState {
  final String error;

  DeleteAllNotificationsError({required this.error});
}
