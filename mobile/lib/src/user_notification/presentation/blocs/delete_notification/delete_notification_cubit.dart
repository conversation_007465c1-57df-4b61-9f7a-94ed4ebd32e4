import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/user_notification/domain/usecase/delete_notification.dart';

part 'delete_notification_state.dart';

@injectable
class DeleteNotificationCubit extends Cubit<DeleteNotificationState> {
  final DeleteNotification _deleteNotification;

  DeleteNotificationCubit(this._deleteNotification)
      : super(DeleteNotificationInitial());

  Future<void> deleteNotification(String notificationId) async {
    emit(DeleteNotificationLoading());

    final result = await _deleteNotification(
      DeleteNotificationParams(notificationId: notificationId),
    );

    result.fold(
      (failure) => emit(DeleteNotificationError(error: failure.message)),
      (success) => emit(DeleteNotificationSuccess(message: success.message)),
    );
  }
}
