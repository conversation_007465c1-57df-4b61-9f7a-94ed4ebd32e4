part of 'delete_notification_cubit.dart';

@immutable
sealed class DeleteNotificationState {}

final class DeleteNotificationInitial extends DeleteNotificationState {}

final class DeleteNotificationLoading extends DeleteNotificationState {}

final class DeleteNotificationSuccess extends DeleteNotificationState {
  final String message;

  DeleteNotificationSuccess({required this.message});
}

final class DeleteNotificationError extends DeleteNotificationState {
  final String error;

  DeleteNotificationError({required this.error});
}
