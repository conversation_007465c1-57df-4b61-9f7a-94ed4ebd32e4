import 'package:injectable/injectable.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/user_notification/domain/entity/user_notification.dart';
import 'package:s3g/src/user_notification/domain/usecase/get_all_notification.dart';

@injectable
class GetAllNotificationBloc extends PaginatedItemBloc<UserNotification> {
  final GetAllNotification _getAllNotification;

  GetAllNotificationBloc(this._getAllNotification) : super();

  @override
  callUseCase(PaginationParams params) => _getAllNotification(params);
}
