import 'package:device_info_plus/device_info_plus.dart';
import 'package:injectable/injectable.dart';
import 'dart:io' show Platform;

abstract class DeviceInfo {
  Future<String> deviceName();
}

@Injectable(as: DeviceInfo)
class DeviceInfoImpl extends DeviceInfo {
  final DeviceInfoPlugin _deviceInfoPlugin;

  DeviceInfoImpl({required DeviceInfoPlugin plugin})
      : _deviceInfoPlugin = plugin;

  @override
  Future<String> deviceName() async {
    if (Platform.isIOS) {
      final androidInfo = await _deviceInfoPlugin.androidInfo;
      return androidInfo.model;
    } else if (Platform.isIOS) {
      final iosInfo = await _deviceInfoPlugin.iosInfo;
      return iosInfo.name;
    } else if (Platform.isLinux) {
      final linuxInfo = await _deviceInfoPlugin.linuxInfo;
      return linuxInfo.name;
    } else if (Platform.isWindows) {
      final windowsInfo = await _deviceInfoPlugin.windowsInfo;
      return windowsInfo.computerName;
    } else if (Platform.isMacOS) {
      final macInfo = await _deviceInfoPlugin.macOsInfo;
      return macInfo.model;
    } else {
      return 'Unknown';
    }
  }
}
