import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:s3g/core/errors/exceptions.dart';
import 'package:s3g/core/errors/failures.dart';

Future<Either<Failure, T>> requestHelper<T>(
  Future<T> Function() handler,
) async {
  try {
    final result = await handler();

    return Right(result);
  } on ServerException catch (e) {
    debugPrint("ServerException helper error: ${e.toString()}");

    return Left(ServerFailure(e.message));
  } on SocketException {
    debugPrint("SocketException helper error");

    return const Left(
      ConnectionFailure('Pas de connexion Internet'),
    );
  } on DioException catch (e) {
    debugPrint("DioException helper error: ${e.toString()}");

    if (e.type == DioExceptionType.connectionError) {
      return const Left(ServerFailure("Pas de connexion Internet"));
    }

    if (e.response?.statusCode == 404) {
      return const Left(ServerFailure("Ressource introuvable"));
    }

    return Left(
      ServerFailure(
        e.response?.data['message'].toString() ??
            "Une erreur s'est produite. Veuillez réessayer",
      ),
    );
  } catch (e) {
    debugPrint("Request helper error: ${e.toString()}");

    return const Left(
      ServerFailure(
        "Une erreur s'est produite. Veuillez réessayer",
      ),
    );
  }
}
