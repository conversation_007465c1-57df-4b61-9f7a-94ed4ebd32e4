{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:4843187991762130878", "lastPropertyId": "11:8755141269492670503", "name": "UserLocalModel", "properties": [{"id": "2:8150247937050990135", "name": "id", "type": 9, "flags": 2080, "indexId": "1:6696137614632170115"}, {"id": "3:4867154126574018817", "name": "name", "type": 9}, {"id": "4:7808261147011250497", "name": "role", "type": 9}, {"id": "5:5861495493908320549", "name": "phone", "type": 9}, {"id": "6:4256530412587493788", "name": "email", "type": 9}, {"id": "7:4494853835445859077", "name": "description", "type": 9}, {"id": "8:330474981545105513", "name": "createdAt", "type": 10}, {"id": "9:5940803811252183204", "name": "updatedAt", "type": 10}, {"id": "10:5089396876425374590", "name": "oid", "type": 6, "flags": 1}], "relations": []}], "lastEntityId": "1:4843187991762130878", "lastIndexId": "1:6696137614632170115", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [2732646462392475608, 8755141269492670503], "retiredRelationUids": [], "version": 1}