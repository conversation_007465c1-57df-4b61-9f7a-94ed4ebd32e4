// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import '../../src/authentication/data/local/models/user_local_model.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 4843187991762130878),
      name: 'UserLocalModel',
      lastPropertyId: const obx_int.IdUid(11, 8755141269492670503),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 8150247937050990135),
            name: 'id',
            type: 9,
            flags: 2080,
            indexId: const obx_int.IdUid(1, 6696137614632170115)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 4867154126574018817),
            name: 'name',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 7808261147011250497),
            name: 'role',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 5861495493908320549),
            name: 'phone',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 4256530412587493788),
            name: 'email',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 4494853835445859077),
            name: 'description',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 330474981545105513),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 5940803811252183204),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 5089396876425374590),
            name: 'oid',
            type: 6,
            flags: 1)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(1, 4843187991762130878),
      lastIndexId: const obx_int.IdUid(1, 6696137614632170115),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [],
      retiredPropertyUids: const [2732646462392475608, 8755141269492670503],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    UserLocalModel: obx_int.EntityDefinition<UserLocalModel>(
        model: _entities[0],
        toOneRelations: (UserLocalModel object) => [],
        toManyRelations: (UserLocalModel object) => {},
        getId: (UserLocalModel object) => object.oid,
        setId: (UserLocalModel object, int id) {
          object.oid = id;
        },
        objectToFB: (UserLocalModel object, fb.Builder fbb) {
          final idOffset = fbb.writeString(object.id);
          final nameOffset = fbb.writeString(object.name);
          final roleOffset = fbb.writeString(object.role);
          final phoneOffset = fbb.writeString(object.phone);
          final emailOffset =
              object.email == null ? null : fbb.writeString(object.email!);
          final descriptionOffset = object.description == null
              ? null
              : fbb.writeString(object.description!);
          fbb.startTable(12);
          fbb.addOffset(1, idOffset);
          fbb.addOffset(2, nameOffset);
          fbb.addOffset(3, roleOffset);
          fbb.addOffset(4, phoneOffset);
          fbb.addOffset(5, emailOffset);
          fbb.addOffset(6, descriptionOffset);
          fbb.addInt64(7, object.createdAt.millisecondsSinceEpoch);
          fbb.addInt64(8, object.updatedAt.millisecondsSinceEpoch);
          fbb.addInt64(9, object.oid);
          fbb.finish(fbb.endTable());
          return object.oid;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final oidParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 22, 0);
          final idParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 6, '');
          final nameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 8, '');
          final roleParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 10, '');
          final phoneParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 12, '');
          final emailParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 14);
          final descriptionParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 16);
          final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 18, 0));
          final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 20, 0));
          final object = UserLocalModel(
              oid: oidParam,
              id: idParam,
              name: nameParam,
              role: roleParam,
              phone: phoneParam,
              email: emailParam,
              description: descriptionParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam);

          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [UserLocalModel] entity fields to define ObjectBox queries.
class UserLocalModel_ {
  /// See [UserLocalModel.id].
  static final id =
      obx.QueryStringProperty<UserLocalModel>(_entities[0].properties[0]);

  /// See [UserLocalModel.name].
  static final name =
      obx.QueryStringProperty<UserLocalModel>(_entities[0].properties[1]);

  /// See [UserLocalModel.role].
  static final role =
      obx.QueryStringProperty<UserLocalModel>(_entities[0].properties[2]);

  /// See [UserLocalModel.phone].
  static final phone =
      obx.QueryStringProperty<UserLocalModel>(_entities[0].properties[3]);

  /// See [UserLocalModel.email].
  static final email =
      obx.QueryStringProperty<UserLocalModel>(_entities[0].properties[4]);

  /// See [UserLocalModel.description].
  static final description =
      obx.QueryStringProperty<UserLocalModel>(_entities[0].properties[5]);

  /// See [UserLocalModel.createdAt].
  static final createdAt =
      obx.QueryDateProperty<UserLocalModel>(_entities[0].properties[6]);

  /// See [UserLocalModel.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<UserLocalModel>(_entities[0].properties[7]);

  /// See [UserLocalModel.oid].
  static final oid =
      obx.QueryIntegerProperty<UserLocalModel>(_entities[0].properties[8]);
}
