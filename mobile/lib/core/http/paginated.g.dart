// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paginated.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Paginated<T> _$PaginatedFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    Paginated<T>(
      data: (json['data'] as List<dynamic>).map(fromJsonT).toList(),
      meta: PaginatedMeta.fromJson(json['meta'] as Map<String, dynamic>),
      links: PaginatedLinks.fromJson(json['links'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PaginatedToJson<T>(
  Paginated<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'data': instance.data.map(toJsonT).toList(),
      'meta': instance.meta,
      'links': instance.links,
    };

PaginatedLinks _$PaginatedLinksFromJson(Map<String, dynamic> json) =>
    PaginatedLinks(
      first: json['first'] as String,
      last: json['last'] as String,
      next: json['next'] as String?,
      prev: json['prev'] as String?,
    );

Map<String, dynamic> _$PaginatedLinksToJson(PaginatedLinks instance) =>
    <String, dynamic>{
      'first': instance.first,
      'last': instance.last,
      'next': instance.next,
      'prev': instance.prev,
    };

PaginatedMeta _$PaginatedMetaFromJson(Map<String, dynamic> json) =>
    PaginatedMeta(
      currentPage: (json['current_page'] as num?)?.toInt(),
      from: (json['from'] as num?)?.toInt(),
      lastPage: (json['last_page'] as num?)?.toInt(),
      path: json['path'] as String,
      perPage: (json['per_page'] as num).toInt(),
      to: (json['to'] as num?)?.toInt(),
      total: (json['total'] as num).toInt(),
      links: (json['links'] as List<dynamic>)
          .map((e) => PaginatedMetaLink.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PaginatedMetaToJson(PaginatedMeta instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'from': instance.from,
      'last_page': instance.lastPage,
      'path': instance.path,
      'per_page': instance.perPage,
      'to': instance.to,
      'total': instance.total,
      'links': instance.links,
    };

PaginatedMetaLink _$PaginatedMetaLinkFromJson(Map<String, dynamic> json) =>
    PaginatedMetaLink(
      active: json['active'] as bool,
      label: json['label'] as String,
      url: json['url'] as String?,
    );

Map<String, dynamic> _$PaginatedMetaLinkToJson(PaginatedMetaLink instance) =>
    <String, dynamic>{
      'active': instance.active,
      'label': instance.label,
      'url': instance.url,
    };
