import 'package:dio/dio.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/dio/dio_api_interceptor.dart';
import 'package:s3g/core/http/dio/dio_cache_interceptor.dart';
import 'package:s3g/core/http/dio/dio_config.dart';
import 'package:s3g/src/authentication/authentication.dart';

@module
abstract class DioRegisterModule {
  @Named("DioOptions")
  BaseOptions get dioOptions => dioDefaultOptions;

  @singleton
  @preResolve
  Future<Dio> getAuthorizedDioClient(
    @Named('DioOptions') BaseOptions options,
    TokenRepository tokenRepository,
  ) async {
    final cacheOptions = await initCacheOptions();
    final dioClient = Dio(options);

    dioClient.interceptors.addAll([
      AuthorizedRequestInterceptor(tokenRepository),
      DioCacheInterceptor(options: cacheOptions),
    ]);

    return dioClient;
  }

  @Named('Unauthorized')
  @singleton
  @preResolve
  Future<Dio> getUnauthorizedDioClient(
    @Named('DioOptions') BaseOptions options,
  ) async {
    final cacheOptions = await initCacheOptions();
    final dioClient = Dio(options);

    dioClient.interceptors.addAll([
      UnauthorizedRequestInterceptor(),
      DioCacheInterceptor(options: cacheOptions),
    ]);

    return dioClient;
  }
}
