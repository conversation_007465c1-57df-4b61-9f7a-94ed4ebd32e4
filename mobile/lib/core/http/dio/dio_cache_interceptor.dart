// Global options
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:dio_cache_interceptor_file_store/dio_cache_interceptor_file_store.dart';
import 'package:path_provider/path_provider.dart';

Future<CacheOptions> initCacheOptions() async {
  final tmpDir = await getTemporaryDirectory();
  final CacheStore cacheStore = FileCacheStore(tmpDir.path);

  return CacheOptions(
    store: cacheStore,
    policy: CachePolicy.refreshForceCache,
    hitCacheOnErrorExcept: [503, 504],
    maxStale: const Duration(days: 7),
    priority: CachePriority.normal,
    cipher: null,
    keyBuilder: CacheOptions.defaultCacheKeyBuilder,
    allowPostMethod: false,
  );
}
