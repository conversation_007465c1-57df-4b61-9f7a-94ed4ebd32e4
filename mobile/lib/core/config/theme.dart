import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppTheme {
  static const primary = Color(0xFFEF3A4F);

  static const AppBarTheme _appBarTheme = AppBarTheme(
    backgroundColor: Color(0xFF18181B),
    systemOverlayStyle: SystemUiOverlayStyle.light,
    toolbarTextStyle: TextStyle(
      color: Colors.white,
    ),
    iconTheme: IconThemeData(color: Colors.white),
    elevation: 1,
    titleTextStyle: TextStyle(
      color: Colors.white,
      fontSize: 20,
    ),
  );

  static ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    appBarTheme: _appBarTheme,
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(),
    useMaterial3: true,
    colorScheme: const ColorScheme.light(
      surface: Color.fromARGB(255, 245, 243, 243),
      primary: primary,
    ),
  );
}
