import 'package:flutter/services.dart';

/// Text input configuration constants for consistent behavior across the app
class TextInputConfig {
  // Private constructor to prevent instantiation
  TextInputConfig._();

  /// Configuration for text fields that should have dictionary suggestions
  /// (names, descriptions, multi-line text)
  static const Map<String, dynamic> withSuggestions = {
    'autocorrect': true,
    'enableSuggestions': true,
    'textCapitalization': TextCapitalization.sentences,
  };

  /// Configuration for name fields
  static const Map<String, dynamic> forNames = {
    'autocorrect': true,
    'enableSuggestions': true,
    'textCapitalization': TextCapitalization.words,
  };

  /// Configuration for fields that should NOT have suggestions
  /// (phone numbers, passwords, codes, numbers)
  static const Map<String, dynamic> withoutSuggestions = {
    'autocorrect': false,
    'enableSuggestions': false,
  };

  /// Configuration for code/ID fields
  static const Map<String, dynamic> forCodes = {
    'autocorrect': false,
    'enableSuggestions': false,
    'textCapitalization': TextCapitalization.characters,
  };

  /// Configuration for email fields
  static const Map<String, dynamic> forEmail = {
    'autocorrect': false,
    'enableSuggestions': true,
  };

  /// Configuration for password fields
  static const Map<String, dynamic> forPassword = {
    'autocorrect': false,
    'enableSuggestions': false,
  };

  /// Configuration for phone number fields
  static const Map<String, dynamic> forPhone = {
    'autocorrect': false,
    'enableSuggestions': false,
  };

  /// Configuration for numeric fields
  static const Map<String, dynamic> forNumbers = {
    'autocorrect': false,
    'enableSuggestions': false,
  };
}