import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

@module
abstract class RegisterModule {
  @lazySingleton
  InternetConnection internetConnect() => InternetConnection();

  @singleton
  @factoryMethod
  FlutterSecureStorage createSecureStorage() {
    return const FlutterSecureStorage();
  }

  @singleton
  @factoryMethod
  DeviceInfoPlugin deviceInfoPlus() {
    return DeviceInfoPlugin();
  }
}
