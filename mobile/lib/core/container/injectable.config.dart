// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:device_info_plus/device_info_plus.dart' as _i6;
import 'package:dio/dio.dart' as _i12;
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i5;
import 'package:get_it/get_it.dart' as _i1;
import 'package:injectable/injectable.dart' as _i2;
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart'
    as _i8;

import '../../common/blocs/inline_form/inline_form_cubit.dart' as _i4;
import '../../src/advice/advice.dart' as _i135;
import '../../src/advice/data/remote/advice_remote_datasource.dart' as _i16;
import '../../src/advice/data/repositories/advice_repository_impl.dart' as _i30;
import '../../src/advice/domain/repository/advice_repository.dart' as _i29;
import '../../src/advice/domain/usecase/get_advice_detail.dart' as _i83;
import '../../src/advice/domain/usecase/get_advices.dart' as _i84;
import '../../src/advice/presentation/bloc/advices/advices_bloc.dart' as _i134;
import '../../src/advice/presentation/bloc/get_advice_detail/get_advice_detail_cubit.dart'
    as _i139;
import '../../src/authentication/authentication.dart' as _i17;
import '../../src/authentication/data/local/authentication_local_datasource.dart'
    as _i9;
import '../../src/authentication/data/local/token_storage.dart' as _i11;
import '../../src/authentication/data/remote/authentication_remote_datasource.dart'
    as _i50;
import '../../src/authentication/data/remote/fcm_token_remote_datasource.dart'
    as _i27;
import '../../src/authentication/data/repositories/authentication_repository_impl.dart'
    as _i78;
import '../../src/authentication/data/repositories/fcm_token_repository_impl.dart'
    as _i49;
import '../../src/authentication/data/repositories/token_repository_impl.dart'
    as _i15;
import '../../src/authentication/domain/repository/authentication_repository.dart'
    as _i77;
import '../../src/authentication/domain/repository/fcm_token_repository.dart'
    as _i48;
import '../../src/authentication/domain/repository/token_repository.dart'
    as _i14;
import '../../src/authentication/domain/usecases/get_user.dart' as _i112;
import '../../src/authentication/domain/usecases/set_fcm_token.dart' as _i115;
import '../../src/authentication/domain/usecases/user_forgot_fassword.dart'
    as _i111;
import '../../src/authentication/domain/usecases/user_login.dart' as _i110;
import '../../src/authentication/domain/usecases/user_reset_password.dart'
    as _i108;
import '../../src/authentication/domain/usecases/user_signout.dart' as _i109;
import '../../src/authentication/presentation/bloc/authenticated/authenticated_cubit.dart'
    as _i150;
import '../../src/authentication/presentation/bloc/forgot_password/forgot_password_cubit.dart'
    as _i132;
import '../../src/authentication/presentation/bloc/login/login_cubit.dart'
    as _i133;
import '../../src/authentication/presentation/bloc/reset_password/reset_password_cubit.dart'
    as _i113;
import '../../src/authentication/presentation/bloc/sign_out/sign_out_cubit.dart'
    as _i142;
import '../../src/companion/companion/data/remote/companion_remote_datasource.dart'
    as _i58;
import '../../src/companion/companion/data/repository/companion_repository_impl.dart'
    as _i107;
import '../../src/companion/companion/domain/repository/companion_repository.dart'
    as _i106;
import '../../src/companion/companion/domain/usecase/get_companion.dart'
    as _i126;
import '../../src/companion/companion/domain/usecase/get_companion_list.dart'
    as _i127;
import '../../src/companion/companion/presentation/blocs/companion_show/companion_show_cubit.dart'
    as _i145;
import '../../src/companion/companion/presentation/blocs/companions_list/companions_list_bloc.dart'
    as _i131;
import '../../src/companion/followup/data/remote/followup_remote_datasource.dart'
    as _i61;
import '../../src/companion/followup/data/repository/followup_repository_impl.dart'
    as _i63;
import '../../src/companion/followup/domain/repository/followup_repository.dart'
    as _i62;
import '../../src/companion/followup/domain/usecase/create_followup.dart'
    as _i94;
import '../../src/companion/followup/domain/usecase/delete_followup.dart'
    as _i118;
import '../../src/companion/followup/domain/usecase/get_followup.dart' as _i97;
import '../../src/companion/followup/domain/usecase/get_followup_list.dart'
    as _i98;
import '../../src/companion/followup/domain/usecase/update_followup.dart'
    as _i95;
import '../../src/companion/followup/presentation/blocs/create_followup/create_followup_cubit.dart'
    as _i125;
import '../../src/companion/followup/presentation/blocs/update_followup/update_followup_cubit.dart'
    as _i114;
import '../../src/companion/relapse/data/remote/relapse_remote_datasource.dart'
    as _i26;
import '../../src/companion/relapse/data/repository/relapse_repository_impl.dart'
    as _i41;
import '../../src/companion/relapse/domain/repository/relapse_repository.dart'
    as _i40;
import '../../src/companion/relapse/domain/usecase/create_relapse.dart' as _i75;
import '../../src/companion/relapse/domain/usecase/delete_relapse.dart' as _i74;
import '../../src/companion/relapse/domain/usecase/get_relapse.dart' as _i72;
import '../../src/companion/relapse/domain/usecase/update_relapse.dart' as _i73;
import '../../src/manager/health_center/data/remote/health_center_remote_datasource.dart'
    as _i24;
import '../../src/manager/health_center/data/repository/health_center_repository_impl.dart'
    as _i39;
import '../../src/manager/health_center/domain/repository/health_center_repository.dart'
    as _i38;
import '../../src/manager/health_center/domain/usecase/get_health_centers.dart'
    as _i51;
import '../../src/manager/health_center/presentation/blocs/health_center/health_center_cubit.dart'
    as _i69;
import '../../src/manager/health_center/presentation/blocs/health_center_drawer/health_center_drawer_cubit.dart'
    as _i3;
import '../../src/manager/instance/data/remote/instance_remote_datasource.dart'
    as _i19;
import '../../src/manager/instance/data/repository/instance_repository_impl.dart'
    as _i56;
import '../../src/manager/instance/domain/repository/instance_repository.dart'
    as _i55;
import '../../src/manager/instance/domain/usecase/create_instance.dart'
    as _i124;
import '../../src/manager/instance/domain/usecase/delete_instance.dart'
    as _i123;
import '../../src/manager/instance/domain/usecase/edit_instance.dart' as _i122;
import '../../src/manager/instance/domain/usecase/get_all_instance.dart'
    as _i70;
import '../../src/manager/instance/domain/usecase/get_instance_detail.dart'
    as _i71;
import '../../src/manager/instance/domain/usecase/get_instances.dart' as _i129;
import '../../src/manager/instance/domain/usecase/get_instances_relapsed.dart'
    as _i130;
import '../../src/manager/instance/presentation/blocs/all_instances/all_instances_bloc.dart'
    as _i140;
import '../../src/manager/instance/presentation/blocs/create_instance/create_instance_cubit.dart'
    as _i151;
import '../../src/manager/instance/presentation/blocs/delete_instance/delete_instance_cubit.dart'
    as _i128;
import '../../src/manager/instance/presentation/blocs/edit_instance/edit_instance_cubit.dart'
    as _i146;
import '../../src/manager/instance_features/companion/data/remote/companion_remote_datasource.dart'
    as _i65;
import '../../src/manager/instance_features/companion/data/repository/companion_repository_impl.dart'
    as _i117;
import '../../src/manager/instance_features/companion/domain/repository/companion_repository.dart'
    as _i116;
import '../../src/manager/instance_features/companion/domain/usecase/create_companion.dart'
    as _i119;
import '../../src/manager/instance_features/companion/domain/usecase/delete_companion.dart'
    as _i121;
import '../../src/manager/instance_features/companion/domain/usecase/get_companion_list.dart'
    as _i120;
import '../../src/manager/instance_features/diagnostic/data/remote/diagnostic_remote_datasource.dart'
    as _i18;
import '../../src/manager/instance_features/diagnostic/data/repository/diagnostic_repository_impl.dart'
    as _i32;
import '../../src/manager/instance_features/diagnostic/domain/repository/diagnostic_repository.dart'
    as _i31;
import '../../src/manager/instance_features/diagnostic/domain/usecase/create_diagnostic.dart'
    as _i76;
import '../../src/manager/instance_features/diagnostic/domain/usecase/delete_diagnostic.dart'
    as _i64;
import '../../src/manager/instance_features/diagnostic/domain/usecase/edit_diagnostic.dart'
    as _i87;
import '../../src/manager/instance_features/diagnostic/domain/usecase/get_diagnostics.dart'
    as _i88;
import '../../src/manager/instance_features/followup/data/remote/followup_remote_datasource.dart'
    as _i21;
import '../../src/manager/instance_features/followup/data/repository/followup_repository_impl.dart'
    as _i36;
import '../../src/manager/instance_features/followup/domain/repository/followup_repository.dart'
    as _i35;
import '../../src/manager/instance_features/followup/domain/usecase/delete_followup.dart'
    as _i37;
import '../../src/manager/instance_features/followup/domain/usecase/get_followup.dart'
    as _i59;
import '../../src/manager/instance_features/followup/domain/usecase/get_followup_list.dart'
    as _i60;
import '../../src/manager/instance_features/relapse/data/remote/relapse_remote_datasource.dart'
    as _i54;
import '../../src/manager/instance_features/relapse/data/repository/relapse_repository_impl.dart'
    as _i86;
import '../../src/manager/instance_features/relapse/domain/repository/relapse_repository.dart'
    as _i85;
import '../../src/manager/instance_features/relapse/domain/usecase/create_relapse.dart'
    as _i92;
import '../../src/manager/instance_features/relapse/domain/usecase/delete_relapse.dart'
    as _i91;
import '../../src/manager/instance_features/relapse/domain/usecase/get_relapse.dart'
    as _i89;
import '../../src/manager/instance_features/relapse/domain/usecase/update_relapse.dart'
    as _i90;
import '../../src/manager/instance_features/treatment/data/remote/treatment_remote_datasource.dart'
    as _i57;
import '../../src/manager/instance_features/treatment/data/repository/treatment_repository_impl.dart'
    as _i100;
import '../../src/manager/instance_features/treatment/domain/repository/treatment_repository.dart'
    as _i99;
import '../../src/manager/instance_features/treatment/domain/usecase/create_treatment.dart'
    as _i101;
import '../../src/manager/instance_features/treatment/domain/usecase/delete_treatment.dart'
    as _i148;
import '../../src/manager/instance_features/treatment/domain/usecase/edit_treatment.dart'
    as _i102;
import '../../src/manager/instance_features/treatment/domain/usecase/get_treatment.dart'
    as _i147;
import '../../src/manager/instance_features/treatment/domain/usecase/get_treatment_list.dart'
    as _i149;
import '../../src/manager/member/data/remote/member_remote_datasource.dart'
    as _i28;
import '../../src/manager/member/data/repository/member_repository_impl.dart'
    as _i68;
import '../../src/manager/member/domain/repository/member_repository.dart'
    as _i67;
import '../../src/manager/member/domain/usecase/attach_member.dart' as _i138;
import '../../src/manager/member/domain/usecase/create_member.dart' as _i103;
import '../../src/manager/member/domain/usecase/delete_member.dart' as _i96;
import '../../src/manager/member/domain/usecase/edit_member.dart' as _i137;
import '../../src/manager/member/domain/usecase/get_members.dart' as _i104;
import '../../src/manager/member/domain/usecase/search_attach_member.dart'
    as _i136;
import '../../src/manager/member/presentation/blocs/attach_member/attach_member_cubit.dart'
    as _i143;
import '../../src/manager/member/presentation/blocs/create_member/create_member_cubit.dart'
    as _i141;
import '../../src/manager/member/presentation/blocs/edit_member/edit_member_cubit.dart'
    as _i144;
import '../../src/manager/questionnaire/data/remote/questionnaire_remote_datasource.dart'
    as _i25;
import '../../src/manager/questionnaire/data/repository/questionnaire_repository_impl.dart'
    as _i34;
import '../../src/manager/questionnaire/domain/repository/questionnaire_repository.dart'
    as _i33;
import '../../src/manager/questionnaire/domain/usecase/get_questionnaire_choices.dart'
    as _i80;
import '../../src/manager/questionnaire/domain/usecase/get_questionnaires.dart'
    as _i79;
import '../../src/manager/questionnaire/presentation/blocs/get_questionnaire_choices/get_questionnaire_choices_cubit.dart'
    as _i81;
import '../../src/manager/questionnaire/presentation/blocs/get_questionnaires/get_questionnaires_cubit.dart'
    as _i82;
import '../../src/user_notification/data/remote/user_notification_remote_datasource.dart'
    as _i20;
import '../../src/user_notification/data/repository/user_notification_repository_impl.dart'
    as _i23;
import '../../src/user_notification/domain/repository/user_notification_repository.dart'
    as _i22;
import '../../src/user_notification/domain/usecase/delete_all_notifications.dart'
    as _i43;
import '../../src/user_notification/domain/usecase/delete_notification.dart'
    as _i42;
import '../../src/user_notification/domain/usecase/get_all_notification.dart'
    as _i45;
import '../../src/user_notification/domain/usecase/get_unread_notifications.dart'
    as _i47;
import '../../src/user_notification/domain/usecase/mark_all_notifications_as_read.dart'
    as _i44;
import '../../src/user_notification/domain/usecase/mark_notification_as_read.dart'
    as _i46;
import '../../src/user_notification/presentation/blocs/delete_all_notifications/delete_all_notifications_cubit.dart'
    as _i52;
import '../../src/user_notification/presentation/blocs/delete_notification/delete_notification_cubit.dart'
    as _i66;
import '../../src/user_notification/presentation/blocs/get_all_notification/get_all_notification_bloc.dart'
    as _i105;
import '../../src/user_notification/presentation/blocs/get_unread_notifications/get_unread_notifications_bloc.dart'
    as _i53;
import '../../src/user_notification/presentation/blocs/mark_notification_as_read/mark_notification_as_read_cubit.dart'
    as _i93;
import '../http/dio/dio_register_module.dart' as _i154;
import '../network/connection_checker.dart' as _i10;
import '../objectbox/objectbox.dart' as _i7;
import '../objectbox/register_module.dart' as _i153;
import '../utils/device_info.dart' as _i13;
import 'register_module.dart' as _i152;

extension GetItInjectableX on _i1.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i1.GetIt> init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i2.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    final registerObjectBoxModule = _$RegisterObjectBoxModule();
    final dioRegisterModule = _$DioRegisterModule();
    gh.factory<_i3.HealthCenterDrawerCubit>(
        () => _i3.HealthCenterDrawerCubit());
    gh.factory<_i4.InlineFormCubit<dynamic>>(
        () => _i4.InlineFormCubit<dynamic>());
    gh.singleton<_i5.FlutterSecureStorage>(
        () => registerModule.createSecureStorage());
    gh.singleton<_i6.DeviceInfoPlugin>(() => registerModule.deviceInfoPlus());
    await gh.singletonAsync<_i7.ObjectBox>(
      () => registerObjectBoxModule.objectBox(),
      preResolve: true,
    );
    gh.lazySingleton<_i8.InternetConnection>(
        () => registerModule.internetConnect());
    gh.factory<_i9.AuthenticationLocalDataSource>(() =>
        _i9.AuthenticationLocalDataSourceImpl(objectBox: gh<_i7.ObjectBox>()));
    gh.lazySingleton<_i10.ConnectionChecker>(
        () => _i10.ConnectionCheckerImpl(gh<_i8.InternetConnection>()));
    gh.factory<_i11.TokenStorage>(
        () => _i11.TokenStorage(gh<_i5.FlutterSecureStorage>()));
    gh.factory<_i12.BaseOptions>(
      () => dioRegisterModule.dioOptions,
      instanceName: 'DioOptions',
    );
    await gh.singletonAsync<_i12.Dio>(
      () => dioRegisterModule.getUnauthorizedDioClient(
          gh<_i12.BaseOptions>(instanceName: 'DioOptions')),
      instanceName: 'Unauthorized',
      preResolve: true,
    );
    gh.factory<_i13.DeviceInfo>(
        () => _i13.DeviceInfoImpl(plugin: gh<_i6.DeviceInfoPlugin>()));
    gh.singleton<_i14.TokenRepository>(
        () => _i15.TokenRepositoryImpl(gh<_i11.TokenStorage>()));
    gh.factory<_i16.AdviceRemoteDataSource>(() =>
        _i16.AdviceRemoteDataSourceImpl(
            httpClient: gh<_i12.Dio>(instanceName: 'Unauthorized')));
    await gh.singletonAsync<_i12.Dio>(
      () => dioRegisterModule.getAuthorizedDioClient(
        gh<_i12.BaseOptions>(instanceName: 'DioOptions'),
        gh<_i17.TokenRepository>(),
      ),
      preResolve: true,
    );
    gh.factory<_i18.DiagnosticRemoteDataSource>(
        () => _i18.DiagnosticRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i19.InstanceRemoteDataSource>(
        () => _i19.InstanceRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i20.UserNotificationRemoteDataSource>(
        () => _i20.UserNotificationRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i21.FollowupRemoteDataSource>(
        () => _i21.FollowupRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i22.UserNotificationRepository>(() =>
        _i23.UserNotificationRepositoryImpl(
            gh<_i20.UserNotificationRemoteDataSource>()));
    gh.factory<_i24.HealthCenterRemoteDataSource>(
        () => _i24.HealthCenterRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i25.QuestionnaireRemoteDataSource>(
        () => _i25.QuestionnaireRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i26.RelapseRemoteDataSource>(
        () => _i26.RelapseRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i27.FcmTokenRemoteDataSource>(
        () => _i27.FcmTokenRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i28.MemberRemoteDataSource>(
        () => _i28.MemberRemoteDataSourceImpl(httpClient: gh<_i12.Dio>()));
    gh.factory<_i29.AdviceRepository>(() => _i30.AdviceRepositoryImpl(
        remoteDataSource: gh<_i16.AdviceRemoteDataSource>()));
    gh.factory<_i31.DiagnosticRepository>(() =>
        _i32.DiagnosticRepositoryImpl(gh<_i18.DiagnosticRemoteDataSource>()));
    gh.factory<_i33.QuestionnaireRepository>(() =>
        _i34.QuestionnaireRepositoryImpl(
            gh<_i25.QuestionnaireRemoteDataSource>()));
    gh.factory<_i35.FollowupRepository>(
        () => _i36.FollowupRepositoryImpl(gh<_i21.FollowupRemoteDataSource>()));
    gh.factory<_i37.DeleteFollowup>(
        () => _i37.DeleteFollowup(gh<_i35.FollowupRepository>()));
    gh.factory<_i38.HealthCenterRepository>(() =>
        _i39.HealthCenterRepositoryImpl(
            gh<_i24.HealthCenterRemoteDataSource>()));
    gh.factory<_i40.RelapseRepository>(
        () => _i41.RelapseRepositoryImpl(gh<_i26.RelapseRemoteDataSource>()));
    gh.factory<_i42.DeleteNotification>(
        () => _i42.DeleteNotification(gh<_i22.UserNotificationRepository>()));
    gh.factory<_i43.DeleteAllNotifications>(() =>
        _i43.DeleteAllNotifications(gh<_i22.UserNotificationRepository>()));
    gh.factory<_i44.MarkAllNotificationsAsRead>(() =>
        _i44.MarkAllNotificationsAsRead(gh<_i22.UserNotificationRepository>()));
    gh.factory<_i45.GetAllNotification>(
        () => _i45.GetAllNotification(gh<_i22.UserNotificationRepository>()));
    gh.factory<_i46.MarkNotificationAsRead>(() =>
        _i46.MarkNotificationAsRead(gh<_i22.UserNotificationRepository>()));
    gh.factory<_i47.GetUnreadNotifications>(() =>
        _i47.GetUnreadNotifications(gh<_i22.UserNotificationRepository>()));
    gh.factory<_i48.FcmTokenRepository>(
        () => _i49.FcmTokenRepositoryImpl(gh<_i27.FcmTokenRemoteDataSource>()));
    gh.factory<_i50.AuthenticationRemoteDataSource>(
        () => _i50.AuthenticationRemoteDataSourceImpl(
              unauthorizedClient: gh<_i12.Dio>(instanceName: 'Unauthorized'),
              authorizedClient: gh<_i12.Dio>(),
              deviceInfo: gh<_i13.DeviceInfo>(),
            ));
    gh.factory<_i51.GetHealthCenters>(
        () => _i51.GetHealthCenters(gh<_i38.HealthCenterRepository>()));
    gh.factory<_i52.DeleteAllNotificationsCubit>(() =>
        _i52.DeleteAllNotificationsCubit(gh<_i43.DeleteAllNotifications>()));
    gh.factory<_i53.GetUnreadNotificationsBloc>(() =>
        _i53.GetUnreadNotificationsBloc(gh<_i47.GetUnreadNotifications>()));
    gh.factory<_i54.RelapseRemoteDataSource>(
        () => _i54.RelapseRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i55.InstanceRepository>(() => _i56.InstanceRepositoryImpl(
        remoteDataSource: gh<_i19.InstanceRemoteDataSource>()));
    gh.factory<_i57.TreatmentRemoteDataSource>(
        () => _i57.TreatmentRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i58.CompanionRemoteDataSource>(
        () => _i58.CompanionRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i59.GetFollowup>(
        () => _i59.GetFollowup(repository: gh<_i35.FollowupRepository>()));
    gh.factory<_i60.GetFollowupList>(
        () => _i60.GetFollowupList(repository: gh<_i35.FollowupRepository>()));
    gh.factory<_i61.FollowupRemoteDataSource>(
        () => _i61.FollowupRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i62.FollowupRepository>(
        () => _i63.FollowupRepositoryImpl(gh<_i61.FollowupRemoteDataSource>()));
    gh.factory<_i64.DeleteDiagnostic>(
        () => _i64.DeleteDiagnostic(gh<_i31.DiagnosticRepository>()));
    gh.factory<_i65.CompanionRemoteDataSource>(
        () => _i65.CompanionRemoteDataSourceImpl(gh<_i12.Dio>()));
    gh.factory<_i66.DeleteNotificationCubit>(
        () => _i66.DeleteNotificationCubit(gh<_i42.DeleteNotification>()));
    gh.factory<_i67.MemberRepository>(
        () => _i68.MemberRepositoryImpl(gh<_i28.MemberRemoteDataSource>()));
    gh.factory<_i69.HealthCenterCubit>(
        () => _i69.HealthCenterCubit(gh<_i51.GetHealthCenters>()));
    gh.factory<_i70.GetAllInstance>(
        () => _i70.GetAllInstance(gh<_i55.InstanceRepository>()));
    gh.factory<_i71.GetInstanceDetail>(
        () => _i71.GetInstanceDetail(gh<_i55.InstanceRepository>()));
    gh.factory<_i72.GetRelapse>(
        () => _i72.GetRelapse(repository: gh<_i40.RelapseRepository>()));
    gh.factory<_i73.UpdateRelapse>(
        () => _i73.UpdateRelapse(repository: gh<_i40.RelapseRepository>()));
    gh.factory<_i74.DeleteRelapse>(
        () => _i74.DeleteRelapse(repository: gh<_i40.RelapseRepository>()));
    gh.factory<_i75.CreateRelapse>(
        () => _i75.CreateRelapse(repository: gh<_i40.RelapseRepository>()));
    gh.factory<_i76.CreateDiagnostic>(
        () => _i76.CreateDiagnostic(gh<_i31.DiagnosticRepository>()));
    gh.factory<_i77.AuthenticationRepository>(
        () => _i78.AuthenticationRepositoryImpl(
              tokenRepository: gh<_i17.TokenRepository>(),
              fcmTokenRepository: gh<_i48.FcmTokenRepository>(),
              remoteDataSource: gh<_i50.AuthenticationRemoteDataSource>(),
              localDataSource: gh<_i9.AuthenticationLocalDataSource>(),
            ));
    gh.factory<_i79.GetQuestionnaires>(
        () => _i79.GetQuestionnaires(gh<_i33.QuestionnaireRepository>()));
    gh.factory<_i80.GetQuestionnaireChoices>(
        () => _i80.GetQuestionnaireChoices(gh<_i33.QuestionnaireRepository>()));
    gh.factory<_i81.GetQuestionnaireChoicesCubit>(() =>
        _i81.GetQuestionnaireChoicesCubit(gh<_i80.GetQuestionnaireChoices>()));
    gh.factory<_i82.GetQuestionnairesCubit>(
        () => _i82.GetQuestionnairesCubit(gh<_i79.GetQuestionnaires>()));
    gh.factory<_i83.GetAdviceDetail>(() =>
        _i83.GetAdviceDetail(adviceRepository: gh<_i29.AdviceRepository>()));
    gh.factory<_i84.GetAdvices>(
        () => _i84.GetAdvices(adviceRepository: gh<_i29.AdviceRepository>()));
    gh.factory<_i85.RelapseRepository>(
        () => _i86.RelapseRepositoryImpl(gh<_i54.RelapseRemoteDataSource>()));
    gh.factory<_i87.EditDiagnostic>(
        () => _i87.EditDiagnostic(repository: gh<_i31.DiagnosticRepository>()));
    gh.factory<_i88.GetDiagnostics>(
        () => _i88.GetDiagnostics(repository: gh<_i31.DiagnosticRepository>()));
    gh.factory<_i89.GetRelapse>(
        () => _i89.GetRelapse(repository: gh<_i85.RelapseRepository>()));
    gh.factory<_i90.UpdateRelapse>(
        () => _i90.UpdateRelapse(repository: gh<_i85.RelapseRepository>()));
    gh.factory<_i91.DeleteRelapse>(
        () => _i91.DeleteRelapse(repository: gh<_i85.RelapseRepository>()));
    gh.factory<_i92.CreateRelapse>(
        () => _i92.CreateRelapse(repository: gh<_i85.RelapseRepository>()));
    gh.factory<_i93.MarkNotificationAsReadCubit>(() =>
        _i93.MarkNotificationAsReadCubit(gh<_i46.MarkNotificationAsRead>()));
    gh.factory<_i94.CreateFollowup>(
        () => _i94.CreateFollowup(gh<_i62.FollowupRepository>()));
    gh.factory<_i95.UpdateFollowup>(
        () => _i95.UpdateFollowup(gh<_i62.FollowupRepository>()));
    gh.factory<_i96.DeleteMember>(
        () => _i96.DeleteMember(memberRepository: gh<_i67.MemberRepository>()));
    gh.factory<_i97.GetFollowup>(
        () => _i97.GetFollowup(repository: gh<_i62.FollowupRepository>()));
    gh.factory<_i98.GetFollowupList>(
        () => _i98.GetFollowupList(repository: gh<_i62.FollowupRepository>()));
    gh.factory<_i99.TreatmentRepository>(() =>
        _i100.TreatmentRepositoryImpl(gh<_i57.TreatmentRemoteDataSource>()));
    gh.factory<_i101.CreateTreatment>(
        () => _i101.CreateTreatment(gh<_i99.TreatmentRepository>()));
    gh.factory<_i102.EditTreatment>(
        () => _i102.EditTreatment(gh<_i99.TreatmentRepository>()));
    gh.factory<_i103.CreateMember>(
        () => _i103.CreateMember(gh<_i67.MemberRepository>()));
    gh.factory<_i104.GetMembers>(
        () => _i104.GetMembers(gh<_i67.MemberRepository>()));
    gh.factory<_i105.GetAllNotificationBloc>(
        () => _i105.GetAllNotificationBloc(gh<_i45.GetAllNotification>()));
    gh.factory<_i106.CompanionRepository>(() =>
        _i107.CompanionRepositoryImpl(gh<_i58.CompanionRemoteDataSource>()));
    gh.factory<_i108.UserResetPassword>(
        () => _i108.UserResetPassword(gh<_i77.AuthenticationRepository>()));
    gh.factory<_i109.UserSignOut>(
        () => _i109.UserSignOut(gh<_i77.AuthenticationRepository>()));
    gh.factory<_i110.UserLogin>(
        () => _i110.UserLogin(gh<_i77.AuthenticationRepository>()));
    gh.factory<_i111.UserForgotPassword>(
        () => _i111.UserForgotPassword(gh<_i77.AuthenticationRepository>()));
    gh.factory<_i112.GetUser>(
        () => _i112.GetUser(gh<_i77.AuthenticationRepository>()));
    gh.factory<_i113.ResetPasswordCubit>(
        () => _i113.ResetPasswordCubit(gh<_i108.UserResetPassword>()));
    gh.factory<_i114.UpdateFollowupCubit>(
        () => _i114.UpdateFollowupCubit(gh<_i95.UpdateFollowup>()));
    gh.factory<_i115.SetFcmToken>(
        () => _i115.SetFcmToken(gh<_i48.FcmTokenRepository>()));
    gh.factory<_i116.CompanionRepository>(() =>
        _i117.CompanionRepositoryImpl(gh<_i65.CompanionRemoteDataSource>()));
    gh.factory<_i118.DeleteFollowup>(
        () => _i118.DeleteFollowup(gh<_i62.FollowupRepository>()));
    gh.factory<_i119.CreateCompanion>(
        () => _i119.CreateCompanion(gh<_i116.CompanionRepository>()));
    gh.factory<_i120.GetCompanionList>(
        () => _i120.GetCompanionList(gh<_i116.CompanionRepository>()));
    gh.factory<_i121.DeleteCompanion>(
        () => _i121.DeleteCompanion(gh<_i116.CompanionRepository>()));
    gh.factory<_i122.EditInstance>(
        () => _i122.EditInstance(gh<_i55.InstanceRepository>()));
    gh.factory<_i123.DeleteInstance>(
        () => _i123.DeleteInstance(gh<_i55.InstanceRepository>()));
    gh.factory<_i124.CreateInstance>(
        () => _i124.CreateInstance(gh<_i55.InstanceRepository>()));
    gh.factory<_i125.CreateFollowupCubit>(
        () => _i125.CreateFollowupCubit(gh<_i94.CreateFollowup>()));
    gh.factory<_i126.GetCompanion>(
        () => _i126.GetCompanion(gh<_i106.CompanionRepository>()));
    gh.factory<_i127.GetCompanionList>(
        () => _i127.GetCompanionList(gh<_i106.CompanionRepository>()));
    gh.factory<_i128.DeleteInstanceCubit>(
        () => _i128.DeleteInstanceCubit(gh<_i123.DeleteInstance>()));
    gh.factory<_i129.GetInstances>(
        () => _i129.GetInstances(repository: gh<_i55.InstanceRepository>()));
    gh.factory<_i130.GetInstancesRelapsed>(() =>
        _i130.GetInstancesRelapsed(repository: gh<_i55.InstanceRepository>()));
    gh.factory<_i131.CompanionsListBloc>(
        () => _i131.CompanionsListBloc(gh<_i127.GetCompanionList>()));
    gh.factory<_i132.ForgotPasswordCubit>(
        () => _i132.ForgotPasswordCubit(gh<_i111.UserForgotPassword>()));
    gh.factory<_i133.LoginCubit>(() => _i133.LoginCubit(gh<_i110.UserLogin>()));
    gh.factory<_i134.AdvicesBloc>(
        () => _i134.AdvicesBloc(gh<_i135.GetAdvices>()));
    gh.factory<_i136.SearchAttachMember>(
        () => _i136.SearchAttachMember(gh<_i67.MemberRepository>()));
    gh.factory<_i137.EditMember>(
        () => _i137.EditMember(gh<_i67.MemberRepository>()));
    gh.factory<_i138.AttachMember>(
        () => _i138.AttachMember(gh<_i67.MemberRepository>()));
    gh.factory<_i139.GetAdviceDetailCubit>(
        () => _i139.GetAdviceDetailCubit(gh<_i83.GetAdviceDetail>()));
    gh.factory<_i140.AllInstancesBloc>(
        () => _i140.AllInstancesBloc(gh<_i70.GetAllInstance>()));
    gh.factory<_i141.CreateMemberCubit>(
        () => _i141.CreateMemberCubit(gh<_i103.CreateMember>()));
    gh.factory<_i142.SignOutCubit>(
        () => _i142.SignOutCubit(gh<_i17.UserSignOut>()));
    gh.factory<_i143.AttachMemberCubit>(
        () => _i143.AttachMemberCubit(gh<_i138.AttachMember>()));
    gh.factory<_i144.EditMemberCubit>(
        () => _i144.EditMemberCubit(gh<_i137.EditMember>()));
    gh.factory<_i145.CompanionShowCubit>(() => _i145.CompanionShowCubit(
          gh<String>(),
          getCompanionUseCase: gh<_i126.GetCompanion>(),
        ));
    gh.factory<_i146.EditInstanceCubit>(
        () => _i146.EditInstanceCubit(gh<_i122.EditInstance>()));
    gh.factory<_i147.GetTreatment>(
        () => _i147.GetTreatment(gh<_i99.TreatmentRepository>()));
    gh.factory<_i148.DeleteTreatment>(
        () => _i148.DeleteTreatment(gh<_i99.TreatmentRepository>()));
    gh.factory<_i149.GetTreatmentList>(
        () => _i149.GetTreatmentList(gh<_i99.TreatmentRepository>()));
    gh.singleton<_i150.AuthenticatedCubit>(
        () => _i150.AuthenticatedCubit(gh<_i17.GetUser>()));
    gh.factory<_i151.CreateInstanceCubit>(
        () => _i151.CreateInstanceCubit(gh<_i124.CreateInstance>()));
    return this;
  }
}

class _$RegisterModule extends _i152.RegisterModule {}

class _$RegisterObjectBoxModule extends _i153.RegisterObjectBoxModule {}

class _$DioRegisterModule extends _i154.DioRegisterModule {}
