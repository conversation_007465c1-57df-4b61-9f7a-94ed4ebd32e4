// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDlPRVEX-nYSFYSlzHXQG8cIhGwVgdZuLY',
    appId: '1:143978279515:android:b4bdc38109995a3c98057e',
    messagingSenderId: '143978279515',
    projectId: 'innovation-fund-healthca-5e6cc',
    databaseURL:
        'https://innovation-fund-healthca-5e6cc-default-rtdb.firebaseio.com',
    storageBucket: 'innovation-fund-healthca-5e6cc.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDMUXwg7c-AKxoWGpynenG41Jj2N2uw_ds',
    appId: '1:143978279515:ios:d43636b9b14bda3d98057e',
    messagingSenderId: '143978279515',
    projectId: 'innovation-fund-healthca-5e6cc',
    databaseURL:
        'https://innovation-fund-healthca-5e6cc-default-rtdb.firebaseio.com',
    storageBucket: 'innovation-fund-healthca-5e6cc.appspot.com',
    iosBundleId: 'org.innovation.fund.healthcare',
  );
}
