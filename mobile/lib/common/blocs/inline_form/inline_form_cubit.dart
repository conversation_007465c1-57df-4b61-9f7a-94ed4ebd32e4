import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';

part 'inline_form_state.dart';

@injectable
class InlineFormCubit<Entity> extends Cubit<InlineFormState<Entity>> {
  InlineFormCubit() : super(InlineFormState<Entity>(false));

  void enableForm({Entity? editable}) {
    emit(InlineFormState<Entity>(true, editable: editable));
  }

  void disableForm() {
    emit(InlineFormState<Entity>(false));
  }
}
