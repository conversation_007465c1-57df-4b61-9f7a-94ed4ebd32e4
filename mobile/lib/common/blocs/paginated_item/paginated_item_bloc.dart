import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:stream_transform/stream_transform.dart';

part 'paginated_item_event.dart';
part 'paginated_item_state.dart';

EventTransformer<E> throttleDroppable<E>(Duration duration) {
  return (events, mapper) {
    return droppable<E>().call(events.throttle(duration), mapper);
  };
}

EventTransformer<Event> debounce<Event>(Duration duration) {
  return (events, mapper) => events.debounce(duration).switchMap(mapper);
}

abstract class PaginatedItemBloc<T>
    extends Bloc<PaginatedItemEvent, PaginatedItemState<T>> {
  PaginatedItemBloc() : super(PaginatedItemState<T>()) {
    on<PaginatedItemFetched>(
      _onUseCaseFetched,
      transformer: throttleDroppable(const Duration(milliseconds: 200)),
    );

    on<PaginatedItemSearch>(
      _onUseCaseSearch,
      transformer: debounce(const Duration(milliseconds: 300)),
    );
  }

  Future<Either<Failure, Paginated<T>>> callUseCase(PaginationParams params);

  Future<void> _onUseCaseSearch(
    PaginatedItemSearch event,
    Emitter<PaginatedItemState<T>> emit,
  ) async {
    // Got unexpected behavior with condition below.
    // if (state.loading) return;

    emit(state.copyWith(loading: true, status: PaginatedItemsStatus.initial));

    final paginatedItems = await callUseCase(
      PaginationParams(search: event.query),
    );

    paginatedItems.fold(
      (failure) {
        emit(state.copyWith(
          status: PaginatedItemsStatus.failure,
          error: failure.message,
          loading: false,
          items: [],
        ));
      },
      (paginated) {
        emit(state.copyWith(
          status: PaginatedItemsStatus.success,
          items: paginated.data,
          hasReachedMax: paginated.links.next == null,
          paginated: paginated,
          error: '',
          loading: false,
        ));
      },
    );
  }

  Future<void> _onUseCaseFetched(
    PaginatedItemFetched event,
    Emitter<PaginatedItemState<T>> emit,
  ) async {
    if ((state.hasReachedMax || state.loading) && !event.refresh) return;

    emit(state.copyWith(loading: true));

    if (state.status == PaginatedItemsStatus.initial || event.refresh) {
      final paginatedItems = await callUseCase(PaginationParams());

      paginatedItems.fold(
        (failure) {
          emit(state.copyWith(
            status: PaginatedItemsStatus.failure,
            error: failure.message,
            loading: false,
          ));
        },
        (paginated) {
          emit(state.copyWith(
            status: PaginatedItemsStatus.success,
            items: paginated.data,
            hasReachedMax: paginated.links.next == null,
            paginated: paginated,
            error: '',
            loading: false,
          ));
        },
      );

      return;
    }

    final paginatedItems = await callUseCase(
      PaginationParams(page: (state.paginated?.meta.currentPage ?? 0) + 1),
    );

    paginatedItems.fold(
      (failure) {
        emit(state.copyWith(
          status: PaginatedItemsStatus.failure,
          error: failure.message,
          loading: false,
        ));
      },
      (paginated) {
        emit(
          paginated.data.isEmpty
              ? state.copyWith(
                  status: PaginatedItemsStatus.success,
                  hasReachedMax: true,
                  loading: false,
                )
              : state.copyWith(
                  status: PaginatedItemsStatus.success,
                  items: List.of(state.items)..addAll(paginated.data),
                  hasReachedMax: paginated.links.next == null,
                  paginated: paginated,
                  error: '',
                  loading: false,
                ),
        );
      },
    );
  }
}
