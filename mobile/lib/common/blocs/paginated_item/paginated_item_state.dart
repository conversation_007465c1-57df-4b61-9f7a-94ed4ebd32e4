part of 'paginated_item_bloc.dart';

enum PaginatedItemsStatus { initial, success, failure }

@immutable
final class PaginatedItemState<T> extends Equatable {
  final PaginatedItemsStatus status;
  final List<T> items;
  final bool hasReachedMax;
  final Paginated<T>? paginated;
  final String? error;
  final bool loading;

  const PaginatedItemState({
    this.status = PaginatedItemsStatus.initial,
    this.hasReachedMax = false,
    this.items = const [],
    this.paginated,
    this.error,
    this.loading = false,
  });

  PaginatedItemState<T> copyWith({
    PaginatedItemsStatus? status,
    List<T>? items,
    bool? hasReachedMax,
    Paginated<T>? paginated,
    String? error,
    bool? loading,
  }) {
    return PaginatedItemState<T>(
      status: status ?? this.status,
      items: items ?? this.items,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      paginated: paginated ?? this.paginated,
      error: error ?? this.error,
      loading: loading ?? this.loading,
    );
  }

  @override
  String toString() {
    return '''PostState { status: $status, hasReachedMax: $hasReachedMax, posts: ${items.length} }''';
  }

  int get itemCountWithLoader => loading ? items.length + 1 : items.length;

  @override
  List<Object?> get props => [status, items, hasReachedMax, paginated];
}
