import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'refresh_data_event.dart';
part 'refresh_data_state.dart';

class RefreshDataBloc extends Bloc<RefreshDataEvent, RefreshDataState> {
  RefreshDataBloc() : super(RefreshDataInitial()) {
    // Companions items
    on<RefreshDataCompanionsEvent>(
      (event, emit) => emit(RefreshDataCompanions()),
    );

    // Companion Followups
    on<RefreshDataCompanionFollowupsEvent>(
      (event, emit) => emit(RefreshDataCompanionFollowups()),
    );

    // Companion Followups
    on<RefreshDataHealthCenterInstancesEvent>(
      (event, emit) => emit(RefreshDataHealthCenterInstances()),
    );
  }
}
