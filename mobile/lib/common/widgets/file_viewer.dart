import 'dart:io';
import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:open_file/open_file.dart';
import 'package:s3g/common/common.dart';

class FileViewer extends StatefulWidget {
  final String url;
  final Widget? child;
  final Widget? loaderWidget;

  const FileViewer({
    super.key,
    required this.url,
    this.child,
    this.loaderWidget,
  });

  @override
  // ignore: library_private_types_in_public_api
  _FileViewerState createState() => _FileViewerState();
}

class _FileViewerState extends State<FileViewer> {
  bool _isLoading = false;

  Future<File?> _checkOrDownloadFile() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final filename = Uri.parse(widget.url).pathSegments.last;
      final tempPath = '${tempDir.path}/$filename';
      final file = File(tempPath);

      if (!await file.exists()) {
        setState(() {
          _isLoading = true;
        });
        // If file does not exist, download it
        final response = await http.get(Uri.parse(widget.url));
        if (response.statusCode == 200) {
          await file.writeAsBytes(response.bodyBytes);
        } else {
          throw Exception('Failed to load file');
        }
      }

      return file;
    } catch (e) {
      if (mounted) {
        showToast(
          context,
          type: ToastType.error,
          message: 'Erreur lors du chargement du fichier',
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }

    return null;
  }

  Future<void> _viewFile() async {
    final file = await _checkOrDownloadFile();

    if (file != null) {
      // print(file);
      await OpenFile.open(file.path);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.child != null) {
      return InkWell(
        onTap: _isLoading ? null : _viewFile,
        child:
            _isLoading ? widget.loaderWidget ?? const GFLoader() : widget.child,
      );
    }

    return Center(
      child: ElevatedButton(
        onPressed: _isLoading ? null : _viewFile,
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: _isLoading
              ? const GFLoader()
              : const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.file_open),
                    Text('Ouvrir le fichier'),
                  ],
                ),
        ),
      ),
    );
  }
}
