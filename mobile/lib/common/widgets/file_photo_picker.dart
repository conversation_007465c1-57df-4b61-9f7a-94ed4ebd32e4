// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:image_picker/image_picker.dart';
import 'package:s3g/core/constants/constants.dart';

class FilePhotoPicker extends StatefulWidget {
  final void Function(XFile? file) onFileChange;

  const FilePhotoPicker({
    super.key,
    required this.onFileChange,
  });

  @override
  State<FilePhotoPicker> createState() => _FilePhotoPickerState();
}

class _FilePhotoPickerState extends State<FilePhotoPicker> {
  XFile? _selectedFile;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (_selectedFile != null) ...[
          Container(
            padding: const EdgeInsets.all(8),
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _selectedFile!.name,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),

          columnSizedBox,

          // Close selected file
          ElevatedButton.icon(
            onPressed: () {
              widget.onFileChange(null);
              setState(() {
                _selectedFile = null;
              });
            },
            label: const Text("Fermer"),
            icon: const Icon(Icons.close),
          )
        ],

        // File Picker
        if (_selectedFile == null)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon Button
              GFIconButton(
                tooltip: "Photo",
                onPressed: _pickImageFromGallery,
                type: GFButtonType.outline,
                shape: GFIconButtonShape.circle,
                color: Colors.grey[600] ?? Colors.grey,
                icon: const Icon(Icons.photo),
              ),

              // Camera
              GFIconButton(
                tooltip: "Camera",
                onPressed: _pickImageFromCamera,
                shape: GFIconButtonShape.circle,
                type: GFButtonType.outline,
                color: Colors.grey[600] ?? Colors.grey,
                icon: const Icon(Icons.camera_alt),
              ),

              // File
              GFIconButton(
                tooltip: "Fichier",
                onPressed: _pickFile,
                shape: GFIconButtonShape.circle,
                type: GFButtonType.outline,
                color: Colors.grey[600] ?? Colors.grey,
                icon: const Icon(Icons.file_present),
              ),
            ],
          ),

        columnSizedBox,
      ],
    );
  }

  Future _pickImageFromGallery() async {
    final image = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (image != null) {
      widget.onFileChange(image);

      setState(() {
        _selectedFile = image;
      });
    }
  }

  Future _pickImageFromCamera() async {
    final image = await ImagePicker().pickImage(source: ImageSource.camera);
    if (image != null) {
      widget.onFileChange(image);

      setState(() {
        _selectedFile = image;
      });
    }
  }

  Future _pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: [
        'pdf',
        'jpeg',
        'png',
        'webp',
        'svg',
        'bmp',
        'xml',
        'txt',
        'csv',
        'doc',
        'docx',
        'rtf',
        'xls',
        'xlsx',
        'xlsm',
        'ppt',
        'pptx',
        'mp4',
        'webm',
        'ogg',
      ],
    );

    if (result != null) {
      XFile file = result.xFiles.first;
      widget.onFileChange(file);

      setState(() {
        _selectedFile = file;
      });
    }
  }
}
