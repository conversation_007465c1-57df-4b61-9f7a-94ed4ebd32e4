import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';

class FormButtons extends StatelessWidget {
  final bool? loading;
  final VoidCallback? onSubmit;
  final VoidCallback? onCancel;

  const FormButtons({super.key, this.loading, this.onSubmit, this.onCancel});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Save form button
        GFButton(
          size: GFSize.LARGE,
          color: loading == true
              ? Colors.grey
              : Theme.of(context).colorScheme.primary,
          type: GFButtonType.outline,
          onPressed: loading == true ? null : onSubmit,
          child: loading == true
              ? const GFLoader(size: GFSize.SMALL)
              : const Text("Enregistrer"),
        ),

        // Close form
        GFButton(
          size: GFSize.SMALL,
          color: Colors.black,
          type: GFButtonType.outline,
          onPressed: onCancel,
          child: const Text("Fermer"),
        ),
      ],
    );
  }
}
