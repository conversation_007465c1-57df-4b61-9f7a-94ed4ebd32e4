import 'package:flutter/material.dart';

class DetailTitle extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final String? titleText;

  final Widget? subtitle;
  final String? subtitleText;
  final void Function()? onTap;

  const DetailTitle({
    super.key,
    this.leading,
    this.title,
    this.titleText,
    this.onTap,
    this.subtitle,
    this.subtitleText,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: 16.0),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                title ??
                    Text(
                      titleText ?? "",
                      style: const TextStyle(fontSize: 18.0),
                    ),
                if (subtitle != null || subtitleText != null) ...[
                  const SizedBox(height: 5.0),
                  subtitle ??
                      Text(
                        subtitleText ?? "",
                        style: const TextStyle(
                          fontSize: 16.0,
                          color: Colors.grey,
                        ),
                      )
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
