import 'package:flutter/material.dart';
import 'package:s3g/core/constants/constants.dart';

class AppBarLeadingTitle extends StatelessWidget {
  final Color? color;
  final bool centered;

  const AppBarLeadingTitle({
    super.key,
    this.color,
    this.centered = true,
  });

  @override
  Widget build(BuildContext context) {
    final text = Text(
      AppConstants.appName,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: color,
      ),
    );
    return centered
        ? Center(
            child: text,
          )
        : text;
  }
}
