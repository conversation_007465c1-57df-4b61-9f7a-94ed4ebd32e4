import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class EmptyList extends StatelessWidget {
  final String? message;

  const EmptyList({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        // Intro
        const EmptySvg(),

        // Some space
        const SizedBox(height: 10),

        if (message != null) ...[
          Center(
            child: Text(
              message!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          )
        ]
      ],
    );
  }
}

class EmptySvg extends StatelessWidget {
  const EmptySvg({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 30),
      child: Center(
        child: SvgPicture.asset(
          "assets/empty_re_opql.svg",
          width: 150,
          height: 150,
        ),
      ),
    );
  }
}
