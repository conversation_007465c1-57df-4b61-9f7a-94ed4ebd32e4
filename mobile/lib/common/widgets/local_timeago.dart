import 'package:flutter/widgets.dart';
import 'package:timeago_flutter/timeago_flutter.dart';

class LocalTimeago extends StatelessWidget {
  const LocalTimeago({super.key, required this.date, required this.builder});

  final DateTime date;
  final Widget Function(BuildContext, String) builder;

  @override
  Widget build(BuildContext context) {
    return Timeago(
      date: date.toLocal(),
      locale: 'fr',
      builder: builder,
    );
  }
}
