import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/core/constants/constants.dart';

class RetryWidget extends StatelessWidget {
  const RetryWidget({
    super.key,
    this.onPressed,
    this.message,
    this.label,
    this.loading = false,
  });

  final Function()? onPressed;
  final String? message;
  final String? label;
  final bool loading;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: bodyPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 15),

          //  Message
          if (message != null)
            Center(
              child: Text(
                message!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Colors.red,
                    ),
              ),
            ),

          // Some space
          if (message != null) const SizedBox(height: 8.0),

          // Retry button
          if (loading)
            const GFLoader()
          else
            MaterialButton(
              onPressed: onPressed,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0),
              ),
              color: Colors.red,
              child: Text(
                label ?? 'Essayer à nouveau',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Colors.white,
                    ),
              ),
            ),
        ],
      ),
    );
  }
}
