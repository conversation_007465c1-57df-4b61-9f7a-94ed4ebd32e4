import 'package:flutter/material.dart';
import 'package:s3g/core/constants/constants.dart';

class PageTitle extends StatelessWidget {
  final String title;
  final String? subtitle;

  const PageTitle({super.key, required this.title, this.subtitle});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall?.apply(
                color: Colors.grey[700],
                fontWeightDelta: 1,
              ),
        ),
        if (subtitle != null)
          Text(
            subtitle!,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        columnSizedBox
      ],
    );
  }
}
