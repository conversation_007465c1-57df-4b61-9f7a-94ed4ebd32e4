import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/user_notification/user_notification.dart';

import 'initial_icon.dart';

class UserIcon extends StatelessWidget {
  const UserIcon({super.key});

  @override
  Widget build(BuildContext context) {
    final user = context.read<AuthenticatedCubit>().state.user!;

    return InitialIcon(
      onTap: () {
        context.push(AppRoute.user);
      },
      text: user.name,
    );
  }
}

class HelpIcon extends StatelessWidget {
  const HelpIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () {
        context.go(AppRoute.advice);
      },
      icon: const Icon(Icons.help),
    );
  }
}

class UserNotifications extends StatefulWidget {
  const UserNotifications({super.key});

  @override
  State<UserNotifications> createState() => _UserNotificationsState();
}

class _UserNotificationsState extends State<UserNotifications> {
  bool hasNotification = false;
  late StreamSubscription<RemoteMessage> onMessageSub;

  @override
  void initState() {
    onMessageSub = FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      setState(() {
        hasNotification = true;
      });
    });

    final getUnreadNotifications = getIt<GetUnreadNotifications>();

    getUnreadNotifications(PaginationParams()).then((result) {
      result.fold((_) {}, (success) {
        if (success.data.isNotEmpty) {
          setState(() {
            hasNotification = true;
          });
        }
      });
    });

    super.initState();
  }

  @override
  void dispose() {
    onMessageSub.cancel();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () {
        setState(() {
          hasNotification = false;
        });
        context.push(AppRoute.userNotifications);
      },
      icon: Stack(
        children: [
          const Center(child: Icon(Icons.notifications)),
          if (hasNotification)
            Positioned(
              top: 5,
              right: 0,
              child: Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
