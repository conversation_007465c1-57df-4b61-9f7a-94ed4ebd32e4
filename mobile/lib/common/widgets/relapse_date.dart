import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class RelapseDate extends StatelessWidget {
  final DateTime createdAt;

  const RelapseDate({super.key, required this.createdAt});

  @override
  Widget build(BuildContext context) {
    final date = DateFormat.yMMMd('fr').format(createdAt.toLocal());
    return Text(
      "Rechute: $date",
      style: TextStyle(
        fontSize: 12,
        color: Colors.red[300],
      ),
    );
  }
}
