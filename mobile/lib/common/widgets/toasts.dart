import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';

enum ToastType { success, error, warning, info, loading }

void showToast(
  BuildContext context, {
  required ToastType type,
  required String message,
  Duration duration = const Duration(seconds: 5),
}) {
  switch (type) {
    case ToastType.success:
      GFToast.showToast(
        message,
        context,
        toastDuration: duration.inSeconds,
        trailing: const Icon(
          Icons.check_circle,
          color: GFColors.SUCCESS,
        ),
      );
      break;

    case ToastType.error:
      GFToast.showToast(
        message,
        context,
        toastDuration: duration.inSeconds,
        trailing: const Icon(
          Icons.dangerous_outlined,
          color: GFColors.DANGER,
        ),
      );
      break;

    case ToastType.warning:
      GFToast.showToast(
        message,
        context,
        toastDuration: duration.inSeconds,
        trailing: const Icon(
          Icons.warning_outlined,
          color: GFColors.WARNING,
        ),
      );
      break;

    case ToastType.info:
      GFToast.showToast(
        message,
        context,
        toastDuration: duration.inSeconds,
        trailing: const Icon(
          Icons.info_outlined,
          color: GFColors.INFO,
        ),
      );
      break;

    case ToastType.loading:
      GFToast.showToast(
        message,
        context,
        toastDuration: duration.inSeconds,
        trailing: const GFLoader(size: GFSize.SMALL),
      );
      break;

    // ignore: unreachable_switch_default
    default:
      break;
  }
}
