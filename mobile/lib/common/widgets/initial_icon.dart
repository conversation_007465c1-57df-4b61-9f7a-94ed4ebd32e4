import 'package:flutter/material.dart';
import 'package:s3g/core/helpers/strings.dart';

class InitialIcon extends StatelessWidget {
  final Widget? child;
  final String? text;
  final void Function()? onTap;

  const InitialIcon({
    super.key,
    this.child,
    this.text,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.all(9),
        decoration: const BoxDecoration(
          color: Color.fromARGB(255, 46, 46, 48),
          shape: BoxShape.circle,
        ),
        child: child ??
            Text(
              getInitials(text?.toUpperCase() ?? ''),
              style: const TextStyle(
                fontSize: 15,
                color: Colors.white,
              ),
            ),
      ),
    );
  }
}
