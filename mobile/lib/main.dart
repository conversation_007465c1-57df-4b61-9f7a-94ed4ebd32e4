import 'package:dio/dio.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:form_builder_validators/localization/l10n.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/firebase_options.dart';
import 'package:s3g/routes/routes.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/authentication/presentation/bloc/sign_out/sign_out_cubit.dart';
import 'package:s3g/src/user_notification/user_notification.dart';
import 'package:timeago/timeago.dart' as timeago;

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  await configureDependencies();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  try {
    await FirebaseMessagingService().initNotifications();
  } catch (e) {
    debugPrint(e.toString());
  }

  await NotificationService.initialize();

  timeago.setLocaleMessages('fr', timeago.FrMessages());

  getIt<Dio>().transformer = BackgroundTransformer();

  FlutterNativeSplash.remove();

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<AuthenticatedCubit>(),
        ),
        BlocProvider(
          create: (context) => getIt<SignOutCubit>(),
        ),
        BlocProvider(
          create: (context) => RefreshDataBloc(),
        ),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    context.read<AuthenticatedCubit>().checkUserAuthenticated();
    super.initState();
  }

  @override
  void dispose() {
    context.read<AuthenticatedCubit>().dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: AppConstants.appName,
      locale: const Locale('fr'),
      localizationsDelegates: const [
        FormBuilderLocalizations.delegate,
        ...GlobalMaterialLocalizations.delegates,
        GlobalWidgetsLocalizations.delegate,
      ],
      supportedLocales: FormBuilderLocalizations.supportedLocales,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      builder: (context, child) => InitWithAuthentication(child: child),
      routerConfig: appRouter,
    );
  }
}
