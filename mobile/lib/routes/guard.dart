import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/authentication/authentication.dart';

FutureOr<String?> redirectGuard(BuildContext context, GoRouterState state) {
  final location = state.matchedLocation;

  if (location == "/") {
    return null;
  }

  final user = context.read<AuthenticatedCubit>().state.user;

  if (user == null && location.startsWith('/app')) {
    return "/";
  }

  return user != null ? authenticatedGuard(user, location) : null;
}

FutureOr<String?> authenticatedGuard(User user, String location) {
  final isManager = [UserRole.APS, UserRole.ROOT].contains(user.role);
  final isCompanion = user.role == UserRole.COMPANION;

  if (isManager && location.startsWith(AppRoute.companion)) {
    return AppRoute.manager;
  }

  if (isCompanion && location.startsWith(AppRoute.manager)) {
    return AppRoute.companion;
  }

  if (location.startsWith('/auth')) {
    return isManager ? AppRoute.manager : AppRoute.companion;
  }

  return null;
}
