import 'package:go_router/go_router.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/core/utils/router_refresh_stream.dart';
import 'package:s3g/routes/config.dart';
import 'package:s3g/routes/guard.dart';
import 'package:s3g/src/advice/advice.dart';
import 'package:s3g/src/companion/routes.dart';
import 'package:s3g/src/manager/manager.dart';

import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/user_notification/user_notification.dart';

final appRouter = GoRouter(
  initialLocation: AppRoute.advice,
  refreshListenable: GoRouterRefreshStream(getIt<AuthenticatedCubit>().stream),
  redirect: redirectGuard,
  navigatorKey: rootNavigatorKey,
  debugLogDiagnostics: true,
  routes: [
    // Advices route
    GoRoute(
      path: '/',
      name: AppRoute.advice,
      builder: (context, state) => const AdvicePage(),
    ),

    GoRoute(
      path: '/advices/:id',
      name: AppRoute.adviceDetail,
      builder: (context, state) => AdviceShowPage(
        adviceId: state.pathParameters['id']!,
      ),
    ),

    // Auth routes
    GoRoute(
      path: '/auth',
      redirect: (_, __) => null,
      routes: [
        GoRoute(
          path: 'login',
          name: AppRoute.login,
          builder: (context, state) => const LoginPage(),
        ),
        GoRoute(
          path: 'forgot-password',
          name: AppRoute.forgotPassword,
          builder: (context, state) => const ForgotPasswordPage(),
        ),
        GoRoute(
          path: 'reset-password',
          name: AppRoute.resetPassword,
          builder: (context, state) => const ResetPasswordPage(),
        ),
      ],
    ),

    // Apps route
    GoRoute(
      path: '/app',
      redirect: (_, __) => null,
      routes: [
        apsRoutes,
        companionRoutes,

        // User route
        GoRoute(
          path: 'user',
          name: AppRoute.user,
          builder: (context, state) => const UserDetailsPage(),
        ),

        // User Notification
        GoRoute(
          path: 'user/notifications',
          name: AppRoute.userNotifications,
          builder: (context, state) => const AllNotificationPage(),
        )
      ],
    ),
  ],
);
