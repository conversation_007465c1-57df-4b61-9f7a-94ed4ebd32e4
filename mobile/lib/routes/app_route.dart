class AppRoute {
  static const advice = '/';
  static const adviceDetail = '/advices/:id';

  // Auth
  static const login = '/auth/login';
  static const forgotPassword = '/auth/forgot-password';
  static const resetPassword = '/auth/reset-password';

  // manager
  static const manager = '/app/manager';

  static const managerHealthCenterDetail = '/app/manager/health-center/detail';

  static const managerHealthCenterInstances =
      '/app/manager/health-center/instances';

  static const managerHealthCenterAPS = '/app/manager/health-centers/aps';

  static const managerHealthCenterCompanions =
      '/app/manager/health-centers/companions';

  // Instances
  static const managerInstance = "/app/manager/instances/:id";

  // Companion
  static const companion = '/app/companion';

  static const companionDetail = '/app/companion/:id/detail';

  static const companionFollowups = '/app/companion/:id/followups';

  static const companionRelapse = '/app/companion/:id/relapse';

  // user
  static const user = '/app/user';
  static const userNotifications = '/app/user/notifications';
}
