@php
$formattedDate = now()->isoFormat('D MMMM YYYY');
@endphp

<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport Détaillé {{ config("app.fullname") }}</title>
    <link
        href="https://fonts.googleapis.com/css2?family=Merriweather:wght@300;400;700&family=Source+Sans+Pro:wght@300;400;600&display=swap"
        rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#EF3A4F',
                    },
                    fontFamily: {
                        'serif': ['Merriweather', 'serif'],
                        'sans': ['Source Sans Pro', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Source Sans Pro', sans-serif;
        }

        h1,
        h2,
        h3 {
            font-family: 'Merriweather', serif;
        }
    </style>
</head>

<body class="text-gray-800 bg-white">
    <div class="max-w-full mx-auto">
        <header class="mb-12 border-b border-gray-300 pb-4">
            <h1 class="text-4xl font-bold text-gray-900">Rapport Détaillé</h1>
            <p class="text-xl text-primary font-semibold mt-2 font-sans">{{ config("app.fullname") }}</p>
            <p class="text-sm text-gray-600 mt-1 font-sans">Date du rapport : {{ $formattedDate }}</p>
        </header>

        <main>
            <section class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">1. Paramètres du Rapport</h2>
                <ul class="list-disc pl-5 text-gray-700 mb-4 font-sans">
                    <li>Type de cas : {{ $type === 'relapse' ? 'Rechutes' : 'Tous les cas' }}</li>
                    <li>Centres de santé : {{ $healthCenterNames ? implode(', ', $healthCenterNames) : 'Tous' }}</li>
                    <li>Date de début : {{ $startDate?->isoFormat('D MMMM YYYY') ?? 'Non spécifiée' }}</li>
                    <li>Date de fin : {{ $endDate?->isoFormat('D MMMM YYYY') ?? 'Non spécifiée' }}</li>
                </ul>
            </section>

            <section class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">2. Résumé des Centres de Santé</h2>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300 font-sans">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Centre de Santé") }}
                                </th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Zone de Santé") }}
                                </th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Nombre de cas") }}
                                </th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Nombre de rechutes") }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($healthCenters as $center)
                            <tr class="{{ $loop->even ? 'bg-gray-50' : '' }}">
                                <td class="border border-gray-300 px-4 py-2">{{ $center->name }}</td>
                                <td class="border border-gray-300 px-4 py-2">{{ $center->healthZone->name }}</td>
                                <td class="border border-gray-300 px-4 py-2">{{ $center->instances_count }}</td>
                                <td class="border border-gray-300 px-4 py-2">{{ $center->relapses_count }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">3. Liste Détaillée des Cas</h2>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300 font-sans text-sm">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("No.") }}
                                </th>
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("Province") }}
                                </th>
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("Zone") }}
                                </th>
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("Centre") }}
                                </th>
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("Code") }}
                                </th>
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("Date") }}
                                </th>
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("Statut") }}
                                </th>
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("Prises en charges") }}
                                </th>
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("Rechute") }}
                                </th>
                                <th class="border border-gray-300 px-2 py-2 font-semibold rotate-header">
                                    {{ __("Suivis") }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($cases as $case)
                            <tr class="{{ $loop->even ? 'bg-gray-50' : '' }}">
                                <td class="border border-gray-300 px-2 py-2">{{ $loop->iteration }}</td>
                                <td class="border border-gray-300 px-2 py-2">
                                    {{ $case->healthCenter->healthZone->province }}
                                </td>
                                <td class="border border-gray-300 px-2 py-2">
                                    {{ $case->healthCenter->healthZone->name }}
                                </td>
                                <td class="border border-gray-300 px-2 py-2">
                                    {{ $case->healthCenter->name }}
                                </td>
                                <td class="border border-gray-300 px-2 py-2">
                                    {{ $case->survivor->code }}
                                </td>
                                <td class="border border-gray-300 px-2 py-2">
                                    {{ $case->created_at->isoFormat('D MMMM YYYY') }}
                                </td>
                                <td class="border border-gray-300 px-2 py-2">
                                    {{ $case->status->getLabel() }}
                                </td>
                                <td class="border border-gray-300 px-2 py-2">
                                    {{ $case->treatmentsLabels() ?? '-' }}
                                </td>
                                <td class="border border-gray-300 px-2 py-2">
                                    {{ $case->relapse ? 'Oui' : 'Non' }}
                                </td>
                                <td class="border border-gray-300 px-2 py-2">
                                    {{ $case->followups()->count() }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">4. Conclusion</h2>
                <p class="text-gray-700 mb-4 font-sans leading-relaxed">
                    Ce rapport détaillé fournit une vue d'ensemble des cas enregistrés dans le système
                    {{ config("app.fullname") }}.
                    Il inclut une répartition par centre de santé ainsi qu'une liste détaillée de chaque cas.
                    Pour toute analyse supplémentaire ou information complémentaire, veuillez contacter l'équipe de
                    gestion des données.
                </p>
            </section>
        </main>

        <footer class="mt-12 pt-4 border-t border-gray-300 text-center text-gray-600 font-sans">
            <p>© {{ now()->format("Y") }} {{ config("app.fullname") }}. Tous droits réservés.</p>
        </footer>
    </div>
</body>

</html>