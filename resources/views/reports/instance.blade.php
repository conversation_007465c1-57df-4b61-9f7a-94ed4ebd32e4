@php
$formattedDate = now()->isoFormat('D MMMM YYYY');
@endphp

<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport de Cas - {{ $case->survivor->code }} - {{ config("app.fullname") }}</title>
    <link
        href="https://fonts.googleapis.com/css2?family=Merriweather:wght@300;400;700&family=Source+Sans+Pro:wght@300;400;600&display=swap"
        rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#EF3A4F',
                    },
                    fontFamily: {
                        'serif': ['Merriweather', 'serif'],
                        'sans': ['Source Sans Pro', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Source Sans Pro', sans-serif;
        }

        h1,
        h2,
        h3 {
            font-family: 'Merriweather', serif;
        }
    </style>
</head>

<body class="text-gray-800">
    <div class="max-w-4xl mx-auto">
        <header class="mb-8 border-b border-gray-300 pb-4">
            <h1 class="text-3xl font-bold">Rapport de Cas Détaillé</h1>
            <p class="text-xl font-semibold text-primary mt-2">{{ config("app.fullname") }}</p>
            <p class="text-sm text-gray-600 mt-1">Date du rapport : {{ $formattedDate }}</p>
        </header>

        <main>
            <section class="mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Informations Générales du Cas</h2>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p><strong>Code du Cas :</strong> {{ $case->code }}</p>
                        <p><strong>Code Survivant(e) :</strong> {{ $case->survivor->code }}</p>
                        <p><strong>Date d'ouverture :</strong> {{ $case->created_at->isoFormat('D MMMM YYYY') }}</p>
                        <p><strong>Type Final :</strong> {{ $case->type->getLabel() }}</p>
                        <p>
                            <strong>Statut :</strong>
                            <span
                                class="font-semibold {{ $case->status->value === 'OPEN' ? 'text-green-600' : 'text-blue-600' }}">
                                {{ $case->status->getLabel() }}
                            </span>
                        </p>
                        <p>
                            <strong>Cas de Rechute :</strong>
                            <span class="font-semibold {{ $case->relapse ? 'text-red-600' : 'text-green-600' }}">
                                {{ $case->relapse ? 'Signalé' : 'Non Signalé' }}
                            </span>
                        </p>
                    </div>
                    <div>
                        <p><strong>Province :</strong> {{ $case->healthCenter->healthZone->province }}</p>
                        <p><strong>Zone de Santé :</strong> {{ $case->healthCenter->healthZone->name }}</p>
                        <p><strong>Centre de Santé :</strong> {{ $case->healthCenter->name }}</p>
                        <p><strong>Créé par (APS) :</strong> {{ $case->creator?->user->name }}</p>
                    </div>
                </div>
            </section>


            <section class="mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Diagnostics</h2>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 px-4 py-2 text-left">Question</th>
                                <th class="border border-gray-300 px-4 py-2 text-left">Réponse</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($case->diagnostics as $diagnostic)
                            <tr>
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ $diagnostic->questionnaire->question }}
                                </td>

                                @if ($diagnostic->questionnaire->type->value === 'ATTACHMENT')
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ __("Pièce jointe") }}
                                </td>
                                @else
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ $diagnostic->response }}
                                </td>
                                @endif
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Traitements</h2>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 px-4 py-2 text-left">Type</th>
                                <th class="border border-gray-300 px-4 py-2 text-left">Observation</th>
                                <th class="border border-gray-300 px-4 py-2 text-left">Pièce Jointe</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($case->treatments as $treatment)
                            <tr>
                                <td class="border border-gray-300 px-4 py-2">{{ $treatment->type->getLabel() }}</td>
                                <td class="border border-gray-300 px-4 py-2">{{ $treatment->observation }}</td>
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ $treatment->attachment ? 'Oui' : 'Non' }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Suivis</h2>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 px-4 py-2 text-left">Titre</th>
                                <th class="border border-gray-300 px-4 py-2 text-left">Description</th>
                                <th class="border border-gray-300 px-4 py-2 text-left">Nom de l'Accompagnant</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($case->followups as $followup)
                            <tr>
                                <td class="border border-gray-300 px-4 py-2">{{ $followup->title }}</td>
                                <td class="border border-gray-300 px-4 py-2">{{ $followup->description }}</td>
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ $followup->companion?->user?->name }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Accompagnants</h2>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 px-4 py-2 text-left">Nom de l'Accompagnant</th>
                                <th class="border border-gray-300 px-4 py-2 text-left">Type</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($case->companions as $companion)
                            <tr>
                                <td class="border border-gray-300 px-4 py-2">{{ $companion->user->name }}</td>
                                <td class="border border-gray-300 px-4 py-2">{{ $companion->type->getLabel() }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Conclusion</h2>
                <p class="text-gray-700 leading-relaxed">
                    Ce rapport fournit un aperçu détaillé du cas {{ $case->survivor->code }}. Pour toute information
                    supplémentaire ou clarification, veuillez contacter l'équipe de gestion des cas.
                </p>
            </section>
        </main>

        <footer class="mt-12 pt-4 border-t border-gray-300 text-center text-gray-600">
            <p>© {{ now()->format("Y") }} {{ config("app.fullname") }}. Tous droits réservés.</p>
        </footer>
    </div>
</body>

</html>