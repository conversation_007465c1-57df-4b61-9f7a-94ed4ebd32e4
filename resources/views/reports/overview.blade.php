@php
$formattedDate = now()->isoFormat('D MMMM YYYY');
@endphp

<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport d'Aperçu {{ config("app.fullname") }}</title>
    <link
        href="https://fonts.googleapis.com/css2?family=Merriweather:wght@300;400;700&family=Source+Sans+Pro:wght@300;400;600&display=swap"
        rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#EF3A4F',
                    },
                    fontFamily: {
                        'serif': ['Merriweather', 'serif'],
                        'sans': ['Source Sans Pro', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Source Sans Pro', sans-serif;
        }

        h1,
        h2,
        h3 {
            font-family: 'Merriweather', serif;
        }
    </style>
</head>

<body class="text-gray-800 bg-white">
    <div class="max-w-4xl mx-auto">
        <header class="mb-12 border-b border-gray-300 pb-4">
            <h1 class="text-4xl font-bold text-gray-900">{{ __("Rapport d'Aperçu") }}</h1>
            <p class="text-xl text-primary font-semibold mt-2 font-sans">{{ config("app.fullname") }}</p>
            <p class="text-sm text-gray-600 mt-1 font-sans">Date du rapport : {{ $formattedDate }}</p>
        </header>

        <main>
            <section class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">1. {{ __("Résumé Exécutif") }}</h2>
                <p class="text-gray-700 mb-4 font-sans leading-relaxed">
                    Ce rapport fournit un aperçu des données collectées par l'application {{ config("app.fullname") }},
                    qui enregistre les cas de victimes dans différentes zones et centres de santé. Les statistiques clés
                    sont présentées ci-dessous :
                </p>
                <ul class="list-disc pl-5 text-gray-700 mb-4 font-sans">
                    <li>
                        {{ __("Nombre total de cas enregistrés") }} :
                        <span class="font-semibold">{{ number_format($instanceCount) }}</span>
                    </li>
                    <li>
                        {{ __("Nombre de cas de rechute") }} :
                        <span class="font-semibold">{{ number_format($relapseCount) }}</span>
                    </li>
                    <li>
                        {{ __("Nombre de zones de santé") }} :
                        <span class="font-semibold">{{ number_format($healthZoneCount) }}</span>
                    </li>
                    <li>
                        {{ __("Nombre de centres de santé") }} :
                        <span class="font-semibold">{{ number_format($healthCenterCount) }}</span>
                    </li>
                </ul>
            </section>

            <section class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">2. {{ __("Aperçu des Centres de Santé") }}</h2>
                <p class="text-gray-700 mb-4 font-sans leading-relaxed">
                    Le tableau suivant présente un aperçu détaillé des centres de santé, y compris le nombre de cas, le
                    personnel et les informations de localisation.
                </p>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300 font-sans">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Zone de Santé") }}
                                </th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Centre de Santé") }}
                                </th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Cas") }}
                                </th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Rechutes") }}
                                </th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Adresse") }}
                                </th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("APS") }}
                                </th>
                                <th class="border border-gray-300 px-4 py-2 text-left font-semibold">
                                    {{ __("Accompagnants") }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($healthCenters as $item)
                            <tr class="{{ $loop->even ? 'bg-gray-50' : '' }}">
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ $item->healthZone->name }}
                                </td>
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ $item->name }}
                                </td>
                                <td class="border border-gray-300 px-4 py-2 font-semibold">
                                    {{ $item->instances()->count() }}
                                </td>
                                <td class="border border-gray-300 px-4 py-2 font-semibold">
                                    {{ $item->relapses()->count() }}
                                </td>
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ $item->address }}
                                </td>
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ $item->aps()->count() }}
                                </td>
                                <td class="border border-gray-300 px-4 py-2">
                                    {{ $item->companions()->count() }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">3. {{ __("Conclusion") }}</h2>
                <p class="text-gray-700 mb-4 font-sans leading-relaxed">
                    Ce rapport fournit un aperçu des données collectées par {{ config("app.fullname") }}. Pour une
                    analyse plus approfondie ou des informations supplémentaires, veuillez contacter l'équipe de gestion
                    des données.
                </p>
            </section>
        </main>

        <footer class="mt-12 pt-4 border-t border-gray-300 text-center text-gray-600 font-sans">
            <p>© {{ now()->format("Y") }} {{ config("app.fullname") }}. Tous droits réservés.</p>
        </footer>
    </div>
</body>

</html>