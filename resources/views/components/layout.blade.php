<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ config('app.fullname') }}</title>
    <!-- Fonts -->
    <link rel="icon" href="/logo.png">
    <link rel="preconnect" href="https://fonts.bunny.net">
    {{--
    <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" /> --}}

    <style>
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-family: 'Prata', serif;
            --font-serif: 'Prata';
        }
    </style>
    <style>
        body {
            font-family: 'DM Serif Display', serif;
            --font-serif: 'DM Serif Display';
        }
    </style>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body>
    <div class="flex flex-col min-h-[100dvh] font-sans">
        <main class="flex-1">
            {{ $slot }}
        </main>

        <footer
            class="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center justify-center px-4 md:px-6 border-t">
            <p class="text-xs text-gray-500 text-center">© {{ now()->format('Y') }} {{ config("app.fullname") }}. Tous
                les droits
                réservés.</p>
        </footer>
    </div>
</body>

</html>