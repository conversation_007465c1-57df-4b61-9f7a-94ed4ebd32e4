<x-layout>
    <section class="w-full pt-12 md:pt-24 lg:pt-32 border-y mb-0 sm:pb-12 lg:pb-0">
        <div class="px-4 md:px-6 space-y-10 xl:space-y-16">
            <div class="grid max-w-[1300px] mx-auto gap-4 px-4 sm:px-6 md:px-10 md:grid-cols-2 md:gap-16">
                <div class="space-y-4">
                    <h1
                        class="lg:leading-tighter text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl xl:text-[3.4rem] 2xl:text-[3.75rem]">
                        Autonomiser les victimes, restaurer l'espoir
                    </h1>
                    <p class="max-w-[700px] text-gray-500 md:text-xl">
                        Notre ONG est dédiée à fournir un soutien et des ressources complets aux victimes de
                        violence, les
                        aidant à guérir et à reconstruire leur vie.
                    </p>
                </div>
                <div class="flex justify-center">
                    <x-logo class="w-full h-full md:-mt-10 lg:-mt-16 contrast-75" />
                </div>
            </div>
        </div>
    </section>

    <section class="w-full py-12 md:py-24 lg:py-32">
        <div class="container space-y-12 px-4 md:px-6 mx-auto">
            <div class="flex flex-col items-center justify-center space-y-4 text-center">
                <div class="space-y-4">
                    <h2 class="text-3xl font-bold tracking-tighter sm:text-5xl">Conseils utiles</h2>
                    <p class="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                        Explorez notre collection de conseils et de ressources informatives pour soutenir les
                        victimes de
                        violence.
                    </p>
                </div>
                <div class="mx-auto flex justify-center">
                    <div class="hs-dropdown relative inline-flex">
                        <button id="hs-dropdown-default" type="button"
                            class="hs-dropdown-toggle py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none "
                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                            {{ $lang?->name }}
                            <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg"
                                width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="m6 9 6 6 6-6" />
                            </svg>
                        </button>

                        <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-md rounded-lg p-1 space-y-0.5 mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full"
                            role="menu" aria-orientation="vertical" aria-labelledby="hs-dropdown-default">
                            @foreach ($languages as $item)
                            <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                                href="{{ url()->query('/', ['lang' => $item->name]) }}">
                                {{ $item->name }}
                            </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>



            <div
                class="mx-auto grid items-start gap-8 sm:max-w-4xl sm:grid-cols-1 md:gap-12 lg:max-w-5xl lg:grid-cols-1">
                <div class="grid gap-1 w-full">
                    <div class="flex items-center gap-2">
                        <svg data-id="33" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-6 w-6 text-[#EF3A4F]">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 16v-4"></path>
                            <path d="M12 8h.01"></path>
                        </svg>
                        <h3 class="text-lg font-bold">Conseils</h3>
                    </div>
                    <div class="space-y-2 grid gap-4">
                        @foreach ($advices as $advice)
                        <a href='{{ url()->query("/advices/{$advice->id}", ["lang" => $lang?->name]) }}'
                            class="text-sm text-gray-500 bg-gray-100 p-4 rounded-lg">
                            {{$advice->title}}
                        </a>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </section>
</x-layout>