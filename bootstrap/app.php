<?php

use App\Http\Middleware as AppMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'auth.aps' => AppMiddleware\EnsureUserIsAPS::class,
            'auth.root' => AppMiddleware\EnsureUserIsRoot::class,
            'auth.companion' => AppMiddleware\EnsureUserIsCompanion::class,
            'user.belongs-to-healthCenter' => AppMiddleware\EnsureUserBelongsToHealthCenter::class,
            'user.belongs-to-instance-healthCenter' => AppMiddleware\EnsureUserBelongsToInstanceHealthCenter::class,
            'user.belongs-to-companion-healthCenter' => AppMiddleware\EnsureUserBelongsToCompanionHealthCenter::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
